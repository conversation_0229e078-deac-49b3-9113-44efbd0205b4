package com.wunding.learn.project.api.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * <p>
 * 学习项目查询对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/3/14 15:03
 */
@Data
public class ProjectListQuery extends BasePageQuery {

    @Parameter(description = "被引用的ID")
    private List<String> referencedIdList;

    /**
     * 引用类型  0=默认值  1=培训项目
     */
    @Schema(description = "引用类型  0=默认值  1=培训项目")
    private Integer referencedType;

    @Parameter(description = "项目名称")
    @Length(max = 80, message = "项目名称长度不能超过80")
    private String proName;

    @Parameter(description = "是否发布,0-未发布 1-已发布")
    private Integer isPublish;

    @Parameter(description = "计划类型 0固定日期，1固定周期",hidden = true)
    private Integer type;

    @Parameter(description = "班级名称")
    private String className;

    @Parameter(description = "开始时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date startTime;

    @Parameter(description = "结束时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date endTime;

    @Parameter(description = "创建时间开始")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date createStartTime;

    @Parameter(description = "创建时间结束")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date createEndTime;
}
