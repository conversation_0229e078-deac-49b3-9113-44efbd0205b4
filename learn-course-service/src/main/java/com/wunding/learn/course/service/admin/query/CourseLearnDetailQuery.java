package com.wunding.learn.course.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 课程学习统计搜索条件
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/10  15:31
 */
@Data
@Schema(name = "CourseLearnDetailQuery", description = "课程学习统计查询对象")
public class CourseLearnDetailQuery extends BaseEntity {

    /**
     * 课程id
     */
    private String courseId;
}
