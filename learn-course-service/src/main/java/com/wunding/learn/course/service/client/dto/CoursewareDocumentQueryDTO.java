package com.wunding.learn.course.service.client.dto;

import com.wunding.learn.common.dto.lecturerworkbench.DocumentQueryBaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p> 查询课件资料dto对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-01-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "CoursewareDocumentQueryDTO", description = "查询课件资料dto对象")
public class CoursewareDocumentQueryDTO extends DocumentQueryBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 课件id
     */
    @Schema(description = "课件id", hidden = true)
    private String cwId;

    /**
     * 课件类型 Word-WORD,PPT-PPT,PDF-PDF,Video-视频,Audio-音频,Scorm-ZIP,Pic-ZIP,Text-图文
     */
    @Schema(description = "课件类型 Word-WORD,PPT-PPT,PDF-PDF,Video-视频,Audio-音频,Scorm-ZIP,Pic-ZIP,Text-图文")
    private String cwType;

    /**
     * 课件预览地址
     */
    @Schema(description = "mime类型")
    private String mime;

    /**
     * 文件时长
     */
    @Schema(description = "文件时长")
    private String playTime;

    /**
     * 视频转换状态：1-转换中 2-转换完成 3-转换失败
     */
    @Schema(description = "转换状态：1-转换中 2-转换完成 3-转换失败")
    private Integer transformStatus;

    /**
     * 课件预览地址
     */
    @Schema(description = "课件预览地址")
    private String previewUrl;

}
