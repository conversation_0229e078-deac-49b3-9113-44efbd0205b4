package com.wunding.learn.course.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 外部课程导入对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON><PERSON><PERSON></a>
 * @date 2023/8/8 9:48
 */
@Data
@Schema(description = "外部课程导入对象",name = "ImportCourseWithoutDTO")
public class ImportCourseWithoutDTO {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;

    /**
     * 外部课程来源
     */
    @Schema(description = "外部课程来源")
    private String sourceTypeName;

    /**
     * 外部课程来源id
     */
    @Schema(description = "外部课程来源id")
    private String sourceType;


    /**
     * 外部课程名称
     */
    @Schema(description = "外部课程名称")
    private String courseName;


    /**
     * 外部课程ID
     */
    @Schema(description = "外部课程ID")
    private String courseNo;


    /**
     * 讲师&作者
     */
    @Schema(description = "讲师&作者")
    private String author;


    /**
     * 课程介绍
     */
    @Schema(description = "课程介绍")
    private String courseRemark;


    /**
     * 讲师&作者介绍
     */
    @Schema(description = "讲师&作者介绍")
    private String authorRemark;




    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    private Integer viewType;


}
