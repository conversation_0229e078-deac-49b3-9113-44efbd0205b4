package com.wunding.learn.course.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * @Author: yanglequn
 * @Date: 2023/4/19 9:53
 */
@Data
@Schema(name = "CourseStudyStatDTO", description = "课程学习统计对象")
public class CourseStudyStatUserRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id", hidden = true)
    private String userId;

    @Schema(description = "用户名")
    private String fullName;

    @Schema(description = "账号")
    private String loginName;

    @Schema(description = "部门名称")
    private String orgName;

    @Schema(description = "部门全路径名")
    private String levelPathName;

}
