package com.wunding.learn.course.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CoursewareQuestionQueryDTO extends BaseEntity implements Serializable {
    
    @Parameter(description = "课件id" ,required = true)
    @NotBlank(message = "课件id不能为空")
    private String coursewareId;
}
