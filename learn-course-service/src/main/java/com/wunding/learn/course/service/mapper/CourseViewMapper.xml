<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.course.service.mapper.CourseViewMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.course.service.mapper.CourseViewMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.course.service.model.CourseView">
        <!--@Table course_view-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="course_id" jdbcType="VARCHAR"
          property="courseId"/>
        <result column="cw_id" jdbcType="VARCHAR"
          property="cwId"/>
        <result column="view_by" jdbcType="VARCHAR"
          property="viewBy"/>
        <result column="view_time" jdbcType="TIMESTAMP"
          property="viewTime"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, cw_id, view_by, view_time, org_id
    </sql>

    <select id="getIdpCourseWareStatistic" parameterType="com.wunding.learn.course.api.query.UserIdpStatisticQuery"
      resultType="int">
        select count(1)
        from course_view cv
        where cv.view_by = #{userId}
          and date_format(cv.view_time, '%Y') = #{year}
    </select>

    <select id="getViewCount" resultType="com.wunding.learn.course.api.dto.CountCourseViewDTO">
        select course_id,count(ur.view_by) as count from course_view ur where ur.course_id
        <foreach collection="resourceIds" item="item" open="in (" close=")"
          separator=",">
            #{item}
        </foreach>
        group by ur.course_id
    </select>
</mapper>
