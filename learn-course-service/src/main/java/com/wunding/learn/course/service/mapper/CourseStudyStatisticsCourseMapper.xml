<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.course.service.mapper.CourseStudyStatisticsCourseMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.course.service.mapper.CourseStudyStatisticsCourseMapper"/>

        <select id="getCourseStudyStatisticsCourseData"
                resultType="com.wunding.learn.course.service.admin.dto.CourseStatisticsCourseDTO" useCache="false">
            SELECT
                c.id as course_id,
                c.course_name,
                ct.category_name,
                c.create_by,
                (SELECT GROUP_CONCAT(b.category_name ORDER BY b.level_path SEPARATOR '/')
                 FROM categorys a
                 LEFT JOIN categorys b ON FIND_IN_SET( b.id, (REPLACE (SUBSTR(a.level_path ,2),'/',',')))
                 where a.id = ct.id
                )as category_level_name,
                IFNULL(( select sum(cw.play_time) from courseware cw where cw.is_del=0 and cw.course_id = c.ID),0) learn_finish_need_time,
                IFNULL(( SELECT count( cw.ID ) FROM courseware cw WHERE cw.course_id = c.ID ),0) total_cw,
                IFNULL((select round(avg(star_count), 1) from course_star cs where cs.course_id = c.id), 0) synthesize_star,
                IFNULL(( SELECT count( cd.ID ) FROM course_download cd WHERE cd.course_id = c.ID ),0) total_download
            FROM course c
                     LEFT JOIN categorys ct ON ct.id = c.course_cate_id
            where c.ID = #{courseId}

        </select>


        <select id="getViewLimitUserCount" resultType="java.lang.Integer" useCache="false">
            <if test="orgLevelPaths != null and orgLevelPaths.size()>0">
                with temp as(
                select su.id user_id
                from 	sys_user su
                INNER JOIN  sys_org so on su.org_id = so.id
                where 1=1
                and
                <foreach collection="orgLevelPaths" open="(" close=")" item="item" separator="or">
                    so.level_path like CONCAT(#{item},'%')
                </foreach>
                )
            </if>
            select count(DISTINCT u.user_id)
            from w_view_limit_user u
            INNER join  w_resource_view_limit r on  r.view_limit_id = u.view_limit_id

            <if test="orgLevelPaths !=null and orgLevelPaths.size()>0">
                INNER JOIN  temp on temp.user_id = u.user_id
            </if>
            where 1=1
            and r.resource_type = 'CourseViewLimit'
            and r.resource_id = #{resourceId}

        </select>
    <select id="getLearnNum" resultType="java.lang.Integer" useCache="false">
        <!-- 管辖范围 -->
        <if test="orgLevelPaths != null and orgLevelPaths.size()>0">
            with temp as(
            select su.id user_id
            from 	sys_user su
            INNER JOIN  sys_org so on su.org_id = so.id
            where 1=1
            and
            <foreach collection="orgLevelPaths" open="(" close=")" item="item" separator="or">
                so.level_path like CONCAT(#{item},'%')
            </foreach>
            )
        </if>
        SELECT count( DISTINCT ucr.user_id ) FROM user_course_record ucr
        <if test="orgLevelPaths !=null and orgLevelPaths.size()>0">
            INNER JOIN  temp on temp.user_id = ucr.user_id
        </if>
        WHERE ucr.is_del = 0
          AND is_learned = #{learnStatus}
          AND ucr.course_id = #{resourceId}
          AND EXISTS( select 1  from w_view_limit_user u
                inner join w_resource_view_limit r on u.view_limit_id = r.view_limit_id
                where r.resource_id = ucr.course_id and u.user_id = ucr.user_id
              )
    </select>

    <select id="getCourseStudyStatisticsCourseList"
            resultType="com.wunding.learn.course.service.admin.dto.CourseStatisticsCourseDTO">
        select c.id as course_id ,c.create_by
        from course c
        where c.is_del=0
        <!-- 默认获取我管辖的人创建的课程 -->
        <if test="managerAreaLevelPaths != null and managerAreaLevelPaths.size()>0">
            AND EXISTS(
            select su.id user_id
            from 	sys_user su
            INNER JOIN  sys_org so on su.org_id = so.id
            where 1=1
            and su.id = c.create_by
            and
            <foreach collection="managerAreaLevelPaths" open="(" close=")" item="item" separator="or">
                so.level_path like CONCAT(#{item},'%')
            </foreach>
            )
        </if>
        <if test="courseIdList != null and courseIdList.size()>0">
            and c.id in
            <foreach collection="courseIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getCourseStatisticsLearnDetail"
            resultType="com.wunding.learn.course.service.admin.dto.CourseStatisticsLearnedDTO" useCache="false">
        <!-- 管辖范围 -->
        <if test="params.managerAreaLevelPaths != null and params.managerAreaLevelPaths.size()>0">
            with temp as(
            select su.id user_id
            from 	sys_user su
            INNER JOIN  sys_org so on su.org_id = so.id
            where 1=1
            and
            <foreach collection="params.managerAreaLevelPaths" open="(" close=")" item="item" separator="or">
                so.level_path like CONCAT(#{item},'%')
            </foreach>
            )
        </if>
        <choose>

            <when test="params.type == 0 or params.type == 1 ">
                select DISTINCT ucr.user_id,
                temp1.min_view_time,temp1.max_view_time ,temp1.total_duration
                from user_course_record ucr
                <if test="params.managerAreaLevelPaths !=null and params.managerAreaLevelPaths.size()>0">
                    INNER JOIN  temp on temp.user_id = ucr.user_id
                </if>
                left join (
                    SELECT
                    cvd.user_id,
                    sum( cvd.DURATION ) total_duration,
                    min( cvd.start_time ) min_view_time,
                    max( cvd.end_time ) max_view_time
                    FROM
                    courseware_user_record cvd
                    where cvd.course_id = #{params.courseId}
                    GROUP BY
                    cvd.user_id
                ) temp1 on ucr.user_id = temp1.user_id
                where 1=1
                and ucr.is_del = 0
                and ucr.course_id = #{params.courseId}
                and is_learned = #{params.type}
                and exists( select 1 from w_view_limit_user u inner join w_resource_view_limit r on u.view_limit_id = r.view_limit_id where ucr.user_id = u.user_id and r.resource_id= ucr.course_id)
                <if test="params.userIdList !=null and params.userIdList.size()>0">
                and ucr.user_id in
                <foreach  collection="params.userIdList"  item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            </when>

            <when test="params.type == 2">
                select DISTINCT u.user_id
                from w_view_limit_user u
                inner join w_resource_view_limit r on u.view_limit_id = r.view_limit_id
                <if test="params.managerAreaLevelPaths !=null and params.managerAreaLevelPaths.size()>0">
                    INNER JOIN  temp on temp.user_id = u.user_id
                </if>
                where 1=1
                and r.resource_type = 'CourseViewLimit'
                and r.resource_id = #{params.courseId}

                <if test="params.userIdList !=null and params.userIdList.size()>0">
                    and u.user_id in
                    <foreach  collection="params.userIdList"  item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <!-- 没有学习记录的 -->
                and not EXISTS(
                select ucr.user_id
                from user_course_record ucr
                where is_del = 0 and u.user_id = ucr.user_id  and course_id = #{params.courseId}
                )

            </when>

            <when test="params.type == 3">
                select DISTINCT u.user_id
                from w_view_limit_user u
                inner join w_resource_view_limit r on u.view_limit_id = r.view_limit_id

                <if test="params.managerAreaLevelPaths !=null and params.managerAreaLevelPaths.size()>0">
                    INNER JOIN  temp on temp.user_id = u.user_id
                </if>
                where 1=1
                and r.resource_type = 'CourseViewLimit'
                and r.resource_id = #{params.courseId}
                <if test="params.userIdList !=null and params.userIdList.size()>0">
                    and u.user_id in
                    <foreach  collection="params.userIdList"  item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </when>
        </choose>
    </select>

    <select id="getUserByLevelPaths" resultType="java.lang.String" useCache="false">
        select su.id user_id
        from 	sys_user su
        INNER JOIN  sys_org so on su.org_id = so.id
        where 1=1
        <if test="createBys != null and createBys.size()>0">
            and su.id in
            <foreach collection="createBys" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        and
        <foreach collection="levelPaths" open="(" close=")" item="item" separator="or">
            so.level_path like CONCAT(#{item},'%')
        </foreach>
    </select>
</mapper>
