package com.wunding.learn.course.service.admin.query;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/5/20 13:30
 */

@Data
public class UserCourseTagQuery {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "添加方式 0-系统添加 1-用户自己添加 2-按访问量添加")
    private Integer createType;

    @Schema(description = "标签id集合")
    private List<String> tagIds;

}
