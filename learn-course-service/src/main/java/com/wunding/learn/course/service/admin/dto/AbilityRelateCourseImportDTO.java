package com.wunding.learn.course.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: chenjinneng
 * @create: 2023-12-29
 **/
@Data
@Accessors(chain = true)
@Schema(name = "AbilityRelateCourseImportDTO", description = "能力关联课程导入数据")
public class AbilityRelateCourseImportDTO {

    @Schema(description = "能力id")
    private String abilityId;

    @Schema(description = "能力编码")
    private String abilityCode;

    @Schema(description = "能力名称")
    private String abilityName;

    @Schema(description = "能力等级")
    private String abilityLevel;

    @Schema(description = "课程ID")
    private String courseId;

    @Schema(description = "课程编码")
    private String courseNo;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课时（分钟）")
    private double courseTime;

    @Schema(description = "来源方式，0-课程")
    private Integer isSource;
}
