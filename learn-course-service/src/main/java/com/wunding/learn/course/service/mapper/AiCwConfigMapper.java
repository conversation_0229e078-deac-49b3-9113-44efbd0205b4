package com.wunding.learn.course.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.course.service.model.AiCwConfig;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;


/**
 * <AUTHOR>
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface AiCwConfigMapper extends BaseMapper<AiCwConfig> {

    /**
     * 清除配置
     *
     * @param cwId 课件id
     */
    void deleteByCwId(@Param("cwId") String cwId);
}
