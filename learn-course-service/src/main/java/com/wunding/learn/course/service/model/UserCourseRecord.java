package com.wunding.learn.course.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * </p> 用户课程学习记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_course_record")
@Schema(name = "UserCourseRecord对象", description = "用户课程学习记录")
public class UserCourseRecord implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 记录id
     */
    @Schema(description = "记录id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableField("user_id")
    private String userId;


    /**
     * 用户组织全路径
     */
    @Schema(description = "用户组织全路径")
    @TableField("level_path")
    private String levelPath;

    /**
     * 课程id
     */
    @Schema(description = "课程id")
    @TableField("course_id")
    private String courseId;


    /**
     * 已学标记，1已学
     */
    @Schema(description = "已学标记，1已学")
    @TableField("is_learned")
    private Integer isLearned;


    /**
     * 课程学习的时长
     */
    @Schema(description = "课程学习的时长")
    @TableField("duration")
    private Long duration;


    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    @TableField("finish_time")
    private Date finishTime;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


}
