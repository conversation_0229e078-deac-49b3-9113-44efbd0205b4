package com.wunding.learn.common.viewlimit.constant;

/**
 * 推送的范围有五种可能性： 1.只推送部门； 2.只推送用户； 3.推送部门和身份； 4.推送部门和用户; 5.推送部门、身份、用户
 *
 * <AUTHOR> href="mailto:p.z<PERSON>@wunding.com"><PERSON><PERSON><PERSON></a>
 * @since [版本号]
 */
public enum PushAreaEnum {

    /**
     * 只推送部门
     */
    ORGAN,
    /**
     * 只推送用户
     */
    USER,
    /**
     * 推送部门和身份
     */
    ORGAN_IDENTITY,
    /**
     * 推送部门和用户
     */
    ORGAN_USER,
    /**
     * 推送部门、身份、用户
     */
    ORGAN_IDENTITY_USER;

    public static PushAreaEnum getPushArea(String[] push) {
        boolean hasOrgan = !"".equals(push[0]);
        boolean hasUser = !"".equals(push[7]);
        boolean hasIdentity =
            !("".equals(push[1]) && "".equals(push[2]) && "".equals(push[3]) && "".equals(push[4]) && "".equals(push[5])
                && "".equals(push[6]));
        if (hasOrgan && !hasIdentity && !hasUser) {
            return ORGAN;
        }
        if (!hasOrgan && !hasIdentity && hasUser) {
            return USER;
        }
        if (hasOrgan && hasIdentity && !hasUser) {
            return ORGAN_IDENTITY;
        }
        if (hasOrgan && !hasIdentity) {
            return ORGAN_USER;
        }
        if (hasOrgan) {
            return ORGAN_IDENTITY_USER;
        }
        return null;
    }

}
