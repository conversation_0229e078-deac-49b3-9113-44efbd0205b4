package com.wunding.learn.user.service.admin.dto.topic.single;

import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.user.service.admin.dto.topic.PageAttributeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 个人赛pk页dto
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Data
@Accessors(chain = true)
@Schema(name = "UpdateSinglePkPageDTO", description = "个人赛pk页dto对象")
public class UpdateSinglePkPageDTO {

    @Schema(description = "当前页id")
    private String id;

    @Schema(description = "主题id")
    private String topicId;

    @Schema(description = "成员A属性")
    private PageAttributeDTO singlePkMemberA;

    @Schema(description = "成员B")
    private PageAttributeDTO singlePkMemberB;

    @Schema(description = "背景图")
    private PageAttributeDTO singlePkBackGroundImg;

    @Schema(description = "进度条")
    private PageAttributeDTO singlePkProgressBar;

    @Schema(description = "答题区背景图片")
    private PageAttributeDTO singlePkAnswerBackground;

    @Schema(description = "选项按钮,多个元素存在值使用逗号分隔,例如 4个圆角则圆角字段以逗号分隔 8,8,8,8 其他字段以此类推")
    private PageAttributeDTO singlePkOptionButton;

    @Schema(description = "音乐播放状态")
    private PageAttributeDTO singlePkMusicPlay;

    @Schema(description = "音乐暂停状态")
    private PageAttributeDTO singlePkMusicStop;

    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "背景音乐")
    private NamePath backgroundMusic;

}
