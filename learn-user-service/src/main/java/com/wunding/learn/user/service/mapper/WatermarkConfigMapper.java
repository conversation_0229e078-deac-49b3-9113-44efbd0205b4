package com.wunding.learn.user.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache;
import com.wunding.learn.user.service.model.WatermarkConfig;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p> 水印配置表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2023-09-06
 */
@Mapper
@CacheNamespace(implementation = MyBatisPlusRedisCache.class, eviction = MyBatisPlusRedisCache.class)
public interface WatermarkConfigMapper extends BaseMapper<WatermarkConfig> {

}
