package com.wunding.learn.user.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.constant.plan.PlanErrorNoEnum;
import com.wunding.learn.common.constant.project.ProjectConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.enums.course.ParaTypeEnum;
import com.wunding.learn.common.enums.other.CategoryTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.form.dto.SyncFormTemplateDTO;
import com.wunding.learn.common.form.enums.OperateTypeEnums;
import com.wunding.learn.common.form.event.SyncFormTemplateEvent;
import com.wunding.learn.common.form.model.FormTemplate;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.CategoryFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.service.admin.query.CommonFormTemplateListQuery;
import com.wunding.learn.user.service.component.CommonFormTemplateViewLimitComponent;
import com.wunding.learn.user.service.dto.CommonFormInfoDTO;
import com.wunding.learn.user.service.dto.CommonFormTemplateListDTO;
import com.wunding.learn.user.service.dto.CommonFormTemplateSaveDTO;
import com.wunding.learn.user.service.dto.CommonTemplateSelectListDTO;
import com.wunding.learn.user.service.mapper.CommonFormTemplateMapper;
import com.wunding.learn.user.service.model.CommonFormTemplate;
import com.wunding.learn.user.service.model.CommonFormTemplateRelevance;
import com.wunding.learn.user.service.model.CommonTemplateOther;
import com.wunding.learn.user.service.service.ICommonFormTemplateRelevanceService;
import com.wunding.learn.user.service.service.ICommonFormTemplateService;
import com.wunding.learn.user.service.service.ICommonTemplateOtherService;
import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 公共表单模板表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhongchaoheng</a>
 * @since 2024-06-13
 */
@Slf4j
@Service("commonFormTemplateService")
public class CommonFormTemplateServiceImpl extends
    BaseServiceImpl<CommonFormTemplateMapper, CommonFormTemplate> implements
    ICommonFormTemplateService {

    public static final String IS_PUBLISH = "isPublish";

    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private CategoryFeign categoryFeign;

    @Resource
    private ICommonTemplateOtherService commonTemplateOtherService;
    @Resource
    private CommonFormTemplateViewLimitComponent commonFormTemplateViewLimitComponent;
    @Resource
    private ICommonFormTemplateRelevanceService commonFormTemplateRelevanceService;
    @Resource
    private ExportComponent exportComponent;

    @Resource
    private FileFeign fileFeign;

    @Resource
    private ParaFeign paraFeign;

    @Resource
    private MqProducer mqProducer;


    @Override
    public PageInfo<CommonFormTemplateListDTO> getFormTemplateList(CommonFormTemplateListQuery query) {
        // 如果是其他查询进入时，需要根据可用范围来筛选
        query.setUserId(UserThreadContext.getUserId());

        Set<String> managerAreaPaths = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        query.setManagerAreaPaths(managerAreaPaths);
        query.setCurrentOrgId(UserThreadContext.getOrgId());
        query.setCurrentUserId(UserThreadContext.getUserId());

        // 设置不使用的模板id
        setNeId(query);

        PageInfo<CommonFormTemplateListDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getFormTemplateList(query));
        pageInfo.getList().forEach(form -> {
            // 查找创建人的信息
            UserDTO userDTO = userFeign.getUserById(form.getCreateName());
            if (userDTO != null) {
                form.setCreateLoginName(userDTO.getLoginName());
                form.setCreateName(userDTO.getFullName());
            }
            if (!StringUtil.isEmpty(form.getCategoryName())) {
                // 分类名称
                Categorys categorys = categoryFeign.getFeign(form.getCategoryName());
                if (categorys != null) {
                    form.setCategoryName(categorys.getCategoryName());
                }
            }
        });
        return pageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveOrUpdateFormTemplate(CommonFormTemplateSaveDTO saveDTO) {
        CommonFormTemplate formTemplate = new CommonFormTemplate();
        BeanUtils.copyProperties(saveDTO, formTemplate);
        Long programmeId = saveDTO.getProgrammeId();
        String oprateType;
        // 不为空时代表是修改
        if (StringUtil.isEmpty(formTemplate.getId())) {
            formTemplate.setId(StringUtil.newId());
            formTemplate.setCreateBy(UserThreadContext.getUserId());
            formTemplate.setCreateTime(new Date());
            SimpleDateFormat sdf = new SimpleDateFormat(DateHelper.YYYYMMDD2);
            formTemplate.setFormNo(sdf.format(new Date()) + StringUtil.random(4));
            formTemplate.setOrgId(UserThreadContext.getOrgId());
            oprateType = OperateTypeEnums.INSERT.getCode();
        } else {
            formTemplate.setUpdateBy(UserThreadContext.getUserId());
            formTemplate.setUpdateTime(new Date());
            oprateType = OperateTypeEnums.UPDATE.getCode();
        }
        saveOrUpdate2(formTemplate);
        // 保存下发范围
        commonFormTemplateViewLimitComponent.handleNewViewLimit(programmeId, formTemplate.getId());
        handleCategoryCanDel();
        //发送消息给业务模块，进行表单修改内容同步
        sendFormTemplateSync(formTemplate, oprateType, programmeId);

        return formTemplate.getId();
    }

    /**
     * 设置不等于id的属性
     *
     * @param query
     */
    private void setNeId(CommonFormTemplateListQuery query) {
        String isAvailableTemplate = paraFeign
            .getParaValue(ParaTypeEnum.AVAILABLE_DEFAULT_APPLY_FORM_TEMPLATE.getCode());
        if (StringUtils.isBlank(isAvailableTemplate)
            || Objects.equals(Integer.valueOf(isAvailableTemplate), GeneralJudgeEnum.NEGATIVE.getValue())) {
            return;
        }
        query.setNeId(ProjectConstant.DEFAULT_APPLY_FORM_TEMPLATE_ID);
    }

    private void handleCategoryCanDel() {
        // 更新表单模板分类是否能够被删除
        List<CommonFormTemplate> formTemplateList = list();
        if (CollectionUtils.isEmpty(formTemplateList)) {
            categoryFeign.deleteCategoryByType(CategoryTypeEnum.FormTemplateCate.getType());
            return;
        }
        List<String> collect = formTemplateList.stream().map(CommonFormTemplate::getCategoryId)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            categoryFeign.deleteCategoryByType(CategoryTypeEnum.FormTemplateCate.getType());
            return;
        }
        categoryFeign.updateCategoryByListCanDel(collect, CategoryTypeEnum.FormTemplateCate.getType());
    }


    @Override
    public void publish(PublishDTO publishDTO) {
        Date now = new Date();
        String userId = UserThreadContext.getUserId();
        String operateType;

        for (String id : publishDTO.getIds()) {
            CommonFormTemplate commonFormTemplate = new CommonFormTemplate();
            commonFormTemplate.setId(id);
            commonFormTemplate.setIsPublish(publishDTO.getIsPublish());
            commonFormTemplate.setUpdateBy(userId);
            commonFormTemplate.setUpdateTime(now);
            //发布表单模板
            if (Objects.equals(publishDTO.getIsPublish(), PublishStatusEnum.IS_PUBLISH.getValue())) {
                // 如果模板字段为空，不允许发布
                boolean hasOther = commonTemplateOtherService
                    .count(new QueryWrapper<CommonTemplateOther>().lambda().eq(CommonTemplateOther::getTemplateId, id))
                    == 0;
                // 默认表单模板没有字段,字段是前端控制固定的
                if (hasOther && !ProjectConstant.DEFAULT_APPLY_FORM_TEMPLATE_ID.equals(id)) {
                    throw new BusinessException(PlanErrorNoEnum.ERR_PUBLIC_NOT_FIELD_DATA);
                }
                commonFormTemplate.setPublishTime(now);
                operateType = OperateTypeEnums.ENABLED.getCode();
            } else {
                // 取消发布模板
                commonFormTemplate.setPublishTime(null);
                operateType = OperateTypeEnums.DISABLED.getCode();
            }
            this.updateById(commonFormTemplate);
            //发送消息给业务模块，进行表单修改内容同步
            sendFormTemplateSync(commonFormTemplate, operateType, null);
        }

    }

    @Override
    public List<String> remove(List<String> ids) {
        List<String> resultList = new ArrayList<>();
        //检查资源表单关联配置表，已经使用的模板不能删除
        ids.forEach(id -> {
            if (commonFormTemplateRelevanceService.count(
                new QueryWrapper<CommonFormTemplateRelevance>().lambda().eq(CommonFormTemplateRelevance::getIsDel, 0)
                    .eq(CommonFormTemplateRelevance::getFormTemplateId, id)) > 0) {
                throw new BusinessException(PlanErrorNoEnum.ERR_DELETE_TEMPLATE_EXISTENCE_DATA);
            }
        });
        //已发布的模板不能删除
        List<CommonFormTemplate> publishList = baseMapper.selectList(new LambdaQueryWrapper<CommonFormTemplate>()
            .in(CommonFormTemplate::getId, ids)
            .eq(CommonFormTemplate::getIsPublish, PublishStatusEnum.IS_PUBLISH.getValue()));
        if (null != publishList) {
            publishList.forEach(formTemplate -> {
                resultList.add("表单编号为:" + formTemplate.getFormNo() + "的表单模板已经发布,不能进行删除");
                ids.removeIf(id -> id.equals(formTemplate.getId()));
            });
        }
        //进行删除操作
        Date now = new Date();
        String userId = UserThreadContext.getUserId();
        for (String id : ids) {
            CommonFormTemplate commonFormTemplate = new CommonFormTemplate();
            commonFormTemplate.setId(id);
            commonFormTemplate.setUpdateBy(userId);
            commonFormTemplate.setUpdateTime(now);
            commonFormTemplate.setIsDel(1);
            this.removeById(commonFormTemplate);
            //删除下发范围
            commonFormTemplateViewLimitComponent.delViewLimit(id);
            //通知分类是否可以删除
            handleCategoryCanDel();
            //发送消息给业务模块，进行表单修改内容同步
            sendFormTemplateSync(commonFormTemplate, OperateTypeEnums.DELETE.getCode(), null);

        }

        return resultList;
    }

    @Override
    public CommonFormTemplateSaveDTO getFormTemplateView(String id) {
        // 根据id获取表单详情
        CommonFormTemplate template = getById(id);
        if (null == template) {
            return null;
        }

        if (Objects.isNull(categoryFeign.getCategoryDetail(template.getCategoryId()))) {
            template.setCategoryId(null);
        }

        CommonFormTemplateSaveDTO saveDTO = new CommonFormTemplateSaveDTO();
        BeanUtils.copyProperties(template, saveDTO);
        saveDTO.setOrgName(orgFeign.getById(saveDTO.getOrgId()).getOrgName());
        // 下发范围
        saveDTO.setLimit(commonFormTemplateViewLimitComponent.getViewLimitBaseInfo(id));
        return saveDTO;
    }

    @Override
    public List<CommonTemplateSelectListDTO> findTemplateSelectList(String id) {
        CommonFormTemplateListQuery inViewLimitQuery = new CommonFormTemplateListQuery();
        //已发布
        inViewLimitQuery.setIsPublish(1);
        // 查询类型 0-表单模板管理 1-其他引用查询
        inViewLimitQuery.setQueryType(1);
        inViewLimitQuery.setId(id);
        inViewLimitQuery.setUserId(UserThreadContext.getUserId());

        // 设置不使用的模板id
        setNeId(inViewLimitQuery);

        // 在下发范围内的表单模板
        List<CommonFormTemplateListDTO> inViewLimitformTemplateList = baseMapper.getFormTemplateList(inViewLimitQuery);

        // 在管辖范围内的表单模板
        Set<String> userManageAreaLevelPath = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        CommonFormTemplateListQuery inManagerAreaQuery = new CommonFormTemplateListQuery();
        inManagerAreaQuery.setIsPublish(1);
        inManagerAreaQuery.setQueryType(0);
        inManagerAreaQuery.setCurrentUserId(UserThreadContext.getUserId());
        inManagerAreaQuery.setManagerAreaPaths(userManageAreaLevelPath);
        List<CommonFormTemplateListDTO> inManagerAreaPathsformTemplateList = baseMapper.getFormTemplateList(inManagerAreaQuery);

        List<CommonTemplateSelectListDTO> result = new ArrayList<>();
        inManagerAreaPathsformTemplateList.forEach(formTemplate -> {
            CommonTemplateSelectListDTO selectListDTO = new CommonTemplateSelectListDTO();
            BeanUtils.copyProperties(formTemplate, selectListDTO);
            // 设置是否在下发范围内
            selectListDTO.setIsInViewLimit(inViewLimitformTemplateList.contains(formTemplate));
            result.add(selectListDTO);
        });

        return result;
    }


    @Override
    public CommonFormInfoDTO findFormTemplateDescription(String id, boolean filterPublish) {
        CommonFormTemplate formTemplate = getById(id);
        if (Objects.isNull(formTemplate)) {
            return null;
        }
        boolean filterPublishCondition = (filterPublish && Objects.equals(formTemplate.getIsPublish(),
            PublishEnum.NOT_PUBLISH.getValue()));
        if (filterPublishCondition) {
            return null;
        }
        CommonFormInfoDTO formInfoDTO = new CommonFormInfoDTO();
        BeanUtils.copyProperties(formTemplate, formInfoDTO);
        return formInfoDTO;
    }

    @Override
    public void deleteByTemplateId(String id) {
        baseMapper.deleteByTemplateId(id);
    }


    @Async
    @Override
    public void exportFormTemplateList(CommonFormTemplateListQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICommonFormTemplateService, CommonFormTemplateListDTO>(
            query) {
            @Override
            protected ICommonFormTemplateService getBean() {
                return SpringUtil.getBean("commonFormTemplateService", ICommonFormTemplateService.class);
            }

            @Override
            protected PageInfo<CommonFormTemplateListDTO> getPageInfo() {
                query.setExport(true);
                return getBean().getFormTemplateList(query);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.FormTemplateManage;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.FormTemplateManage.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                String isPublish = Objects.isNull(map.get(IS_PUBLISH)) ? null : (String) map.get(IS_PUBLISH);
                map.put(IS_PUBLISH, "1".equals(isPublish) ? "已发布" : "未发布");
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    /**
     * 发送消息给业务模块，进行表单修改内容同步--1,修改表单内容;2,修改表单发布状态;3,删除表单
     *
     * @param commonFormTemplate 公共模板
     * @param operateType        操作类型
     * @param programmeId        下发id
     */
    public void sendFormTemplateSync(CommonFormTemplate commonFormTemplate, String operateType, Long programmeId) {
        SyncFormTemplateDTO syncFormTemplateDTO = new SyncFormTemplateDTO();
        FormTemplate formTemplate = new FormTemplate();
        BeanUtils.copyProperties(commonFormTemplate, formTemplate);
        syncFormTemplateDTO.setFormTemplate(formTemplate);
        syncFormTemplateDTO.setOperateType(operateType);
        syncFormTemplateDTO.setProgrammeId(programmeId);
        //发送消息给业务模块，进行表单修改内容同步
        SyncFormTemplateEvent event = new SyncFormTemplateEvent(syncFormTemplateDTO);
        mqProducer.send(event);
    }


}
