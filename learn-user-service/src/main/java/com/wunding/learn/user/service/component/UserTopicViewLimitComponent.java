package com.wunding.learn.user.service.component;

import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.impl.BaseViewLimitServiceImpl;
import com.wunding.learn.user.service.model.UserTopicViewLimit;
import com.wunding.learn.user.service.service.IUserTopicViewLimitService;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 课程下发范围处理服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/13  18:17
 */
@Component("userTopicViewLimitComponent")
public class UserTopicViewLimitComponent extends BaseViewLimitServiceImpl<UserTopicViewLimit>  {

    @Resource
    private IUserTopicViewLimitService userTopicViewLimitService;

    public UserTopicViewLimitComponent() {
        super(LimitTable.userTopicViewLimit);
    }


    @Override
    public void saveBatch(List<UserTopicViewLimit> baseViewLimits) {
        userTopicViewLimitService.saveBatch(baseViewLimits);
    }

    @Override
    public void removeBatchByIds(List<UserTopicViewLimit> baseViewLimits) {
        userTopicViewLimitService.removeBatchByIds(baseViewLimits);
    }
}
