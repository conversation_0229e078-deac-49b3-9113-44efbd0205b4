package com.wunding.learn.user.service.imports;

import com.google.common.collect.Lists;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.i18n.util.ImportTemplateI18nEnum;
import com.wunding.learn.common.util.excel.AbstractExcelTemplate;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.user.service.admin.dto.ability.AbilityBaseInfoImportDTO;
import com.wunding.learn.user.service.admin.dto.ability.AbilityLevelImportDTO;
import com.wunding.learn.user.service.model.AbilityBaseInfo;
import com.wunding.learn.user.service.service.IAbilityBaseInfoService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;

/**
 * @author: chenjinneng
 * @create: 2024-01-02
 **/
public class AbilityLevelExcelTemplate extends AbstractExcelTemplate {

    private static final String[] IMPORT_TITLES = {"能力编码", "能力名称", "能力等级", "行为表现", "等级描述"};

    private final IAbilityBaseInfoService abilityBaseInfoService;
    private final List<AbilityBaseInfoImportDTO> abilityBaseInfoList;

    public AbilityLevelExcelTemplate(IAbilityBaseInfoService abilityBaseInfoService,
        List<AbilityBaseInfoImportDTO> abilityBaseInfoList) {
        super(IMPORT_TITLES);
        this.abilityBaseInfoService = abilityBaseInfoService;
        this.abilityBaseInfoList = abilityBaseInfoList;
    }

    @Override
    protected ExcelCheckMessage doCheck(ExcelCheckMessage excelCheckMessage) {
        String[][] excel = excelCheckMessage.getExcel();
        List<String> messages = Lists.newArrayList();
        List<AbilityLevelImportDTO> list = new ArrayList<>();
        for (int index = 1; index < excel.length; index++) {
            //行数
            int lineNum = index + 1;
            //每一行的数据
            String[] rowData = excel[index];

            // 能力编码校验 不为空、长度限制80
            String code = rowData[0];
            // 不能为空 长度限制80
            verifyAbilityCode(messages, code, lineNum);

            AbilityBaseInfo abilityBaseInfo = abilityBaseInfoService.lambdaQuery().eq(AbilityBaseInfo::getCode, code)
                .one();
            List<String> codeList = abilityBaseInfoList.stream().map(AbilityBaseInfoImportDTO::getCode)
                .collect(Collectors.toList());
            if (abilityBaseInfo == null) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.AbilityLevelExcelTemplateAbilityCodeNotExist,
                        lineNum));
                break;
            }

            // 能力名称校验 不为空、长度限制80
            String name = rowData[1];
            verifyAbilityName(name, messages, lineNum, code, codeList, abilityBaseInfo);

            // 能力等级
            String abilityLevel = rowData[2];
            // 不能为空 长度限制80
            verifyAbilityLevel(messages, abilityLevel, lineNum);

            String abilityId = abilityBaseInfo.getId();
            List<String> levelNameList = abilityBaseInfoService.getLevelNameListByAbilityId(abilityId);
            if (levelNameList.contains(abilityLevel)) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.AbilityLevelExcelTemplateAbilityLevelAlreadyExist,
                        lineNum));
            }

            // 行为表现
            String behavior = rowData[3];
            if (!StringUtils.hasText(behavior)) {
                messages.add(emptyMessage(lineNum, IMPORT_TITLES[3]));
            } else {
                if (behavior.length() > 500) {
                    messages.add(longMessage(lineNum, IMPORT_TITLES[3], 500));
                }
            }

            // 等级描述
            String levelDescription = rowData[4];
            if (StringUtils.hasText(levelDescription) && levelDescription.length() > 500) {
                messages.add(longMessage(lineNum, IMPORT_TITLES[4], 500));
            }

            // 组装数据
            AbilityLevelImportDTO dto = new AbilityLevelImportDTO()
                .setCode(code).setAbilityName(name).setName(abilityLevel)
                .setBehavior(behavior).setLevelDescription(levelDescription).setAbilityId(abilityId);
            list.add(dto);
        }
        excelCheckMessage.setObjects(list);
        excelCheckMessage.setMessage(messages);
        return excelCheckMessage;
    }

    /**
     * <p>  验证能力等级
     *
     * <AUTHOR>
     * @since 2024/6/22
     */
    private void verifyAbilityLevel(List<String> messages, String abilityLevel, int lineNum) {
        // 不能为空 长度限制80
        if (!StringUtils.hasText(abilityLevel)) {
            messages.add(emptyMessage(lineNum, IMPORT_TITLES[2]));
        } else {
            if (abilityLevel.length() > 80) {
                messages.add(longMessage(lineNum, IMPORT_TITLES[2], 80));
            }
        }
    }

    /**
     * <p>  验证能力编码
     *
     * <AUTHOR>
     * @since 2024/6/22
     */
    private void verifyAbilityCode(List<String> messages, String code, int lineNum) {
        if (!StringUtils.hasText(code)) {
            messages.add(emptyMessage(lineNum, IMPORT_TITLES[0]));
        } else {
            if (code.length() > 80) {
                messages.add(longMessage(lineNum, IMPORT_TITLES[0], 80));
            }
        }
    }

    /**
     * <p>  验证能力名称
     *
     * <AUTHOR>
     * @since 2024/6/22
     */
    private void verifyAbilityName(String name,
        List<String> messages,
        int lineNum,
        String code,
        List<String> codeList,
        AbilityBaseInfo abilityBaseInfo) {
        // 不能为空 长度限制80
        if (!StringUtils.hasText(name)) {
            messages.add(emptyMessage(lineNum, IMPORT_TITLES[1]));
        } else {
            if (name.length() > 80) {
                messages.add(longMessage(lineNum, IMPORT_TITLES[1], 80));
            }
        }
        Map<String, String> map = abilityBaseInfoList.stream()
            .collect(HashMap::new, (k, v) -> k.put(v.getCode(), v.getName()), HashMap::putAll);
        if (!name.equals(abilityBaseInfo.getName()) || (codeList.contains(code) && !name.equals(map.get(code)))) {
            messages.add(I18nUtil.getImportMessage(
                ImportTemplateI18nEnum.AbilityLevelExcelTemplateAbilityCodeAndAbilityNameNotMatch, lineNum));
        }
    }
}
