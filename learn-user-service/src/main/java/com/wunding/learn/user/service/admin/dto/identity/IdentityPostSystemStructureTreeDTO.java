package com.wunding.learn.user.service.admin.dto.identity;

import com.wunding.learn.common.bean.BaseTreeEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p> 岗位体系结构树形dto对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-01-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "IdentityPostSystemStructureTreeDTO", description = "岗位体系结构树形dto对象")
public class IdentityPostSystemStructureTreeDTO extends BaseTreeEntity {

    /**
     * 用户端显示 0-隐藏 1-显示
     */
    @Schema(description = "用户端显示 0-隐藏 1-显示")
    private Integer isShow;

    /**
     * 父级id
     */
    @Schema(description = "父级id")
    private String parentId;

    /**
     * 全路径
     */
    @Schema(description = "全路径")
    private String levelPath;

    /**
     * 全路径名称
     */
    @Schema(description = "全路径名称")
    private String levelPathName;

}
