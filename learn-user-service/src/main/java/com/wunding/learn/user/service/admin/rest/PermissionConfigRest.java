package com.wunding.learn.user.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.dto.ImportExcelDTO;
import com.wunding.learn.common.query.ListQuery;
import com.wunding.learn.user.service.admin.dto.ChildPermissionSaveRouterDTO;
import com.wunding.learn.user.service.admin.dto.PermissionConfigDTO;
import com.wunding.learn.user.service.admin.dto.PermissionConfigSaveDTO;
import com.wunding.learn.user.service.admin.dto.PermissionConfigSortBaseDTO;
import com.wunding.learn.user.service.admin.dto.PermissionConfigSortDTO;
import com.wunding.learn.user.service.admin.dto.PermissionConfigUpdateDTO;
import com.wunding.learn.user.service.admin.dto.PermissionRouterSaveDTO;
import com.wunding.learn.user.service.dto.ImportResultDTO;
import com.wunding.learn.user.service.dto.PermissionConfigBaseTreeEntity;
import com.wunding.learn.user.service.model.PermissionConfig;
import com.wunding.learn.user.service.service.IPermissionConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限目录配置 前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2023-09-26
 */
@RestController
@RequestMapping("${module.user.contentPath:/}permissionConfig")
@Tag(description = "权限相关接口", name = "permissionConfigRest")
public class PermissionConfigRest {

    @Resource
    private IPermissionConfigService permissionConfigService;

    @PostMapping("/add")
    @Operation(operationId = "permissionConfigRest_add", summary = "添加权限", description = "添加权限")
    public Result<Void> add(@RequestBody @Valid PermissionConfigSaveDTO saveDTO) {
        permissionConfigService.add(saveDTO);
        return Result.success();
    }

    @PostMapping("/update")
    @Operation(operationId = "permissionConfigRest_update", summary = "更新权限", description = "更新权限")
    public Result<Void> update(@RequestBody @Valid PermissionConfigUpdateDTO updateDTO) {
        permissionConfigService.update(updateDTO);
        return Result.success();
    }

    @PostMapping("/sort")
    @Operation(operationId = "permissionConfigRest_sort", summary = "拖拽排序", description = "拖拽排序")
    public Result<Void> sort(@RequestBody @Valid PermissionConfigSortBaseDTO sortBaseDTO) {
        List<PermissionConfigSortDTO> sortDTOList = sortBaseDTO.getSortList();
        permissionConfigService.sort(sortDTOList);
        return Result.success();
    }

    /**
     * 删除权限
     *
     * @param id 权限id
     * @return 操作状态
     */
    @DeleteMapping("/{id}/del")
    @Operation(operationId = "permissionConfigRest_del", summary = "删除权限", description = "删除权限")
    public Result<Void> del(@PathVariable("id") String id) {
        permissionConfigService.del(id);
        return Result.success();
    }

    /**
     * 后端授权树形列表
     *
     * @return
     */
    @GetMapping("/auth/treeList")
    @Operation(operationId = "permissionConfigRest_authTreeList", summary = "权限管理-后端授权树形列表", description = "权限管理-后端授权树形列表")
    public Result<List<PermissionConfigBaseTreeEntity>> authTreeList(
        @RequestParam(required = false, defaultValue = "") @Parameter(description = "角色id") String roleId) {
        return Result.success(permissionConfigService.authTreeList(roleId));
    }

    /**
     * 权限树
     *
     * @return
     */
    @GetMapping("/treeList")
    @Operation(operationId = "permissionConfigRest_treeList", summary = "权限树", description = "权限树")
    public Result<List<PermissionConfigBaseTreeEntity>> treeList() {
        return Result.success(permissionConfigService.treeList());
    }

    @GetMapping("/list")
    @Operation(operationId = "permissionConfigRest_list", summary = "权限目录配置列表检索", description = "权限目录配置列表检索")
    public Result<PageInfo<PermissionConfigDTO>> list(ListQuery query) {
        return Result.success(permissionConfigService.findListByPage(query));
    }

    @GetMapping("/import")
    @Operation(operationId = "permissionConfigRest_import", summary = "权限目录配置列表导入", description = "权限目录配置列表导入", hidden = true)
    public Result<PageInfo<PermissionConfigDTO>> importDate(String excelPath) {
        permissionConfigService.importData(excelPath);
        return Result.success();
    }

    @GetMapping("/importValid")
    @Operation(operationId = "permissionConfigRest_exportValid", summary = "权限目录配置列表导入校验", description = "权限目录配置列表导入校验", hidden = true)
    public Result<PageInfo<PermissionConfigDTO>> exportValid(String excelPath) {
        permissionConfigService.importValid(excelPath);
        return Result.success();
    }

    @GetMapping("/allList")
    public Result<PageInfo<PermissionConfig>> allList() {
        return Result.success(permissionConfigService.allList());
    }

    @GetMapping("/export")
    @Operation(operationId = "permissionConfigRest_export", summary = "权限目录配置列表-全量导出", description = "权限目录配置列表-全量导出")
    public Result<PageInfo<PermissionConfigDTO>> exportData() {
        permissionConfigService.export();
        return Result.success();
    }

    @PostMapping("/imporExcelData")
    @Operation(operationId = "permissionConfigRest_import", summary = "权限目录配置列表-全量导入", description = "权限目录配置列表-全量导入")
    public Result<String> importExcelData(@RequestBody ImportExcelDTO dto) {
        ImportResultDTO importResultDTO = permissionConfigService.imporExceltData(dto);
        if (Boolean.TRUE.equals(importResultDTO.getIsSuccess())) {
            return Result.success(importResultDTO.getMsg());
        } else {
            return Result.fail(ErrorNoEnum.SUCCESS.getErrorCode(), importResultDTO.getMsg(), importResultDTO.getMsg());
        }
    }

    @PostMapping("/importIncrementExcelData")
    @Operation(operationId = "permissionConfigRest_importIncrementExcelData", summary = "权限目录配置列表-增量导入", description = "权限目录配置列表-增量导入")
    public Result<String> importIncrementExcelData(@RequestBody ImportExcelDTO dto) {
        ImportResultDTO importResultDTO = permissionConfigService.importIncrementExcelData(dto);
        if (Boolean.TRUE.equals(importResultDTO.getIsSuccess())) {
            return Result.success(importResultDTO.getMsg());
        } else {
            return Result.fail(ErrorNoEnum.SUCCESS.getErrorCode(), importResultDTO.getMsg(), importResultDTO.getMsg());
        }
    }


}
