//package com.wunding.learn.user.service.admin.rest;
//
//
//import com.wunding.learn.common.bean.Result;
//import com.wunding.learn.user.service.admin.dto.MailTemplateDTO;
//import com.wunding.learn.user.service.service.IMailTemplateService;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import java.util.List;
//import jakarta.annotation.Resource;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * <p>  邮箱模板表 前端控制器
// *
// * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
// * @since 2022-08-11
// */
//@RestController
//@RequestMapping("${module.user.contentPath:/}mailTemplate")
//@SuppressWarnings("deprecation")
//@Tag(description = "邮件模板管理", name = "MailTemplateRest")
//public class MailTemplateRest {
//
//    @Resource
//    private IMailTemplateService mailTemplateService;
//
//    @GetMapping(path = {"/{type}", ""})
//    public Result<List<MailTemplateDTO>> getMailTemplateList(@PathVariable(value = "type", required = false)
//    @Parameter(description ="模板分类(lecturer_warn:讲师预警), 非必传") String type) {
//        List<MailTemplateDTO> mailTemplateDTOList = mailTemplateService.getMailTemplateList(type);
//        return Result.success(mailTemplateDTOList);
//    }
//}
