package com.wunding.learn.user.service.enums.topic;

/**
 * 页面类型枚举
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
public enum GameTypeEnum {

    /**
     * 列表页类型
     */
    LIST_PAGE(0),

    /**
     * 单人赛
     */
    SINGLE_PK(1),

    /**
     * 组队赛
     */
    TEAM_PK(2),

    /**
     * 多人赛
     */
    MULTIPLE_PK(3),

    /**
     * 错题库
     */
    WRONG_QUESTION(4),
    ;

    public Integer getValue() {
        return value;
    }

    GameTypeEnum(Integer value) {
        this.value = value;
    }

    private final Integer value;


}
