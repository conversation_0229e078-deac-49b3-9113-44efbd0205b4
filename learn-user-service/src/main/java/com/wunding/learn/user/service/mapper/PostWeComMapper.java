package com.wunding.learn.user.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.user.service.model.PostWeCom;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 企微岗位表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2023-05-18
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface PostWeComMapper extends BaseMapper<PostWeCom> {

    /**
     * 删除全部同步岗位数据
     */
    void truncate();
}
