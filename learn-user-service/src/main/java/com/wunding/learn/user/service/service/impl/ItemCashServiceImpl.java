package com.wunding.learn.user.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.constant.redis.CacheRedisDbConst;
import com.wunding.learn.common.redis.util.RedisTemplateUtil;
import com.wunding.learn.common.util.redis.RedisDBUtil;
import com.wunding.learn.user.service.mapper.ItemMapper;
import com.wunding.learn.user.service.model.Item;
import com.wunding.learn.user.service.service.IUserItemCashService;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <p> 案例缓存service
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-29
 */
@Slf4j
@Service("itemCashService")
public class ItemCashServiceImpl extends ServiceImpl<ItemMapper, Item> implements
    IUserItemCashService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void removeItemDetailCache() {
        Set<String> keys = getItemDetailKeys(null);
        redisTemplate.delete(keys);
    }

    private Set<String> getItemDetailKeys(String userId) {
        Set<String> keys = new HashSet<>();
        String keySet = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, "getMenu", "*");
        String homeProjectKeySet = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, "findProjectHomePageList", "*");
        String homeTrainKeySet = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, "findTrainHomePageList", "*");
        keys.addAll(RedisTemplateUtil.scan(redisTemplate, keySet));
        keys.addAll(RedisTemplateUtil.scan(redisTemplate, homeProjectKeySet));
        keys.addAll(RedisTemplateUtil.scan(redisTemplate, homeTrainKeySet));
        if (StringUtils.isNotBlank(userId)) {
            keys = keys.stream().filter(k -> k.contains(":"+userId+":")).collect(Collectors.toSet());
        }
        log.info("keys:{}", keys);
        return keys;
    }

    @Override
    public void removeItemDetailCache(String userId) {
        Set<String> keys = getItemDetailKeys(userId);
        redisTemplate.delete(keys);
    }
}
