package com.wunding.learn.user.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ImportExcelDTO;
import com.wunding.learn.common.dto.QueryBaseDTO;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.query.ListQuery;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.user.service.admin.dto.PermissionConfigDTO;
import com.wunding.learn.user.service.admin.dto.PermissionConfigSaveDTO;
import com.wunding.learn.user.service.admin.dto.PermissionConfigSortDTO;
import com.wunding.learn.user.service.admin.dto.PermissionConfigUpdateDTO;
import com.wunding.learn.user.service.admin.dto.PermissionRouterListDTO;
import com.wunding.learn.user.service.admin.dto.PermissionRouterSaveDTO;
import com.wunding.learn.user.service.dto.ImportResultDTO;
import com.wunding.learn.user.service.dto.PermissionConfigBaseTreeEntity;
import com.wunding.learn.user.service.mapper.PermissionConfigMapper;
import com.wunding.learn.user.service.model.PermissionConfig;
import com.wunding.learn.user.service.model.PermissionRole;
import com.wunding.learn.user.service.model.PermissionRouter;
import com.wunding.learn.user.service.service.IPermissionConfigService;
import com.wunding.learn.user.service.service.IPermissionRoleService;
import com.wunding.learn.user.service.service.IPermissionRouterService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <p> 权限目录配置 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2023-09-26
 */
@Slf4j
@Service("permissionConfigService")
public class PermissionConfigServiceImpl extends BaseServiceImpl<PermissionConfigMapper, PermissionConfig> implements
    IPermissionConfigService {

    public static final String STR_DATA_CONUT = " 条数据！";
    public static final String STR_TABAL_AUTH_CODE = "表格权限目录CODE：";
    @Resource
    @Lazy
    private IPermissionRouterService permissionRouterService;

    @Resource
    private IPermissionRoleService permissionRoleService;


    @Resource
    ImportDataFeign importDataFeign;

    @Resource
    private ExportComponent exportComponent;

    @Override
    public String add(PermissionConfigSaveDTO saveDTO) {
        PermissionConfig parentPermission;
        if (StringUtils.isBlank(saveDTO.getParentId())) {
            parentPermission = new PermissionConfig();
            parentPermission.setId("").setLevel(0).setLevelPath("/").setLevelPathName("/");
        } else {
            parentPermission = getById(saveDTO.getParentId());
        }
        addCheck(saveDTO, parentPermission);
        PermissionConfig permissionConfig = new PermissionConfig();
        BeanUtils.copyProperties(saveDTO, permissionConfig);
        if (StringUtils.isBlank(permissionConfig.getId())) {
            permissionConfig.setId(newId());
        }
        if (StringUtils.isBlank(saveDTO.getCode())) {
            permissionConfig.setCode(permissionConfig.getId());
        }
        permissionConfig.setSortNo((getBaseMapper().getMaxSortNo(parentPermission.getId())) + 1)
            .setLevelPath(parentPermission.getLevelPath() + permissionConfig.getId() + "/")
            .setLevelPathName(parentPermission.getLevelPathName() + permissionConfig.getTitle() + "/");
        permissionConfig.setLevel(parentPermission.getLevel() + 1);
        save(permissionConfig);
        return permissionConfig.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(PermissionConfigUpdateDTO updateDTO) {
        PermissionConfig oldPermissionConfig = getById(updateDTO.getId());
        updateCheck(updateDTO, oldPermissionConfig);
        PermissionConfig permissionConfig = new PermissionConfig();
        BeanUtils.copyProperties(updateDTO, permissionConfig);
        PermissionConfig parentPermission;
        if (StringUtils.isBlank(updateDTO.getParentId())) {
            parentPermission = new PermissionConfig();
            parentPermission.setId("").setLevel(0).setLevelPath("/").setLevelPathName("/");
        } else {
            parentPermission = getById(updateDTO.getParentId());
        }
        permissionConfig.setLevelPath(parentPermission.getLevelPath() + permissionConfig.getId() + "/")
            .setLevelPathName(parentPermission.getLevelPathName() + permissionConfig.getTitle() + "/")
            .setSortNo(oldPermissionConfig.getSortNo());
        setLevel(permissionConfig, parentPermission);
        updateById(permissionConfig);
        if (!permissionConfig.getLevelPath().equals(oldPermissionConfig.getLevelPath())
            || !permissionConfig.getLevelPathName().equals(oldPermissionConfig.getLevelPathName())) {
            List<PermissionConfig> children = list(
                new LambdaQueryWrapper<PermissionConfig>().like(PermissionConfig::getLevelPath, permissionConfig.getId()));
            log.info("权限信息children: " + children);
            if (!CollectionUtils.isEmpty(children)) {
                // 处理权限点的下级LevelPath，及LevelPathName
                getBaseMapper().updateLevelPath(oldPermissionConfig.getLevelPath(), permissionConfig.getLevelPath(),
                    oldPermissionConfig.getLevelPathName(), permissionConfig.getLevelPathName(),
                    children.stream().map(PermissionConfig::getId).collect(Collectors.toList()));
            }
        }
        // 更新权限的level
        updateLevel();

//        下面是开发阶段，为了方便前端修改权限和路由，临时添加的功能。这个不能长期打开，前端改完路由后，需要关闭。
//        if (Objects.nonNull(updateDTO.getChangePermissionConfigTypeToColumn())
//            && updateDTO.getChangePermissionConfigTypeToColumn()) {
//            IPermissionConfigService permissionConfigService = SpringUtil.getBean("permissionConfigService",
//                IPermissionConfigService.class);
//            assert permissionConfigService != null;
//            permissionConfigService.changePermissionConfigType(updateDTO.getId());
//        }
    }

    /**
     * 更新校验
     */
    private void updateCheck(PermissionConfigUpdateDTO updateDTO, PermissionConfig oldPermissionConfig) {

        //parentId为空时先设置为空字符串，避免后续查询异常
        if (updateDTO.getParentId() == null) {
            updateDTO.setParentId("");
        }

        List<PermissionConfig> configList = getPermissionConfigList(updateDTO,
            oldPermissionConfig);

        //校验code,code不同说明这次修改改了code，需要查询数据库该code是否重复
        checkCode(updateDTO, oldPermissionConfig);

        //校验类型
        if (oldPermissionConfig.getType().equals(updateDTO.getType())) {
            return;
        }
        // 只有栏目的父级可以为空
        if (StringUtils.isBlank(oldPermissionConfig.getParentId())) {
            if (updateDTO.getType() == 2) {
                throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_LOCATION);
            }
            return;
        }
        // 获取新的父级下的所有权限
        List<PermissionConfig> parentSubConfigList = list(
            new LambdaQueryWrapper<PermissionConfig>().eq(PermissionConfig::getParentId, updateDTO.getParentId()));
        Set<Integer> subTypeSet = parentSubConfigList.stream()
            .filter(config -> !config.getId().equals(oldPermissionConfig.getId())).map(PermissionConfig::getType)
            .collect(Collectors.toSet());
        if (updateDTO.getType() == 1) {
            checkParentPermission(oldPermissionConfig, subTypeSet);
        } else {
            if (subTypeSet.contains(1)) {
                throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_SAME_LEVEL);
            }
//            改成权限点
            checkLevelCount(updateDTO, oldPermissionConfig, configList);
        }
    }

    private void checkLevelCount(PermissionConfigUpdateDTO updateDTO, PermissionConfig oldPermissionConfig,
        List<PermissionConfig> configList) {
        // 1、没有下一级子菜单，当前权限点级数不能超过2级
        if (CollectionUtils.isEmpty(configList)) {
            Integer levelCount = getBaseMapper().selectLevelCount(updateDTO.getParentId(),
                updateDTO.getType());
            if (levelCount > 1) {
                throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_POINT_LIMIT_LEVEL);
            }
        } else {
            Set<Integer> typeSet = configList.stream().map(PermissionConfig::getType).collect(Collectors.toSet());
            // 2、有下级子菜单，下一级是栏目，不能改成权限点
            if (typeSet.contains(1)) {
                throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_CHILDREN_EXISTS_COLUMN);
            }
            // 3、有下级子菜单，下一级是权限点，需要获取最底层权限点的级数，该权限点级数不能超过2级
            int levelCount = oldPermissionConfig.getLevelPath().split("/").length;
            List<PermissionConfig> leafConfigs = getBaseMapper().selectLeafList(oldPermissionConfig.getLevelPath());
            for (PermissionConfig leafConfig : leafConfigs) {
                int length = leafConfig.getLevelPath().split("/").length;
                if (length > levelCount + 1) {
                    throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_POINT_LIMIT_LEVEL);
                }
            }
        }
    }

    private void checkParentPermission(PermissionConfig oldPermissionConfig,
        Set<Integer> subTypeSet) {
        PermissionConfig parentPermission = getById(oldPermissionConfig.getParentId());
        if (parentPermission.getType() == 2) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_PARENT);
        }
        if (subTypeSet.contains(2)) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_SAME_LEVEL);
        }
    }

    //校验code,code不同说明这次修改改了code，需要查询数据库该code是否重复
    private void checkCode(PermissionConfigUpdateDTO updateDTO, PermissionConfig oldPermissionConfig) {
        if (!oldPermissionConfig.getCode().equals(updateDTO.getCode())) {
            long count = count(
                new LambdaQueryWrapper<PermissionConfig>().eq(PermissionConfig::getCode, updateDTO.getCode()));
            if (count > 0) {
                throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_CODE_EXISTS);
            }
        }
    }

    @Nullable
    private List<PermissionConfig> getPermissionConfigList(PermissionConfigUpdateDTO updateDTO,
        PermissionConfig oldPermissionConfig) {
        if (ObjectUtils.isEmpty(oldPermissionConfig)) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_NOT_EXIST);
        }

        if (updateDTO.getParentId().equals(updateDTO.getId())) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARENT_AUTH_CANNOT_SELF);
        }
        //校验父级
        //parentId不同说明修改了父级，需进行校验
        List<PermissionConfig> configList = list(
            new LambdaQueryWrapper<PermissionConfig>().eq(PermissionConfig::getParentId,
                oldPermissionConfig.getId()));

        // 获取修改后的权限点父级内容 如果是权限点，并且是叶子节点，则允许修改上级权限，但要验证级数
        if (!oldPermissionConfig.getParentId().equals(updateDTO.getParentId()) && updateDTO.getType() == 2) {
                Integer levelCount = getBaseMapper().selectLevelCount(updateDTO.getParentId(), updateDTO.getType());
                if (levelCount > 1) {
                    throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_POINT_LIMIT_LEVEL);
                }
                long count = permissionRouterService.count(
                    new LambdaQueryWrapper<PermissionRouter>().eq(PermissionRouter::getParentId,
                        updateDTO.getParentId()));
                if (count > 0) {
                    throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_PARENT_EXIST_ROUTER);
                }
            }

        return configList;
    }

    @Override
    public void sort(List<PermissionConfigSortDTO> sortDTOList) {
        List<PermissionConfig> permissionConfigs = new ArrayList<>();
        sortDTOList.forEach(sortDTO -> {
            PermissionConfig permissionConfig = new PermissionConfig();
            permissionConfig.setId(sortDTO.getKey()).setSortNo(sortDTO.getSortNo());
            permissionConfigs.add(permissionConfig);
        });
        updateBatchById2(permissionConfigs);
    }

    @Override
    public void del(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BusinessException(UserErrorNoEnum.ERR_ID_NOT_NULL);
        }
        if (isExistChildren(id)) {
            throw new BusinessException(UserErrorNoEnum.ERR_ROUTER_PARENT_NOT_NULL);
        }
        removeById(id);
        permissionRoleService.remove(
            new LambdaQueryWrapper<PermissionRole>().eq(PermissionRole::getPermissionConfigId, id));
        permissionRouterService.remove(
            new LambdaQueryWrapper<PermissionRouter>().eq(PermissionRouter::getParentId, id));
    }

    @Override
    public List<PermissionConfigBaseTreeEntity> authTreeList(String roleId) {
        List<PermissionConfig> permissionConfigList = list(
            new LambdaQueryWrapper<PermissionConfig>().orderByAsc(PermissionConfig::getSortNo,
                PermissionConfig::getCreateTime));
        PermissionConfig rootPermissionConfig = new PermissionConfig();
        rootPermissionConfig.setId("").setLevel(0);
        List<String> permissionIds = permissionRoleService.getPermissionIdsByRoleId(roleId);
        return getTreeChildren(permissionConfigList, rootPermissionConfig, permissionIds);
    }

    @Override
    public List<PermissionConfigBaseTreeEntity> treeList() {
        List<PermissionConfig> permissionConfigList = list(
            new LambdaQueryWrapper<PermissionConfig>().orderByAsc(PermissionConfig::getSortNo,
                PermissionConfig::getCreateTime));
        return getChildren("", permissionConfigList);
    }

    @Override
    public PageInfo<PermissionConfigDTO> findListByPage(ListQuery query) {
        PageInfo<PermissionConfig> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> list(
                new LambdaQueryWrapper<PermissionConfig>().like(StringUtils.isNotBlank(query.getKeyword()),
                    PermissionConfig::getTitle, query.getKeyword())));
        PageInfo<PermissionConfigDTO> resultInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultInfo);
        List<PermissionConfigDTO> list = new ArrayList<>();
        if (!pageInfo.getList().isEmpty()) {
            pageInfo.getList().forEach(config -> {
                PermissionConfigDTO permissionConfigDTO = new PermissionConfigDTO();
                BeanUtils.copyProperties(config, permissionConfigDTO);
                list.add(permissionConfigDTO);
            });
            resultInfo.setList(list);
        }
        return resultInfo;
    }

    @Override
    public void importData(String excelPath) {
        String date = DateHelper.formatDate(new Date(), DateHelper.YYYYMMDD_HHMMSS);
        // excel表信息
        ImportDataDTO importData = importDataFeign.getImportData(excelPath);
        // excel数据
        String[][] excel = importData.getExcel();
        HashMap<String, String> nameIdMap = new HashMap<>();
        List<String[]> rowList = importData.getRowList();
        List<String> init = new ArrayList<>();
        init.add("第一级");
        init.add("第二级");
        init.add("第三级");
        init.add("第四级");
        init.add("第五级");
        List<String> excelTitle = new ArrayList<>();
        excelTitle.add("create_time");
        excelTitle.add("update_by");
        excelTitle.add("update_time");
        for (int i = 0; i < excel.length; i++) {
            StringBuilder stringBuilder = new StringBuilder();
            String[] title = importData.getTitle();
            stringBuilder.append("insert into `permission_config`(");
            for (String cloum : title) {
                if (init.contains(cloum)) {
                    continue;
                }
                stringBuilder.append("`" + cloum + "`" + ",");
            }
            StringBuilder finalStringBuilder = stringBuilder;
            excelTitle.forEach(cloum -> finalStringBuilder.append("`" + cloum + "`" + ","));
            if (',' == stringBuilder.charAt(stringBuilder.length() - 1)) {
                stringBuilder = stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                stringBuilder.delete(stringBuilder.lastIndexOf(","), stringBuilder.length());
            }
            stringBuilder.append("," + "`" + "update_time" + "`");
            stringBuilder.append(") VALUES (");

            String[] strings = rowList.get(i);
            nameIdMap.put(strings[3], strings[0]);
            buildSqlStr(strings, stringBuilder, date);
            if (',' == stringBuilder.charAt(stringBuilder.length() - 1)) {
                stringBuilder = stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                stringBuilder.delete(stringBuilder.lastIndexOf(","), stringBuilder.length());
            }
            stringBuilder.append(");");
            log.info(stringBuilder.toString());
        }
    }

    private static void buildSqlStr(String[] strings, StringBuilder stringBuilder, String date) {
        for (int j = 0; j < strings.length - 1; j++) {
            if (j == 4 || j == 5 || j == 9) {
                stringBuilder.append(strings[j] + ",");
                continue;
            }
            if (j == 11) {
                // 填充创建时间
                stringBuilder.append("'" + date + "'" + ",");
                continue;
            }
            if (j == 12) {
                //填充更新人
                stringBuilder.append("'" + "admin" + "'" + ",");
                continue;
            }
            if (j == 13) {
                // 填充更新时间
                stringBuilder.append("'" + date + "'" + ",");
                continue;
            }
            stringBuilder.append("'" + strings[j] + "'" + ",");
        }
    }

    @Override
    @Transactional
    public void importValid(String excelPath) {
        // excel表信息
        ImportDataDTO importData = importDataFeign.getImportData(excelPath);
        // excel数据
        String[][] excel = importData.getExcel();
        List<String[]> rowList = importData.getRowList();
        for (int i = 0; i < excel.length; i++) {
            String[] strings = rowList.get(i);
            PermissionConfigSaveDTO saveDTO = new PermissionConfigSaveDTO();
            saveDTO.setTitle(strings[3]);
            saveDTO.setCode(strings[1]);
            saveDTO.setSortNo(Integer.valueOf(strings[4]));
            saveDTO.setParentId(strings[2]);
            saveDTO.setType(Integer.valueOf(strings[6]));
            saveDTO.setId(strings[0]);
            add(saveDTO);
        }

    }

    @Override
    public Set<String> getAllConfigId() {
        return list().stream().map(PermissionConfig::getId).collect(Collectors.toSet());
    }

    @Override
    public void export() {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IPermissionConfigService, PermissionConfig>() {

            @Override
            protected IPermissionConfigService getBean() {
                return SpringUtil.getBean("permissionConfigService", IPermissionConfigService.class);
            }

            @Override
            protected PageInfo<PermissionConfig> getPageInfo() {
                return getBean().allList();
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.PermissionConfig;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.PermissionConfig.getType() + "_" + DateUtil.getCurrentTime(
                    DateHelper.YYYYMMDD2);
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<PermissionConfig> allList() {
        QueryBaseDTO queryBaseDTO = new QueryBaseDTO();
        queryBaseDTO.setExport(true);
        return new PageInfo<>(list());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDTO imporExceltData(ImportExcelDTO dto) {
        ImportResultDTO importResultDTO = new ImportResultDTO();
        importResultDTO.setIsSuccess(true);
        // excel表信息
        ImportDataDTO importData = importDataFeign.getImportData(dto.getExcelFile());
        // excel数据
        List<String[]> rowList = importData.getRowList();
        List<PermissionConfig> permissionConfigs = new ArrayList<>();
        handelExcelData(rowList, permissionConfigs);
        Set<String> idList = new HashSet<>();
        Set<String> code = new HashSet<>();
        List<String> messages = new ArrayList<>();
        List<String> ids = permissionConfigs.stream().map(PermissionConfig::getId)
            .collect(Collectors.toList());
        List<String> parentId = permissionConfigs.stream().map(PermissionConfig::getParentId)
            .collect(Collectors.toList());
        permissionConfigs.forEach(json -> {
            if (idList.contains(json.getId())) {
                messages.add("表格权限目录Id：" + json.getId() + "重复");
            } else {
                idList.add(json.getId());
            }
        });
        permissionConfigs.forEach(json -> {
            if (code.contains(json.getCode())) {
                messages.add("编码：" + json.getCode() + "重复");
            } else {
                code.add(json.getId());
            }
        });
        parentId.forEach(parentIds -> {
            if (!ids.contains(parentIds) && StringUtils.isNotBlank(parentIds)) {
                log.info("表格权限目录PatentId：" + parentIds + "不存在表格中");
                messages.add("表格权限目录PatentId：" + parentIds + "不存在表格中");
            }
        });
        if (CollectionUtils.isEmpty(messages)) {
            getBaseMapper().clear();
            saveOrUpdateBatch2(permissionConfigs);
            importResultDTO.setMsg("成功导入" + permissionConfigs.size() + "数据！");
        } else {
            importResultDTO.setMsg(JsonUtil.objToJson(messages));
            importResultDTO.setIsSuccess(false);
        }
        return importResultDTO;
    }

    private static void handelExcelData(List<String[]> rowList, List<PermissionConfig> permissionConfigs) {
        String userId = UserThreadContext.getUserId();
        Date date = new Date();
        for (String[] strings : rowList) {
            PermissionConfig permissionConfig = new PermissionConfig();
            permissionConfig.setId(strings[0]);
            permissionConfig.setCode(strings[1]);
            permissionConfig.setParentId(StringUtils.isNotBlank(strings[2]) ? strings[2] : StringUtils.EMPTY);
            permissionConfig.setTitle(strings[3]);
            permissionConfig.setSortNo(Integer.valueOf(strings[4]));
            permissionConfig.setLevel(Integer.valueOf(strings[5]));
            permissionConfig.setType(Integer.valueOf(strings[6]));
            permissionConfig.setLevelPath(strings[7]);
            permissionConfig.setLevelPathName(strings[8]);
            permissionConfig.setIsDel(Integer.valueOf(strings[9]));
            permissionConfig.setCreateBy(userId);
            permissionConfig.setCreateTime(date);
            permissionConfig.setUpdateBy(userId);
            permissionConfig.setUpdateTime(date);
            permissionConfigs.add(permissionConfig);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDTO importIncrementExcelData(ImportExcelDTO dto) {
        ImportResultDTO importResultDTO = new ImportResultDTO();
        importResultDTO.setIsSuccess(true);
        // excel表信息
        ImportDataDTO importData = importDataFeign.getImportData(dto.getExcelFile());
        // excel数据
        List<String[]> rowList = importData.getRowList();
        List<PermissionConfig> permissionConfigs = new ArrayList<>();
        handelExcelData(rowList, permissionConfigs);
        List<PermissionConfig> dbAllList = getBaseMapper().list();
        List<String> dbDelListId = getBaseMapper().getDelList().stream().map(PermissionConfig::getId).collect(
            Collectors.toList());
        List<PermissionConfig> dbCodeList = list();
        List<String> dbId = dbAllList.stream().map(PermissionConfig::getId).collect(Collectors.toList());
        List<String> dbAllId = dbAllList.stream().map(PermissionConfig::getId).collect(Collectors.toList());
        List<String> dbNoDelId = dbAllList.stream().filter(e -> e.getIsDel() == DelEnum.NOT_DELETE.getValue())
            .map(PermissionConfig::getId).collect(
                Collectors.toList());

        List<String> dbParentId = dbAllList.stream().map(PermissionConfig::getParentId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        List<String> excelId = permissionConfigs.stream().map(PermissionConfig::getId).collect(Collectors.toList());
        List<String> excelParentIds = permissionConfigs.stream().map(PermissionConfig::getParentId)
            .filter(StringUtils::isNotBlank).collect(
                Collectors.toList());
        List<String> excelDelIdList = permissionConfigs.stream()
            .filter(e -> e.getIsDel() == DelEnum.DELETED.getValue()).map(PermissionConfig::getId)
            .collect(Collectors.toList());

        dbId.retainAll(excelId);
        List<String> messages = new ArrayList<>();
        // 需要筛选出新增或者更新信息的，或者恢复删除的数据
        List<PermissionConfig> addList = permissionConfigs.stream().filter(e -> !dbAllId.contains(e.getId()))
            .collect(
                Collectors.toList());
        List<PermissionConfig> updateDTOList = permissionConfigs.stream()
            .filter(e -> dbNoDelId.contains(e.getId()) && e.getIsDel() == DelEnum.NOT_DELETE.getValue())
            .collect(
                Collectors.toList());
        List<String> updateDel = permissionConfigs.stream()
            .filter(e -> dbDelListId.contains(e.getId()) && e.getIsDel() == DelEnum.NOT_DELETE.getValue())
            .map(PermissionConfig::getId).collect(
                Collectors.toList());
        List<String> trueDelList = excelDelIdList.stream().filter(e -> !dbDelListId.contains(e))
            .collect(Collectors.toList());
        List<PermissionConfig> dbAndAddExcelList = new ArrayList<>();
        dbAndAddExcelList.addAll(addList);
        dbAndAddExcelList.addAll(dbAllList);
        List<PermissionConfig> dbAndExcelUpdateList = new ArrayList<>();
        dbAndExcelUpdateList.addAll(dbAllList);
        dbAndExcelUpdateList.addAll(updateDTOList);

        check01(dbAndAddExcelList, messages);

        check02(dbAndAddExcelList, messages);

        check03(permissionConfigs, messages);
        Map<String, Long> excelCode = permissionConfigs.stream()
            .collect(Collectors.groupingBy(PermissionConfig::getCode, Collectors.counting()));
        excelCode.keySet().forEach(e -> {
            if (excelCode.get(e) > 1) {
                messages.add(STR_TABAL_AUTH_CODE + e + " 重复");
            }
        });

        // 校验数据库中是否重复
        checkForDbData(dbAndExcelUpdateList, dbAllList, permissionConfigs, messages);
        dbCodeList.addAll(permissionConfigs);
        dbParentId.addAll(excelParentIds);
        dbParentId.forEach(e -> {
            if (!dbAllId.contains(e) && StringUtils.isNotBlank(e)) {
                log.info("权限目录PatentId：" + e + " 不存在表格ID列中或者数据库中");
                messages.add("权限目录PatentId：" + e + " 不存在表格ID列中或者数据库中");
            }
        });

        if (CollectionUtils.isEmpty(messages)) {
            if (!CollectionUtils.isEmpty(permissionConfigs)) {
                extractedBatchExec(addList, messages, updateDTOList, updateDel, trueDelList);
            }
        } else {
            importResultDTO.setIsSuccess(false);
        }
        importResultDTO.setMsg(JsonUtil.objToJson(messages));
        return importResultDTO;
    }

    @Override
    public void changeDeleteStatusByIdList(List<String> permissionConfigIdList, int deleteStatus) {
        baseMapper.changeDeleteStatusByIdList(permissionConfigIdList, deleteStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePermissionConfigType(String permissionConfigId) {
        PermissionConfig targetPermission = getById(permissionConfigId);

        boolean existsChildren = exists(
            Wrappers.<PermissionConfig>lambdaQuery().eq(PermissionConfig::getParentId, permissionConfigId));
        if (existsChildren) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_HAVE_CHILDREN);
        }
        if (targetPermission.getType() != 2) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_CHANGE_TYPE_TYPE_NOT_ALLOW);
        }

        // 获取权限路由
        List<PermissionRouterListDTO> targetPermissionRouters = permissionRouterService.getChildrenByParentId(
            permissionConfigId);
        // 更新权限点类型为栏目
        targetPermission.setType(1);
        updateById(targetPermission);
        // 删除权限点路由
        List<String> targetPermissionRouterIds = targetPermissionRouters.stream()
            .map(PermissionRouterListDTO::getId).toList();
        permissionRouterService.removeBatchByIds(targetPermissionRouterIds);

        // 在栏目下新增权限点和权限路由
        PermissionConfigSaveDTO permissionConfigSaveDTO = new PermissionConfigSaveDTO();
        permissionConfigSaveDTO.setParentId(permissionConfigId);
        permissionConfigSaveDTO.setTitle("所有操作");
        permissionConfigSaveDTO.setType(2);
        String newPermissionId = add(permissionConfigSaveDTO);

        List<String> routerIds = targetPermissionRouters.stream().map(PermissionRouterListDTO::getRouterId)
            .toList();
        if (!CollectionUtils.isEmpty(routerIds)) {
            PermissionRouterSaveDTO permissionRouterSaveDTO = new PermissionRouterSaveDTO();
            permissionRouterSaveDTO.setParentId(newPermissionId);
            permissionRouterSaveDTO.setRouterId(routerIds);
            permissionRouterService.add(permissionRouterSaveDTO);
        }
    }

    @Override
    public List<String> getSelfAndAllChildPermissionIds(List<String> parentPermissionIds) {
        // 查询所有未被删除的权限配置
        List<PermissionConfig> allPermissions = baseMapper.listAll();

        // 构建父级ID到子权限列表的映射
        Map<String, List<PermissionConfig>> parentChildMap = new HashMap<>();
        for (PermissionConfig pc : allPermissions) {
            String parentId = pc.getParentId();
            parentChildMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(pc);
        }

        Set<String> allChildIds = new HashSet<>();
        Queue<String> queue = new LinkedList<>();

        // 初始化队列和集合，加入初始权限ID
        for (String pid : parentPermissionIds) {
            if (allChildIds.add(pid)) {
                queue.add(pid);
            }
        }

        // BFS遍历所有子权限
        while (!queue.isEmpty()) {
            String currentId = queue.poll();
            List<PermissionConfig> children = parentChildMap.getOrDefault(currentId, Collections.emptyList());
            for (PermissionConfig child : children) {
                String childId = child.getId();
                if (allChildIds.add(childId)) {
                    queue.add(childId);
                }
            }
        }

        return new ArrayList<>(allChildIds);
    }

    public void updateLevel() {
        // 更新权限的level
        int level = 1;
        List<String> parentIdList = new ArrayList<>();
        parentIdList.add("");
        List<PermissionConfig> permissionConfigList = list(
            new LambdaQueryWrapper<PermissionConfig>()
                .in(PermissionConfig::getParentId, parentIdList));
        updateLevel(level, permissionConfigList);
    }

    @NotNull
    private void updateLevel(int level, List<PermissionConfig> permissionConfigList) {
        log.info("permissionConfigList: " + permissionConfigList);
        permissionConfigList.forEach(permissionConfig -> {
            permissionConfig.setLevel(level);
            updateById(permissionConfig);
        });
        Set<String> parentIdList = permissionConfigList.stream().map(PermissionConfig::getId).collect(Collectors.toSet());
        List<PermissionConfig> childPermissionConfigList = list(
            new LambdaQueryWrapper<PermissionConfig>()
                .in(PermissionConfig::getParentId, parentIdList));
        // 当前层级的节点包含下级节点时，继续递归
        if (!CollectionUtils.isEmpty(childPermissionConfigList)){
            updateLevel(level + 1, childPermissionConfigList);
        }
    }

    private static void check03(List<PermissionConfig> permissionConfigs, List<String> messages) {
        Map<String, Long> excelIdMap = permissionConfigs.stream()
            .collect(Collectors.groupingBy(PermissionConfig::getId, Collectors.counting()));
        excelIdMap.keySet().forEach(e -> {
            if (excelIdMap.get(e) > 1) {
                messages.add("表格权限ID：" + e + " 重复");
            }
        });
    }

    private static void check02(List<PermissionConfig> dbAndAddExcelList, List<String> messages) {
        Map<String, Long> reviewCodeMapCount = dbAndAddExcelList.stream()
            .collect(Collectors.groupingBy(PermissionConfig::getCode, Collectors.counting()));
        reviewCodeMapCount.keySet().forEach(e -> {
            if (reviewCodeMapCount.get(e) > 1) {
                messages.add("权限目录CODE：" + e + " 重复");
            }
        });
    }

    private static void check01(List<PermissionConfig> dbAndAddExcelList, List<String> messages) {
        Map<String, Long> reviewIdMapCount = dbAndAddExcelList.stream()
            .collect(Collectors.groupingBy(PermissionConfig::getId, Collectors.counting()));
        reviewIdMapCount.keySet().forEach(e -> {
            if (reviewIdMapCount.get(e) > 1) {
                messages.add("权限目录ID：" + e + " 重复");
            }
        });
    }

    private static void checkForDbData(List<PermissionConfig> dbAndExcelUpdateList, List<PermissionConfig> dbAllList,
        List<PermissionConfig> permissionConfigs, List<String> messages) {
        Map<String, Long> excelUpdateCode = dbAndExcelUpdateList.stream()
            .collect(Collectors.groupingBy(PermissionConfig::getCode, Collectors.counting()));
        excelUpdateCode.keySet().forEach(e -> {
            if (excelUpdateCode.get(e) > 1) {
                PermissionConfig sameCodeDb = dbAllList.stream()
                    .filter(dt -> Objects.equals(String.valueOf(e), dt.getCode())).collect(Collectors.toList())
                    .get(0);
                List<PermissionConfig> sameCodeExcel = permissionConfigs.stream()
                    .filter(dt -> Objects.equals(String.valueOf(e), dt.getCode())).collect(
                        Collectors.toList());
                if (sameCodeExcel.size() > 2) {
                    messages.add(STR_TABAL_AUTH_CODE + e + " 与数据库有重复");
                } else {
                    PermissionConfig permissionConfig = sameCodeExcel.get(0);
                    if (!sameCodeDb.getId().equals(permissionConfig.getId())) {
                        messages.add(STR_TABAL_AUTH_CODE + e + " 与数据库有重复");
                    }
                }
            }
        });
    }

    private void extractedBatchExec(List<PermissionConfig> addList, List<String> messages,
        List<PermissionConfig> updateDTOList,
        List<String> updateDel, List<String> trueDelList) {
        if (!CollectionUtils.isEmpty(addList)) {
            saveOrUpdateBatch2(addList);
            messages.add("成功导入 " + addList.size() + STR_DATA_CONUT);
        }
        if (!CollectionUtils.isEmpty(updateDTOList)) {
            saveOrUpdateBatch2(updateDTOList);
            messages.add("成功更新 " + updateDTOList.size() + STR_DATA_CONUT);
        }
        if (!CollectionUtils.isEmpty(updateDel)) {
            getBaseMapper().updateDel(updateDel);
            messages.add("成功恢复 " + updateDel.size() + STR_DATA_CONUT);
        }
        if (!CollectionUtils.isEmpty(trueDelList)) {
            getBaseMapper().deleteBatchIds(trueDelList);
            messages.add("成功删除 " + trueDelList.size() + STR_DATA_CONUT);
        }
    }

    /**
     * 递归权限子节点
     *
     * @param parentId 上级节点id
     * @param list     所有权限
     * @return 下级树
     */
    private List<PermissionConfigBaseTreeEntity> getChildren(String parentId, List<PermissionConfig> list) {
        List<PermissionConfigBaseTreeEntity> resultList = new ArrayList<>();
        list.stream().filter(permissionConfig -> Objects.equals(permissionConfig.getParentId(), parentId))
            .forEach(permissionConfig -> {
                PermissionConfigBaseTreeEntity baseTreeEntity = new PermissionConfigBaseTreeEntity();
                baseTreeEntity.setKey(permissionConfig.getId());
                baseTreeEntity.setTitle(permissionConfig.getTitle());
                baseTreeEntity.setSortNo(Long.valueOf(permissionConfig.getSortNo()));
                baseTreeEntity.setType(permissionConfig.getType());
                baseTreeEntity.setCode(permissionConfig.getCode());
                baseTreeEntity.setChildren(getChildren(permissionConfig.getId(), list));
                baseTreeEntity.setIsLeaf(CollectionUtils.isEmpty(baseTreeEntity.getChildren()));
                baseTreeEntity.setIsExistChildren(!baseTreeEntity.getIsLeaf());
                resultList.add(baseTreeEntity);
            });
        return resultList;
    }

    public List<PermissionConfigBaseTreeEntity> getTreeChildren(List<PermissionConfig> permissionConfigList,
        PermissionConfig rootPermissionConfig, List<String> permissionIds) {
        List<PermissionConfigBaseTreeEntity> list = new ArrayList<>();
        // 获取第一层的节点数据
        List<PermissionConfig> levelList = permissionConfigList.stream()
            .filter(
                permissionConfig -> Objects.equals(permissionConfig.getParentId(), rootPermissionConfig.getId()))
            .collect(Collectors.toList());

        for (PermissionConfig permissionConfig : levelList) {
            PermissionConfigBaseTreeEntity treeEntity = new PermissionConfigBaseTreeEntity();
            treeEntity.setKey(permissionConfig.getId());
            treeEntity.setTitle(permissionConfig.getTitle());
            treeEntity.setType(permissionConfig.getType());
            treeEntity.setLevel(permissionConfig.getLevel());
            List<PermissionConfigBaseTreeEntity> treeChildren = getTreeChildren(permissionConfigList,
                permissionConfig, permissionIds);
            treeEntity.setChildren(treeChildren);
            treeEntity.setIsLeaf(CollectionUtils.isEmpty(treeEntity.getChildren()));
            treeEntity.setIsExistChildren(!treeEntity.getIsLeaf());
            dealIsCheck(permissionIds, treeEntity, treeChildren);
            list.add(treeEntity);
        }
        return list;
    }

    /**
     * 处理是否选中
     *
     * @param permissionIds
     * @param treeEntity
     * @param treeChildren
     */
    private void dealIsCheck(List<String> permissionIds, PermissionConfigBaseTreeEntity treeEntity,
        List<PermissionConfigBaseTreeEntity> treeChildren) {
        treeEntity.setIsCheck(
            !CollectionUtils.isEmpty(permissionIds) && permissionIds.contains(treeEntity.getKey()));
        if (!CollectionUtils.isEmpty(treeChildren)) {
            List<PermissionConfigBaseTreeEntity> tempTreeChildren = treeChildren.stream()
                    .filter(t -> !t.getIsCheck()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tempTreeChildren)) {
                treeEntity.setIsCheck(false);
            }
        }
    }

    /**
     * 是否有下级权限
     *
     * @param id 权限id
     * @return 是否有下一级权限
     */
    private boolean isExistChildren(String id) {
        LambdaQueryWrapper<PermissionConfig> query = new LambdaQueryWrapper<>();
        query.eq(PermissionConfig::getParentId, id);
        return baseMapper.selectCount(query) > 0;
    }

    /**
     * 新增校验
     */
    private void addCheck(PermissionConfigSaveDTO saveDTO, PermissionConfig parentPermission) {
        if (addCheck01(saveDTO, parentPermission)) {
            return;
        }

        List<PermissionConfig> parentSubConfigList = list(
            new LambdaQueryWrapper<PermissionConfig>().eq(PermissionConfig::getParentId, parentPermission.getId()));
        Set<Integer> typeSet = parentSubConfigList.stream().map(PermissionConfig::getType).collect(Collectors.toSet());
        if (saveDTO.getType() == 1) {
            addCheck02(parentPermission, typeSet);
        } else {
            if (typeSet.contains(1)) {
                throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_SAME_LEVEL);
            }
            if (parentPermission.getType() == 2) {
                Integer levelCount = getBaseMapper().selectLevelCount(parentPermission.getId(), saveDTO.getType());
                if (levelCount > 1) {
                    throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_POINT_LIMIT_LEVEL);
                }
                long count = permissionRouterService.count(
                    new LambdaQueryWrapper<PermissionRouter>().eq(PermissionRouter::getParentId,
                        saveDTO.getParentId()));
                if (count > 0) {
                    throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_PARENT_EXIST_ROUTER);
                }
            }
        }
    }

    private void addCheck02(PermissionConfig parentPermission, Set<Integer> typeSet) {
        if (parentPermission.getType() == 2) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_PARENT);
        }
        if (typeSet.contains(2)) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_SAME_LEVEL);
        }
    }

    private boolean addCheck01(PermissionConfigSaveDTO saveDTO, PermissionConfig parentPermission) {
        if (ObjectUtils.isEmpty(parentPermission)) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_PARENT_NOT_EXIST);
        }
        long resultCount = count(
            new LambdaQueryWrapper<PermissionConfig>().eq(PermissionConfig::getCode, saveDTO.getCode()));
        if (resultCount > 0) {
            throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_CODE_EXISTS);
        }
        if (StringUtils.isBlank(parentPermission.getId())) {
            if (saveDTO.getType() == 2) {
                throw new BusinessException(UserErrorNoEnum.ERR_PERMISSION_LOCATION);
            }
            return true;
        }
        return false;
    }

    /**
     * 设置层级
     */
    private void setLevel(PermissionConfig permissionConfig, PermissionConfig parentPermissionConfig) {
        if (permissionConfig.getType() == 1) {
            permissionConfig.setLevel(parentPermissionConfig.getLevel() + 1);
        } else {
            if (parentPermissionConfig.getLevel() == 5) {
                permissionConfig.setLevel(6);
            } else {
                permissionConfig.setLevel(5);
            }
            if (parentPermissionConfig.getLevel() == 2 || parentPermissionConfig.getLevel() == 3) {
                parentPermissionConfig.setLevel(4);
                updateById(parentPermissionConfig);
            }
        }
    }

}
