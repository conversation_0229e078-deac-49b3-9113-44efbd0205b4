package com.wunding.learn.user.service.dao;
import com.wunding.learn.user.service.model.SysSensitiveWord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 敏感词持久层
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON><PERSON><PERSON></a>
 * @date 2024/8/29 14:37
 */
public interface SysSensitiveWordDao extends IService<SysSensitiveWord>{
    /**
     * 保存敏感词
     *
     * @param sysSensitiveWord 敏感词对象
     */
    void saveSysSensitiveWord(SysSensitiveWord sysSensitiveWord);

    /**
     * 更新敏感词
     *
     * @param sysSensitiveWord 敏感词对象
     */
    void updateSysSensitiveWord(SysSensitiveWord sysSensitiveWord);

    /**
     * 启用敏感词
     * @param sysSensitiveWord 敏感词对象
     */
    void enabledSysSensitiveWord(SysSensitiveWord sysSensitiveWord);

    /**
     * 禁用敏感词
     * @param sysSensitiveWord 敏感词对象
     */
    void disabledSysSensitiveWord(SysSensitiveWord sysSensitiveWord);

    /**
     * 删除敏感词
     *
     * @param sysSensitiveWord 敏感词对象
     */
    void delSysSensitiveWord(SysSensitiveWord sysSensitiveWord);
}
