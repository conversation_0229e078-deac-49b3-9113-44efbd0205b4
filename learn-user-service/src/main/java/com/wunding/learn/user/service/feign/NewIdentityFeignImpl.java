package com.wunding.learn.user.service.feign;

import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.user.api.dto.NewIdentityFeignDTO;
import com.wunding.learn.user.api.dto.NewIdentitySaveFeignDTO;
import com.wunding.learn.user.api.service.NewIdentityFeign;
import com.wunding.learn.user.service.model.NewIdentity;
import com.wunding.learn.user.service.service.INewIdentityService;
import com.wunding.learn.user.service.service.IPostWeComService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 身份信息Feign接口实现
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhong<PERSON><PERSON><PERSON></a>
 * @date 2024/1/17 19:37
 */
@RestController
public class NewIdentityFeignImpl implements NewIdentityFeign {

    @Resource
    private INewIdentityService newIdentityService;
    @Resource
    private IPostWeComService postWeComService;

    @Override
    public List<NewIdentityFeignDTO> getNewIdentityListByCategoryId(String categoryId) {
        List<NewIdentityFeignDTO> result = new ArrayList<>();
        List<NewIdentity> list = newIdentityService.getNewIdentityListByCategoryId(categoryId);
        if (!CollectionUtils.isEmpty(list)) {
            result = BeanListUtils.copyList(list, NewIdentityFeignDTO.class);
        }
        return result;
    }

    @Override
    public void saveNewIdentityBatchFeign(List<NewIdentitySaveFeignDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<NewIdentity> saveList = BeanListUtils.copyList(list, NewIdentity.class);
        newIdentityService.saveBatch(saveList);
    }

    @Override
    public void updateNewIdentityBatchFeign(List<NewIdentitySaveFeignDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<NewIdentity> saveList = BeanListUtils.copyList(list, NewIdentity.class);
        newIdentityService.updateBatchById(saveList);
    }
}
