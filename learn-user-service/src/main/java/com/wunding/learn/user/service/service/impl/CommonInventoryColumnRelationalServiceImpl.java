package com.wunding.learn.user.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.form.enums.OperateTypeEnums;
import com.wunding.learn.common.form.mapper.InventoryColumnRelationalMapper;
import com.wunding.learn.common.form.model.InventoryColumnRelational;
import com.wunding.learn.common.form.model.TemplateOther;
import com.wunding.learn.common.form.service.IInventoryColumnRelationalService;
import com.wunding.learn.common.form.service.IInventoryService;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.user.service.mapper.CommonInventoryColumnRelationalMapper;
import com.wunding.learn.user.service.model.CommonInventoryColumnRelational;
import com.wunding.learn.user.service.model.CommonTemplateOther;
import com.wunding.learn.user.service.service.ICommonInventoryColumnRelationalService;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 公共表单列映射关系 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-03-13
 */
@Slf4j
@Service("commonInventoryColumnRelationalService")
public class CommonInventoryColumnRelationalServiceImpl extends
    ServiceImpl<CommonInventoryColumnRelationalMapper, CommonInventoryColumnRelational> implements
    ICommonInventoryColumnRelationalService {


    private static final String COLUMN_DATETIME = "columnDatetime";
    private static final String COLUMN_BIGINT = "columnBigint";
    private static final String COLUMN_VARCHAR = "columnVarchar";
    private static final String COLUMN_TYPE = "column_type";
    private static final String MAX_COLUMN_CODE_SQL = "max(column_code) column_code";
    private static final String TEMPLATE_ID_SQL = "template_id";


    /**
     * 初始化字段映射关系信息
     *
     * @param other
     * @return
     */
    @Override
    public Map<String,Object> initFieldRelational(CommonTemplateOther other) {
        Map<String,Object> result = new HashMap<>();
        CommonInventoryColumnRelational inventoryColumnRelational;
        String relationalOperateType;
        //检查数据库是否有历史记录
        LambdaQueryWrapper<CommonInventoryColumnRelational> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommonInventoryColumnRelational::getTemplateOtherId, other.getId());
        queryWrapper.eq(CommonInventoryColumnRelational::getTemplateId, other.getTemplateId());
        inventoryColumnRelational = baseMapper.selectOne(queryWrapper);
        //保存模板和清单的列映射关系
        QueryWrapper<CommonInventoryColumnRelational> query = new QueryWrapper<>();
        query.select(MAX_COLUMN_CODE_SQL);
        query.eq(TEMPLATE_ID_SQL, other.getTemplateId());
        if (Objects.nonNull(inventoryColumnRelational)) {
            inventoryColumnRelational = generateUpdateInfo(other, inventoryColumnRelational);
            relationalOperateType = OperateTypeEnums.UPDATE.getCode();
        } else {
            inventoryColumnRelational = generateInsertInfo(other, inventoryColumnRelational);
            relationalOperateType = OperateTypeEnums.INSERT.getCode();
        }
        result.put("relationalOperateType",relationalOperateType);
        result.put("inventoryColumnRelational",inventoryColumnRelational);
        return result;
    }

    /**
     * 新增字段映射关系信息
     *
     * @param inventoryColumnRelational
     * @return
     */
    @Override
    public void insertFieldRelational(CommonInventoryColumnRelational inventoryColumnRelational) {
        baseMapper.insert(inventoryColumnRelational);
    }

    /**
     * 修改字段映射关系信息
     *
     * @param inventoryColumnRelational
     * @return
     */
    @Override
    public void updateFieldRelational(CommonInventoryColumnRelational inventoryColumnRelational) {
        baseMapper.updateById(inventoryColumnRelational);
    }


    /**
     * 生成插入信息
     *
     * @param other
     * @param inventoryColumnRelational
     * @return
     */
    public CommonInventoryColumnRelational generateInsertInfo(CommonTemplateOther other, CommonInventoryColumnRelational inventoryColumnRelational) {
        String curUserId = UserThreadContext.getUserId();
        //查找最大字段编码
        QueryWrapper<CommonInventoryColumnRelational> query = new QueryWrapper<>();
        query.select(MAX_COLUMN_CODE_SQL);
        query.eq(TEMPLATE_ID_SQL, other.getTemplateId());
        CommonInventoryColumnRelational columnCode = baseMapper.selectOne(query);

        //填充插入字段信息
        inventoryColumnRelational = new CommonInventoryColumnRelational();
        if (other.getFieldType() == 2) {
            inventoryColumnRelational.setColumnType(COLUMN_BIGINT);
            query.eq(COLUMN_TYPE, COLUMN_BIGINT);
        } else if (other.getFieldType() == 1) {
            inventoryColumnRelational.setColumnType(COLUMN_DATETIME);
            query.eq(COLUMN_TYPE, COLUMN_DATETIME);
        } else {
            inventoryColumnRelational.setColumnType(COLUMN_VARCHAR);
            query.eq(COLUMN_TYPE, COLUMN_VARCHAR);
        }
        inventoryColumnRelational.setColumnCode(
            (Objects.isNull(columnCode) ? Integer.valueOf(1) : columnCode.getColumnCode()) + 1);
        inventoryColumnRelational.setId(StringUtil.newId());
        inventoryColumnRelational.setTemplateOtherId(other.getId());
        inventoryColumnRelational.setTemplateId(other.getTemplateId());
        inventoryColumnRelational.setCreateBy(curUserId);
        inventoryColumnRelational.setCreateTime(new Date());

        return inventoryColumnRelational;
    }

    /**
     * 生成修改信息
     *
     * @param other
     * @param inventoryColumnRelational
     * @return
     */
    public CommonInventoryColumnRelational generateUpdateInfo(CommonTemplateOther other, CommonInventoryColumnRelational inventoryColumnRelational) {
        String curUserId = UserThreadContext.getUserId();
        String columType = "";
        //查找最大字段编码
        QueryWrapper<CommonInventoryColumnRelational> query = new QueryWrapper<>();
        query.select(MAX_COLUMN_CODE_SQL);
        query.eq(TEMPLATE_ID_SQL, other.getTemplateId());
        if (other.getFieldType() == 2) {
            query.eq(COLUMN_TYPE, COLUMN_BIGINT);
            columType = COLUMN_BIGINT;
        } else if (other.getFieldType() == 1) {
            query.eq(COLUMN_TYPE, COLUMN_DATETIME);
            columType = COLUMN_DATETIME;
        } else {
            query.eq(COLUMN_TYPE, COLUMN_VARCHAR);
            columType = COLUMN_VARCHAR;
        }

        CommonInventoryColumnRelational columnCode = baseMapper.selectOne(query);
        if (!Objects.equals(columType, inventoryColumnRelational.getColumnType())) {
            inventoryColumnRelational.setColumnCode(
                (Objects.isNull(columnCode) ? Integer.valueOf(1) : columnCode.getColumnCode() + 1));
            inventoryColumnRelational.setColumnType(columType);
        }

        inventoryColumnRelational.setUpdateBy(curUserId);
        inventoryColumnRelational.setUpdateTime(new Date());

        return inventoryColumnRelational;
    }

}
