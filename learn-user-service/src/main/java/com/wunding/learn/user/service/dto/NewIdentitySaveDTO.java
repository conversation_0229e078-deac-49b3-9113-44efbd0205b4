package com.wunding.learn.user.service.dto;

import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.user.api.dto.IdentityPostSystemRelateBaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * 保存身份参数对象
 *
 * <AUTHOR>
 * @date 2022/3/29
 */
@Data
@Schema(name = "NewIdentitySaveDTO", description = "(新)保存身份参数对象")
public class NewIdentitySaveDTO {

    @Schema(description = "id , 更新时用")
    private String id;

    @Schema(description = "身份名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称最多输入100个字")
    private String name;

    @Schema(description = "身份类别id", hidden = true)
    private String categoryId;

    @Schema(description = "身份目录id")
    @NotBlank(message = "身份目录id不能为空")
    private String directoryId;

    @Schema(description = "身份编码")
    @Length(max = 100, message = "编码最多输入100个字")
    @Pattern(regexp = "[a-zA-Z0-9]*", message = "编码只能是字母或数字")
    private String code;

    @Schema(description = "是否启用:0-禁用,1-启用")
    private Integer isAvailable;

    @Schema(description = "在职时间（大于），单位是月，此字段只给时间身份使用")
    @Range(min = 0, max = 999, message = "在职时间取值范围1-9999")
    private Integer minMonth;

    @Schema(description = "在职时间（小于等于），单位是月，此字段只给时间身份使用")
    @Range(min = 1, max = 999, message = "在职时间取值范围1-9999")
    private Integer maxMonth;

    @Schema(description = "身份说明")
    @Length(max = 300, message = "说明最多输入300个字")
    private String description;

    @Schema(description = "仅自己使用:0-禁用,1-启用")
    private Integer isPrivate;

    @Schema(description = "排序")
    private Integer sortNo;

    @Schema(description = "岗位体系关联列表")
    private List<IdentityPostSystemRelateBaseDTO> relateList;

    @Schema(description = "操作类型", hidden = true)
    private OperationEnum operationEnum;

}
