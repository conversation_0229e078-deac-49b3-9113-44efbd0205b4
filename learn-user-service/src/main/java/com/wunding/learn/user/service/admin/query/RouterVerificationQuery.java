package com.wunding.learn.user.service.admin.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RouterVerificationQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = -5210514286251564740L;

    @Schema(description = "路由ID")
    @Size(min = 1, message = "路由ID集合不可为空")
    @NotNull(message = "路由ID集合不可为空")
    private List<String> routerIdList;
}
