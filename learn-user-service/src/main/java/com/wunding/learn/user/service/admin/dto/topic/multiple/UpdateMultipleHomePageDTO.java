package com.wunding.learn.user.service.admin.dto.topic.multiple;

import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.user.service.admin.dto.topic.PageAttributeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 更新多人赛主页dto对象
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Data
@Accessors(chain = true)
@Schema(name = "MultipleHomePageDTO", description = "更新多人赛主页dto对象")
public class UpdateMultipleHomePageDTO {

    @Schema(description = "当前页id")
    private String id;

    @Schema(description = "主题id")
    private String topicId;

    /**
     * 针对多人赛首页的这个属性,对应 TopicPageAttribute 实体的同名属性，但意义不太相同
     * <p>
     * 以多人赛首页注释的参数意义为准
     * <p>
     * 注意使用封面图片得学员端接口针对处理逻辑
     */
    @Schema(description = "0-使用封面图片,1-使用自定义图片")
    private Integer useImage;

    @Schema(description = "多人赛头部图片")
    private PageAttributeDTO multipleHomeHeadImg;

    @Schema(description = "多人赛首页背景图片")
    private PageAttributeDTO multipleHomeBackgroundImg;

    @Schema(description = "排行版")
    private PageAttributeDTO multipleHomeRankImg;

    @Schema(description = "我要组队")
    private PageAttributeDTO multipleStartChallengeButton;

    @Schema(description = "随机加入")
    private PageAttributeDTO multipleRandomMatchButton;

    @Schema(description = "邀请码加入")
    private PageAttributeDTO multipleInviteFriendButton;

    @Schema(description = "音乐播放状态")
    private PageAttributeDTO multipleHomePageMusicPlay;

    @Schema(description = "音乐暂停状态")
    private PageAttributeDTO multipleHomePageMusicStop;

    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "背景音乐")
    private NamePath backgroundMusic;
}
