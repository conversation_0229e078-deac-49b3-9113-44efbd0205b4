package com.wunding.learn.user.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.user.service.model.UserOrg;
import java.util.List;
import java.util.Set;

/**
 * 用户组织表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-02-23
 */
public interface IUserOrgService extends IService<UserOrg> {

    /**
     * 根据用户的组织关系查询用户的组织层级
     *
     * @param userOrg 参数对象
     * @return levelPathList
     */
    List<String> getLevelPathByUserOrg(UserOrg userOrg);

    /**
     * 根据用户的组织关系查询用户的组织层级
     *
     * @param userId       userId
     * @param relationType relationType
     * @return levelPath
     */
    List<String> getLevelPathByUserIdAndRelationType(String userId, String relationType);

    /**
     * 根据用户的组织关系查询
     *
     * @param userId       用户id
     * @param relationType relationType
     * @return 用户组织关系
     */
    List<UserOrg> getByUserIdAndRelationType(String userId, String relationType);

    /**
     * 根据用户的组织关系查询
     *
     * @param userIds      用户id
     * @param relationType relationType
     * @return 用户组织关系
     */
    List<UserOrg> getByUserIdsAndRelationType(Set<String> userIds, String relationType);

    /**
     * 更新数据
     *
     * @param limitIdList 组织id列表
     * @param limitType   可见类型
     * @param userId      用户id
     */
    void updateBy(List<String> limitIdList, String limitType, String userId);

    /**
     * 获取用户的管辖范围
     *
     * @param userId 用户id
     * @return 管辖范围部门id
     */
    Set<String> getUserManageAreaOrgId(String userId);

    /**
     * 获取用户的管辖范围 或指定部门的下发范围
     *
     * @param userId 用户id
     * @param orgId  指定部门id
     * @return 管辖范围部门id
     */
    Set<String> getUserManageAreaOrgId(String userId, String orgId);

    /**
     * 获取用户的下发范围
     *
     * @param userId 用户id
     * @return 下发范围部门id
     */
    Set<String> getUserVisitAreaOrgId(String userId);
}
