package com.wunding.learn.user.service.client.rest;


import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.user.service.client.dto.BackgroundConfigDTO;
import com.wunding.learn.user.service.client.dto.DictInfoDTO;
import com.wunding.learn.user.service.client.dto.HomePageConfigDTO;
import com.wunding.learn.user.service.client.dto.HomePageSwitchConfigDTO;
import com.wunding.learn.user.service.client.dto.ItemClientDTO;
import com.wunding.learn.user.service.client.dto.SysConfigDTO;
import com.wunding.learn.user.service.service.IHomePageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/7/26
 */
@RestController
@RequestMapping("${module.user.contentPath:/}api/home")
@Tag(description = "首页相关控制器", name = "HomePageRest")
public class HomePageRest {

    @Resource
    private IHomePageService homePageService;


    @Operation(operationId = "getDictInfo", summary = "获取字典信息", description = "获取字典信息")
    @GetMapping("/dictInfo")
    public Result<DictInfoDTO> getDictInfo() {
        return Result.success(homePageService.getDictInfo());
    }

    @Operation(operationId = "sysConfig", summary = "获取系统配置信息", description = "获取系统配置信息")
    @GetMapping("/sysConfig")
    public Result<SysConfigDTO> getSysConfig(){
        return Result.success(homePageService.getSysConfig());
    }

    @Operation(operationId = "getBottomItemList", summary = "获取底部菜单", description = "获取底部菜单")
    @GetMapping("/bottomItem")
    public Result<List<ItemClientDTO>> getBottomItemList() {
        return Result.success(homePageService.getBottomItemList());
    }


    @Operation(operationId = "getMiddleItemList", summary = "获取中部菜单", description = "获取中部菜单")
    @GetMapping("/middleItem")
    public Result<List<ItemClientDTO>> getMiddleItemList() {
        return Result.success(homePageService.getMiddleItemList());
    }

    @Operation(operationId = "getTopicItemList", summary = "获取话题菜单", description = "获取话题菜单")
    @GetMapping("/topicItem")
    public Result<List<ItemClientDTO>> getTopicItemList() {
        return Result.success(homePageService.getTopicItemList());
    }


    @Operation(operationId = "getMyCenterItemList", summary = "获取我的菜单", description = "获取我的菜单")
    @GetMapping("/myCenterItem")
    public Result<List<ItemClientDTO>> getMyCenterItemList() {
        return Result.success(homePageService.getMyCenterItemList());
    }


    @Operation(operationId = "getAppItemList", summary = "获取应用菜单", description = "获取应用菜单")
    @GetMapping("/appItem")
    public Result<List<ItemClientDTO>> getAppItemList() {
        return Result.success(homePageService.getAppItemList());
    }

    @Operation(operationId = "getHomePageItemConfig", summary = "获取首页配置信息", description = "获取首页配置信息")
    @GetMapping("/homePageItemConfig")
    public Result<HomePageConfigDTO> getHomePageItemConfig(@Parameter(description = "配置id，可空。空参：有用户最近选择的，返回用户最近选择的。无用户最近选择的返回空数据。")
        @RequestParam(required = false) String configId,
        @Parameter(description = "服务端： PC, H5") String server
    ) {
        return Result.success(homePageService.getHomePageItemConfig(configId, server));
    }

    @Operation(operationId = "getHomePageSwitchConfig", summary = "获取切换配置列表", description = "获取切换配置列表")
    @GetMapping("/homePageSwitchConfig")
    public Result<List<HomePageSwitchConfigDTO>> homePageSwitchConfig() {
        return Result.success(homePageService.getHomePageSwitchConfig());
    }

    @Operation(operationId = "getBackgroundConfig", summary = "获取DIY背景配置信息", description = "获取DIY背景配置信息")
    @GetMapping("/backgroundConfig")
    public Result<BackgroundConfigDTO> getHomePageItemConfig(@Parameter(description = "配置id") String configId) {
        return Result.success(homePageService.getBackgroundConfig(configId));
    }

    @Operation(operationId = "checkNetwork_HomePageRest", summary = "检查访问是否在外网 true-外网 false-内网", description = "检查是否是外网 true-外网 false-内网")
    @GetMapping("/checkNetwork")
    public Result<Boolean> checkNetwork() {
        HttpServletRequest request = WebUtil.getRequest();
        // 非HTTP请求过来，拿不到request抛出异常
        if (Objects.isNull(request)) {
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_ILLEGAL_ENTRY_TYPE);
        }
        // IPv4地址正则表达式
        String regex = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";
        // 编译regex这个正则表达式，得到代表此正则表达式的对象
        Pattern pattern = Pattern.compile(regex);
        // 看data数据里面有没有和该正则表达式相匹配的内容
        Matcher m = pattern.matcher(request.getServerName());
        // 匹配器的find方法若返回true，则客户机提交的数据里面有和正则表达式相匹配的内容
        if (m.find()) {
            return Result.success(false);
        }
        return Result.success(true);
    }

}