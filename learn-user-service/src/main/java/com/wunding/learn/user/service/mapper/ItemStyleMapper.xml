<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.user.service.mapper.ItemStyleMapper">

    <!-- 开启二级缓存 -->
    <!--
        <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.user.service.mapper.ItemStyleMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.user.service.model.ItemStyle">
        <!--@Table item_style-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="code" jdbcType="VARCHAR"
            property="code"/>
        <result column="name" jdbcType="VARCHAR"
            property="name"/>
        <result column="proportion" jdbcType="VARCHAR"
            property="proportion"/>
        <result column="type" jdbcType="INTEGER"
            property="type"/>
        <result column="apply_area" jdbcType="VARCHAR"
            property="applyArea"/>
        <result column="server" jdbcType="VARCHAR"
            property="server"/>
        <result column="is_available" jdbcType="INTEGER"
            property="isAvailable"/>
        <result column="max_size" jdbcType="INTEGER"
            property="maxSize"/>
        <result column="min_size" jdbcType="INTEGER"
            property="minSize"/>
        <result column="is_del" jdbcType="INTEGER"
            property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
            property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
            property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
            property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
            property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id ,code ,name ,proportion ,type
        ,apply_area ,server ,is_available , max_size ,min_size
        ,is_del ,create_by ,create_time ,update_by ,update_time
    </sql>

</mapper>
