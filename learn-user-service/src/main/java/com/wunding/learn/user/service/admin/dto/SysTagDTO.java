package com.wunding.learn.user.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * </p> 系统标签对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">guoyong</a>
 * @since 2025/5/13 15:44
 */
@Data
@Schema(name = "SysTagDTO", description = "系统标签DTO")
public class SysTagDTO implements Serializable {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;

    /**
     * 标签分类id
     */
    @Schema(description = "标签分类id")
    private String tagClassifyId;

    /**
     * 标签分类名称
     */
    @Schema(description = "标签分类名称")
    private String tagClassifyName;

    /**
     * 创建方式 0-随课程分类创建  1-手动创建
     */
    @Schema(description = "创建方式 0-随课程分类创建  1-手动创建")
    private Integer createType;

    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String tagName;

    /**
     * 是否输入推荐 0-否 1-是
     */
    @Schema(description = "是否输入推荐 0-否 1-是")
    private Integer isRecommend;

    /**
     * 用户是否自选 0-否 1-是
     */
    @Schema(description = "用户是否自选 0-否 1-是")
    private Integer isOptional;

    /**
     * 是否前端展示 0-否 1-是
     */
    @Schema(description = "是否前端展示 0-否 1-是")
    private Integer isShow;

    /**
     * 前端筛选 0-否 1-是
     */
    @Schema(description = "前端筛选 0-否 1-是")
    private Integer clientDisplay;

    /**
     * 默认方式 0-否 1-初始化默认
     */
    @Schema(description = "默认方式 0-否 1-初始化默认")
    private Integer defaultType;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer sortNo;

    /**
     * 是否启用 0-否 1-是
     */
    @Schema(description = "是否启用 0-否 1-是")
    private Integer isAvailable;

}
