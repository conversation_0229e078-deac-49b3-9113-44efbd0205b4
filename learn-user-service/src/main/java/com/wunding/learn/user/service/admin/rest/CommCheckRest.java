package com.wunding.learn.user.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.service.admin.dto.ApiListQuery;
import com.wunding.learn.user.service.model.Api;
import com.wunding.learn.user.service.service.ICommCheckService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 管理端公共后端校验接口
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">z<PERSON><PERSON><PERSON><PERSON></a>
 * @date 2024/9/6 16:40
 */
@RestController
@RequestMapping("${module.user.contentPath:/}commCheck")
@Tag(description = "管理端公共后端校验接口", name = "CommCheckRest")
public class CommCheckRest {

    @Resource
    ICommCheckService commCheckService;

    @GetMapping("/checkIdCard/{idCard}")
    @Operation(operationId = "commCheck_checkIdCard", summary = "校验身份证号码是否正确", description = "校验身份证号码是否正确")
    public Result<Boolean> checkIdCard(@Parameter(description = "身份证号码") @PathVariable("idCard") String idCard) {
        return Result.success(commCheckService.checkIdCard(idCard));
    }

}
