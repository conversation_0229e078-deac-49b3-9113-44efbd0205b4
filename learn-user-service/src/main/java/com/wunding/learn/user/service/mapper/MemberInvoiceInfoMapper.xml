<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.user.service.mapper.MemberInvoiceInfoMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.user.service.mapper.MemberInvoiceInfoMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.user.service.model.MemberInvoiceInfo">
            <!--@Table member_invoice_info-->
                    <result column="id" jdbcType="VARCHAR"
                            property="id"/>
                    <result column="company_name" jdbcType="VARCHAR"
                            property="companyName"/>
                    <result column="taxpayer_identification_no" jdbcType="VARCHAR"
                            property="taxpayerIdentificationNo"/>
                    <result column="account_bank" jdbcType="VARCHAR"
                            property="accountBank"/>
                    <result column="account_no" jdbcType="VARCHAR"
                            property="accountNo"/>
                    <result column="address" jdbcType="VARCHAR"
                            property="address"/>
                    <result column="phone" jdbcType="VARCHAR"
                            property="phone"/>
                    <result column="biz_type" jdbcType="TINYINT"
                            property="bizType"/>
                    <result column="biz_id" jdbcType="VARCHAR"
                            property="bizId"/>
                    <result column="is_del" jdbcType="TINYINT"
                            property="isDel"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, company_name, taxpayer_identification_no, account_bank, account_no, address, phone, biz_type, biz_id, is_del, create_by, create_time, update_by, update_time
        </sql>

</mapper>
