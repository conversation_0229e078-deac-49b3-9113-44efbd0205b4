package com.wunding.learn.user.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 公共表单资源关联配置查询对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON><PERSON><PERSON></a>
 * @date 2024/6/13 20:46
 */
@Data
@Schema(name = "CommonFormTemplateRelevanceQuery", description = "公共表单资源关联配置查询对象")
public class CommonFormTemplateRelevanceQuery extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;

    /**
     * 模板id
     */
    @Schema(description = "模板id")
    private String formTemplateId;


    /**
     * 关联模块
     */
    @Schema(description = "关联模块")
    private String model;


    /**
     * 关联资源id
     */
    @Schema(description = "关联资源id")
    private String resourceId;


    /**
     * 关联资源名称
     */
    @Schema(description = "关联资源名称")
    private String resourceName;


    /**
     * 关联表单模板类型(RelevanceTypeEnums)
     */
    @Schema(description = "关联表单模板类型")
    private String relevanceType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
