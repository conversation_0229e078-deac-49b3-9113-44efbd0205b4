package com.wunding.learn.user.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.user.service.model.HomeDiyMenu;

/**
 * <p> DIY菜单表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
    * @since 2023-07-11
 */
public interface IHomeDiyMenuService extends IService<HomeDiyMenu> {

    /**
     *  获取DIY配置
     * @param configId
     * @param menuType
     * @return
     */
    HomeDiyMenu findDiyMenuByQuery(String configId, String menuType);

    /**
     * 获取diy配置信息
     * @param server 服务端
     * @param configId 配置id
     * @param menuType 中部菜单home
     * @return
     */
    HomeDiyMenu findUserDiyMenu(String server, String configId, String menuType);
}
