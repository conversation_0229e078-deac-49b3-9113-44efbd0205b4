package com.wunding.learn.user.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @since 2022-12-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
        @Accessors(chain = true)
@TableName("sys_user_org_relation")
@Schema(description = "UserOrgRelation对象", name = "UserOrgRelation")
public class UserOrgRelation implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableField("user_id")
    private String userId;


    /**
     * 组织id
     */
    @Schema(description = "组织id")
    @TableField("org_id")
    private String orgId;


    /**
     * 所在部门账号
     */
    @Schema(description = "所在部门账号")
    @TableField("emp_no")
    private String empNo;


    /**
     * 0=一般关联部门  1=主要关联部门
     */
    @Schema(description = "0=一般关联部门  1=主要关联部门")
    @TableField("relation_type")
    private Integer relationType;


    /**
     * 关联时间
     */
    @Schema(description = "关联时间")
    @TableField("relation_time")
    private Date relationTime;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
