package com.wunding.learn.user.service.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.wunding.learn.user.api.dto.AddressDTO;
import com.wunding.learn.user.api.service.AddressFeign;
import com.wunding.learn.user.service.model.SysAddress;
import com.wunding.learn.user.service.service.ISysAddressService;
import java.util.List;
import java.util.Objects;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AddressFeignImpl implements AddressFeign {

    @Resource
    private ISysAddressService sysAddressService;

    @Override
    public AddressDTO getById(String id) {
        SysAddress sysAddress = sysAddressService.getById(id);
        if (Objects.isNull(sysAddress)){
            return null;
        }
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setId(sysAddress.getRegionId());
        addressDTO.setName(sysAddress.getRegionName());
        return addressDTO;
    }

    @Override
    public AddressDTO getByName(String name) {
        List<SysAddress> list = sysAddressService.list(
            new LambdaQueryWrapper<SysAddress>().like(SysAddress::getRegionName, name));
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        SysAddress sysAddress = list.get(0);
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setId(sysAddress.getRegionId());
        addressDTO.setName(sysAddress.getRegionName());
        return addressDTO;
    }
}
