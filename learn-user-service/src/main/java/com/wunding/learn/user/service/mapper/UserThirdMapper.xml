<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.user.service.mapper.UserThirdMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.user.service.mapper.UserThirdMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.user.service.model.UserThird">
            <!--@Table sys_user_third-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="user_sync_id" jdbcType="VARCHAR"
                            property="userSyncId"/>
                    <result column="user_id" jdbcType="VARCHAR"
                            property="userId"/>
                    <result column="user_type" jdbcType="INTEGER"
                            property="userType"/>
                    <result column="is_bind" jdbcType="TINYINT"
                            property="isBind"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, user_sync_id, user_id, user_type, is_bind, create_by, create_time, update_by, update_time
        </sql>

        <select id="listByCondition" resultType="com.wunding.learn.user.service.dto.UserThirdDTO" useCache="false">
        select user_t.id, user_t.user_sync_id, user_t.user_id, user_t.user_type, sys_user.login_name from sys_user_third user_t join sys_user  on user_t.user_id = sys_user.id
            where
            sys_user.is_del = 0
            <if test="userType != null">
            and user_t.user_type = #{userType}
            </if>
        </select>

        <select id="getLocalUserIds" resultType="string">
         select u.id from sys_user u where is_del = 0 and u.is_available = 1 and u.login_name != 'admin' and u.id not in(select user_id from  sys_user_third)
        </select>

</mapper>
