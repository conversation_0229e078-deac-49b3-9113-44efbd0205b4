package com.wunding.learn.user.service.biz;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.category.dto.CategoryResultDTO;
import com.wunding.learn.common.dto.SysCategoryDTO;
import com.wunding.learn.common.dto.SysCategorySaveDTO;
import com.wunding.learn.common.query.SysCategoryQuery;
import com.wunding.learn.user.service.admin.query.OrgCateQuery;
import com.wunding.learn.user.service.client.dto.CategorySimpleDTO;
import java.util.List;

/**
 * <p> 分类 业务服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2024-03-14
 */
public interface ICategoryBiz {

    /**
     * 删除分类
     *
     * @param ids 分类id
     */
    void deleteCategory(String ids);

    /**
     * 获取指定类别一级分类
     *
     * @param type {@link com.wunding.learn.common.enums.other.CategoryTypeEnum}
     * @return {@link List}<{@link CategoryResultDTO}>
     */
    List<CategoryResultDTO> findSpecifyCateType(String type);

    /**
     * 获取组织关联分类列表
     *
     * @param query {@link OrgCateQuery}
     * @return {@link List}<{@link SysCategoryDTO}>
     */
    List<SysCategoryDTO> queryOrgCateList(OrgCateQuery query);
    
    /**
     * 通过分类id获取分类信息
     * @param id
     * @return
     */
    CategorySimpleDTO findCategoryById(String id);

    /**
     * 获取类别列表
     *
     * @param type 类型
     * @return {@link List }<{@link CategoryResultDTO }>
     */
    List<CategoryResultDTO> getCategoryList(String type);

    /**
     * 分页查找类别管理列表
     *
     * @param sysCategoryQuery sys类别查询
     * @return {@link PageInfo }<{@link SysCategoryDTO }>
     */
    PageInfo<SysCategoryDTO> findCategoryManageListByPage(SysCategoryQuery sysCategoryQuery);

    /**
     * 保存或更新类别
     *
     * @param sysCategorySaveDTO 系统类别保存数据
     */
    void saveOrUpdateCategory(SysCategorySaveDTO sysCategorySaveDTO);

    /**
     * 保存或更新类别v2
     *
     * @param sysCategorySaveDTO 系统类别保存数据
     */
    void saveOrUpdateCategoryV2(SysCategorySaveDTO sysCategorySaveDTO);
}
