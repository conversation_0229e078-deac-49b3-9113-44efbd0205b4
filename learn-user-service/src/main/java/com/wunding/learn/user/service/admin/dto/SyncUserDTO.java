package com.wunding.learn.user.service.admin.dto;

import com.wunding.learn.user.api.dto.OrgSimpleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 同步用户dto对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-08-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "SyncUserDTO", description = "同步用户dto对象")
public class SyncUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id，第三方同步用户id")
    @NotBlank
    private String userId;

    @Schema(description = "账号")
    private String employeeNo;

    @Schema(description = "账号")
    @NotBlank
    private String loginName;

    @Schema(description = "姓名")
    @NotBlank
    private String fullName;

    @Schema(description = "昵称")
    private String nikeName;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "所属岗位身份Code，如果不存在，默认为默认岗位8")
    @NotBlank
    private String postCode;

    @Schema(description = "所属部门Code，如果不存在，默认为根组织0")
    @NotBlank
    private String orgCode;

    @Schema(description = "工作区域")
    private String workingArea;

    @Schema(description = "姓")
    private String firstName;

    @Schema(description = "名")
    private String lastName;

    @Schema(description = "用户全名的拼音")
    private String pinyin;

    @Schema(description = "性别 1：男 2：女")
    private Integer sex;

    @Schema(description = "工作电话")
    private String workPhone;

    @Schema(description = "手机")
    private String telephone;

    @Schema(description = "短号")
    private String shortMobile;

    @Schema(description = "邮件")
    private String email;

    @Schema(description = "是否锁定")
    @NotNull
    private Integer isLock;

    @Schema(description = "是否可用")
    @NotNull
    private Integer isAvailable;

    @Schema(description = "是否删除")
    @NotNull
    private Integer isDel;

    @Schema(description = "是否为超级管理员")
    private Integer isSuper;

    @Schema(description = "等级")
    private String userLevelId;

    @Schema(description = "是否专家")
    private Integer isExpert;

    @Schema(description = "是否部门终审员 0否 1是")
    private Integer lsDepartAdmin;

    @Schema(description = "生日")
    private Date birthday;

    @Schema(description = "入职日期")
    private Date joinDate;

    @Schema(description = "人员在部门排序，同步记录用到")
    private String order;

    @Schema(description = "更新时是否需要更新登录账号：true:是，false:否，默认值为否false")
    @NotNull
    private Boolean isUpdateLoginName;

    @Schema(description = "所属部门Code列表")
    @NotNull
    private List<OrgSimpleInfo> orgCodeList;

    /**
     * 企业微信类型（支持配置多个企业微信）：0-澳美点击 1-奥美领航
     */
    @Schema(description = "企业微信类型（支持配置多个企业微信）：0-澳美点击 1-奥美领航")
    private Integer multiChannelType;

    /**
     * 职级ID
     */
    @Schema(description = "职级ID")
    private String identityJobLevelId;

    /**
     * 上司ID
     */
    @Schema(description = "上司ID")
    private String identitySuperiorId;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    private String idNumber;
}
