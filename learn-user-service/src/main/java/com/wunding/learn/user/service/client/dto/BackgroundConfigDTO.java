package com.wunding.learn.user.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p> DIY背景配置对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2025/6/11
 */
@Data
@Schema(name = "BackgroundConfigDTO", description = "DIY背景配置对象")
public class BackgroundConfigDTO {


    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "背景图片平铺方式 0-XY轴重复 1-X轴重复 2-Y轴重复（左右居中） 3-不重复")
    private Integer backgroundRepeat;

    @Schema(description = "背景图片")
    private String backgroundImageUrl;

    @Schema(description = "当前背景图优先显示 0-否 1-是")
    private Integer currentImgPriority;
}
