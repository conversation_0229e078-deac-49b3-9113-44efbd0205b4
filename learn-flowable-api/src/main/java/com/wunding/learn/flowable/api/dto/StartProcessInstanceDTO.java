package com.wunding.learn.flowable.api.dto;

import com.wunding.learn.common.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> 启动流程实例dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-10-11
 */
@Data
@Accessors(chain = true)
@Schema(name = "StartProcessInstanceDTO", description = "启动流程实例dto")
public class StartProcessInstanceDTO {

    @Schema(description = "流程定义id")
    private String definitionId;

    /**
     * 流程实例编号
     */
    @Schema(description = "流程实例编号")
    private String processCode;

    /**
     * 定义类型
     */
    @Schema(description = "流程定义类型")
    private String definitionType;

    @Schema(description = "资源id")
    @NotEmpty(message = "资源id不能为空")
    private String resourceId;

    @Schema(description = "资源名称")
    private String resourceName;

    /**
     * 资源类型 {@link ResourceTypeEnum}
     */
    @Schema(description = "资源类型")
    private String resourceType;

    @Schema(description = "被审核人,如果是针对人员审核")
    private String userId;

    @Schema(description = "申请人,发起审核申请人")
    private String applicantUserId;

    /**
     * 审核申请类型，0-新建申请，1-修改申请
     */
    @Schema(description = "审核申请类型，0-新建申请，1-修改申请")
    private Integer processApplyType;

    @Schema(description = "进行审批的组织")
    private String orgId;

    @Schema(description = "审核原因(初始化则是初始化原因)")
    private String comments;

    @Schema(description = "业务数据可以供后续查询使用")
    private String businessData1;

    @Schema(description = "审核原因(初始化则是初始化原因)")
    private String businessData2;

    @Schema(description = "实例扩展信息")
    private String extraInfo;

}
