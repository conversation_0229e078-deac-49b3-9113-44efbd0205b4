package com.wunding.learn.evaluation.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
@Data
@Accessors(chain = true)
public class LearnerEvaluateInfoDTO implements Serializable {

    public static final long serialVersionUID = 1L;

    /**
     * 评估id
     */
    @Schema(description = "评估id")
    private String id;

    /**
     * 评估标题
     */
    @Schema(description = "评估标题")
    private String evalName;

    /**
     * 评估开始时间
     */
    @Schema(description = "评估开始时间")
    private Date startTime;

    /**
     * 评估截止时间
     */
    @Schema(description = "评估截止时间")
    private Date endTime;

    /**
     * 参与评估人数
     */
    @Schema(description = "参与评估人数")
    private Integer finishedCount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String memo;

    /**
     * 题目总数
     */
    @Schema(description = "题目总数")
    private Integer questionCount;

    /**
     * 我完成的时间
     */
    @Schema(description = "我完成的时间")
    private Date finishedTime;

    /**
     * 是否可以查看结果
     */
    @Schema(description = "是否可以查看结果 0：不可以 1：可以")
    private Integer viewResult;

    /**
     * 我是否完成
     */
    @Schema(description = "我是否完成 0：未完成 1：已完成")
    private Integer finished;

    /**
     * 评估类型 1-学习项目应用 2-讲师应用添加
     */
    @Schema(description = "评估类型 1-学习项目 2-讲师")
    private Integer evaluationType;

    /**
     * 评估对象
     */
    @Schema(description = "评估对象")
    private String evaluationObject;
}
