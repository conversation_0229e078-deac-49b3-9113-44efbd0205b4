package com.wunding.learn.evaluation.api.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Author: suchenyu @Date: 2022/12/8 11:10
 */
@Data
@Schema(name = "EvaluationQueryDTO", description = "评估列表查询对象")
public class EvaluationQueryDTO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "评估名称")
    private String evalName;

    @Schema(description = "评估类型")
    private Integer evaluationType;

    @Schema(description = "是否发布")
    private Integer published;

    @Schema(description = "评估对象集合")
    private List<String> evaluationObjectIds;

    @Schema(description = "评估开始时间")
    private String startTime;

    @Schema(description = "评估结束时间")
    private String endTime;
}
