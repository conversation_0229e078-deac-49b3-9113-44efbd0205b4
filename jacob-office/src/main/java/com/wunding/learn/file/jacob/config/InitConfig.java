package com.wunding.learn.file.jacob.config;

import com.jacob.com.LibraryLoader;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class InitConfig {

    @Resource
    private MinioConfig minioConfig;

    @PostConstruct
    public void loadDriver() {
        log.info("begin loadDriver");
        String dllName = LibraryLoader.getPreferredDLLName();
        log.info("loadDriver dllName {}", dllName);
        File temporaryDll = null;
        try {
            temporaryDll = File.createTempFile(dllName, ".dll", new File(minioConfig.getLocation()));
        } catch (IOException e) {
            log.error("loadDriver error", e);
        }
        assert temporaryDll != null;
        try (FileOutputStream outputStream = new FileOutputStream(temporaryDll);
            InputStream inputStream = Thread.currentThread().getContextClassLoader()
                .getResourceAsStream("lib/" + dllName + ".dll")) {

            if (inputStream == null) {
                log.warn("loadDriver inputStream is null");
                return;
            }
            byte[] array = new byte[8192];
            for (int i = inputStream.read(array); i != -1; i = inputStream
                .read(array)) {
                outputStream.write(array, 0, i);
            }
            // 这一行不能少,temporaryDll注册了钩子监听文件,如果在注册之前不关闭流则会导致文件被进程占用,而报错
            IOUtils.close(outputStream);
            temporaryDll.deleteOnExit();
            System.setProperty(LibraryLoader.JACOB_DLL_PATH, temporaryDll.getPath());
            LibraryLoader.loadJacobLibrary();
        } catch (IOException e) {
            log.error("loadDriver error", e);
        }
    }
}
