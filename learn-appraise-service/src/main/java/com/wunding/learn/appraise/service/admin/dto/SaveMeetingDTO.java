package com.wunding.learn.appraise.service.admin.dto;

import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 保存答辩会议对象
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "SaveMeetingDTO", description = "保存答辩会议对象")
public class SaveMeetingDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 举办组织
     */
    @Schema(description = "举办组织")
    @Length(max = 80, message = "举办组织不可超过80个字")
    private String sponsorOrg;

    @Schema(description = "会议编码")
    private String code;


    @Schema(description = "是否线上")
    private Integer isOnline;
    /**
     * 地点
     */
    @Schema(description = "地点")
    @Length(max = 80, message = "地点/会议链接")
    private String address;

    /**
     * 任职资格ID
     */
    @Schema(description = "任职资格ID")
    private String jobQualificationId;

    /**
     * 任职资格名称
     */
    @Schema(description = "任职资格名称")
    private String jobQualificationName;

    /**
     * 认证岗位ID
     */
    @Schema(description = "认证岗位ID")
    private String postId;

    /**
     * 认证岗位名称
     */
    @Schema(description = "认证岗位名称")
    private String postName;


    /**
     * 标题
     */
    @Schema(description = "标题", required = true)
    @NotBlank(message = "标题不可为空")
    @Length(max = 80, message = "标题不可超过80个字")
    private String title;

    /**
     * 答辩会议封面
     */
    @Schema(description = "答辩会议封面", required = true)
    private NamePath coverImage;

    /**
     * 答辩会议说明
     */
    @Schema(description = "答辩会议说明")
    @NotBlank(message = "答辩会议说明不可为空")
    @Length(max = 500, message = "答辩会议说明不可超过500个字")
    private String activityDescription;

    /**
     * 评估模板的地址
     */
    @Schema(description = "评估模板的地址")
    private String excelFilePath;

    @Schema(description = "立即发布 0不 1是")
    @NotNull(message = "立即发布字段不可为空")
    private Integer isPublish;

    /**
     * pushDTO
     */
    @Schema(description = "推送通知设置")
    private PushNoticeSetDTO pushNoticeSetDTO;

    @Schema(description = "来源 0-本身 1-学习项目 2专题")
    private Integer isTrain;

    @Schema(description = "创建、归属部门Id")
    private String orgId;

    @Schema(description = "创建、归属部门Name")
    private String orgName;

    @Schema(description = "答辩会议开始时间")
    @NotNull(message = "答辩会议开始时间不可为空")
    private Date startTime;

    @Schema(description = "答辩会议结束时间")
    @NotNull(message = "答辩会议结束时间不可为空")
    private Date endTime;

    @Schema(description = "参加人数")
    private Integer peopleCount;

    @Schema(description = "通过分数")
    BigDecimal score;

    @Schema(description = "下发范围基本信息")
    private ViewLimitBaseInfoDTO limit;

    @Schema(description = "下发范围方案id")
    private Long programmeId;

    @Schema(description = "休息开始时间")
    Date breakStartTime;

    @Schema(description = "休息结束时间")
    Date breakEndTime;

    @Schema(description = "答辩时长")
    Integer duration;

    @Schema(description = "间隔时长")
    Integer intervalDuration;

}
