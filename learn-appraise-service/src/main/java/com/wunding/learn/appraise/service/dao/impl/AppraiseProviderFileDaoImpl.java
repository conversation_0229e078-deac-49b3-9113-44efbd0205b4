package com.wunding.learn.appraise.service.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.appraise.service.dao.AppraiseProviderFileDao;
import com.wunding.learn.appraise.service.mapper.AppraiseProviderFileMapper;
import com.wunding.learn.appraise.service.model.AppraiseProviderFile;
import com.wunding.learn.common.aop.log.annotation.Log;
import com.wunding.learn.common.aop.log.annotation.Log.TargetType;
import com.wunding.learn.common.aop.log.annotation.Log.Type;
import org.springframework.stereotype.Repository;

/**
 * <p>评价材料 数据操作封装类</p>
 *
 * <AUTHOR> href="<EMAIL>">李恒</a>
 * {@code @date} 2024-02-08
 */
@Repository("appraiseProviderFileDao")
public class AppraiseProviderFileDaoImpl extends
    ServiceImpl<AppraiseProviderFileMapper, AppraiseProviderFile> implements AppraiseProviderFileDao {


    @Override
    @Log(type = Type.DELETE, targetId = "#fileId", targetName = "#fileName", targetType = TargetType.APPRAISE_PROVIDER_FILE)
    public void delAppraiseProviderFile(String fileId, String fileName) {
        removeById(fileId);
    }
}
