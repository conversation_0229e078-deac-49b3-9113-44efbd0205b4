package com.wunding.learn.train.api.service;

import com.wunding.learn.train.api.dto.SupplierInfoFeignDTO;
import java.util.Collection;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 供应商管理
 * <AUTHOR>
 * @date 2023/3/27 14:08
 */
@FeignClient(url = "${learn.service.learn-train-service}", name = "learn-train-service", path = "/train")
public interface SupplierFeign {

    @GetMapping("/getSupplierListByIds")
    List<SupplierInfoFeignDTO> getSupplierListByIds(@RequestBody Collection<String> ids);
}
