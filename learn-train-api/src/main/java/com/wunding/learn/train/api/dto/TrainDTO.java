package com.wunding.learn.train.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/3/6 14:16
 */
@Data
@Schema(name = "TrainDTO", description = "培训项目对象")
public class TrainDTO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "可见范围方案id")
    private Long programmeId;

    /**
     * 培训项目编码
     */
    @Schema(description = "培训项目编码")
    private String trainNo;

    /**
     * 培训项目名称
     */
    @Schema(description = "培训项目名称")
    private String trainName;

    /**
     * 培训类别id，分类管理ID
     */
    @Schema(description = "培训类别id，分类管理ID", hidden = true)
    private Integer trainTypeId;

    @Schema(description = "项目类型名称")
    private String trainTypeName;

    /**
     * 项目开始时间
     */
    @Schema(description = "项目开始时间")
    private Date startTime;

    /**
     * 项目结束时间
     */
    @Schema(description = "项目结束时间")
    private Date endTime;

    /**
     * 举报组织ID
     */
    @Schema(description = "举报组织名称")
    private String holdOrgName;

    /**
     * 是否发布
     */
    @Schema(description = "是否发布  0=未发布  1=发布")
    private Integer isPublish;

    /**
     * 培训管理员
     */
    @Schema(description = "培训管理员", hidden = true)
    private String manageUserId;


    /**
     * 培训管理员
     */
    @Schema(description = "培训管理员用户名")
    private String manageUserName;

    /**
     * 创建人
     */
    @Schema(description = "创建人ID", hidden = true)
    private String createBy;

    /**
     * 创建人
     */
    @Schema(description = "创建人用户名")
    private String createByUserName;

    /**
     * 创建人部门id
     */
    @Schema(description = "创建人部门id", hidden = true)
    private String orgId;

    /**
     * 创建人部门名称
     */
    @Schema(description = "创建人部门名称")
    private String orgName;


    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    private Integer viewType;


    /**
     * 报名设置 1=公开，不需要报名 2=报名，人数限制 3=报名，人数不限制
     */
    @Schema(description = "报名设置  1=公开，不需要报名 2=报名，人数限制 3= 报名，人数不限制 ")
    private Integer applyType;

    @Schema(description = "计划ID")
    private String planId;
}
