<transformation>
  <info>
    <name>train_holding_statistics_train</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>learnBusinessHost</name>
        <default_value>************</default_value>
        <description>统计-主机host</description>
      </parameter>
      <parameter>
        <name>learnBusinessName</name>
        <default_value>business_view</default_value>
        <description>统计-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnBusinessPassword</name>
        <default_value>123456</default_value>
        <description>统计-密码</description>
      </parameter>
      <parameter>
        <name>learnBusinessPort</name>
        <default_value>30020</default_value>
        <description>统计-端口</description>
      </parameter>
      <parameter>
        <name>learnBusinessUser</name>
        <default_value>root</default_value>
        <description>统计-用户名</description>
      </parameter>
      <parameter>
        <name>learnCourseHost</name>
        <default_value>************</default_value>
        <description>课程-主机host</description>
      </parameter>
      <parameter>
        <name>learnCourseName</name>
        <default_value>course</default_value>
        <description>课程-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnCoursePassword</name>
        <default_value>123456</default_value>
        <description>课程-密码</description>
      </parameter>
      <parameter>
        <name>learnCoursePort</name>
        <default_value>30020</default_value>
        <description>课程-端口</description>
      </parameter>
      <parameter>
        <name>learnCourseUser</name>
        <default_value>root</default_value>
        <description>课程-用户名</description>
      </parameter>
      <parameter>
        <name>learnEvaluationHost</name>
        <default_value>************</default_value>
        <description>评估-主机host</description>
      </parameter>
      <parameter>
        <name>learnEvaluationName</name>
        <default_value>evaluation</default_value>
        <description>评估-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnEvaluationPassword</name>
        <default_value>123456</default_value>
        <description>评估-密码</description>
      </parameter>
      <parameter>
        <name>learnEvaluationPort</name>
        <default_value>30020</default_value>
        <description>评估-端口</description>
      </parameter>
      <parameter>
        <name>learnEvaluationUser</name>
        <default_value>root</default_value>
        <description>评估-用户名</description>
      </parameter>
      <parameter>
        <name>learnLecturerHost</name>
        <default_value>************</default_value>
        <description>讲师-主机host</description>
      </parameter>
      <parameter>
        <name>learnLecturerName</name>
        <default_value>lecturer</default_value>
        <description>讲师-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnLecturerPassword</name>
        <default_value>123456</default_value>
        <description>讲师-密码</description>
      </parameter>
      <parameter>
        <name>learnLecturerPort</name>
        <default_value>30020</default_value>
        <description>讲师-端口</description>
      </parameter>
      <parameter>
        <name>learnLecturerUser</name>
        <default_value>root</default_value>
        <description>讲师-用户名</description>
      </parameter>
      <parameter>
        <name>learnPlanHost</name>
        <default_value>************</default_value>
        <description>培训计划-主机host</description>
      </parameter>
      <parameter>
        <name>learnPlanName</name>
        <default_value>plan</default_value>
        <description>培训计划-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnPlanPassword</name>
        <default_value>123456</default_value>
        <description>培训计划-密码</description>
      </parameter>
      <parameter>
        <name>learnPlanPort</name>
        <default_value>30020</default_value>
        <description>培训计划端口</description>
      </parameter>
      <parameter>
        <name>learnPlanUser</name>
        <default_value>root</default_value>
        <description>培训计划-用户名</description>
      </parameter>
      <parameter>
        <name>learnProjectHost</name>
        <default_value>************</default_value>
        <description>学习项目-主机host</description>
      </parameter>
      <parameter>
        <name>learnProjectName</name>
        <default_value>project</default_value>
        <description>学习项目-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnProjectPassword</name>
        <default_value>123456</default_value>
        <description>学习项目-密码</description>
      </parameter>
      <parameter>
        <name>learnProjectPort</name>
        <default_value>30020</default_value>
        <description>学习项目-端口</description>
      </parameter>
      <parameter>
        <name>learnProjectUser</name>
        <default_value>root</default_value>
        <description>学习项目-用户名</description>
      </parameter>
      <parameter>
        <name>learnTrainHost</name>
        <default_value>************</default_value>
        <description>培训-主机host</description>
      </parameter>
      <parameter>
        <name>learnTrainName</name>
        <default_value>train</default_value>
        <description>培训-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnTrainPassword</name>
        <default_value>123456</default_value>
        <description>培训-密码</description>
      </parameter>
      <parameter>
        <name>learnTrainPort</name>
        <default_value>30020</default_value>
        <description>培训-端口</description>
      </parameter>
      <parameter>
        <name>learnTrainUser</name>
        <default_value>root</default_value>
        <description>培训-用户名</description>
      </parameter>
      <parameter>
        <name>learnUserHost</name>
        <default_value>************</default_value>
        <description>用户-主机host</description>
      </parameter>
      <parameter>
        <name>learnUserName</name>
        <default_value>user</default_value>
        <description>用户-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnUserPassword</name>
        <default_value>123456</default_value>
        <description>用户-密码</description>
      </parameter>
      <parameter>
        <name>learnUserPort</name>
        <default_value>30020</default_value>
        <description>用户-端口</description>
      </parameter>
      <parameter>
        <name>learnUserUser</name>
        <default_value>root</default_value>
        <description>用户-用户名</description>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2023/05/12 11:13:15.079</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/05/12 11:17:47.821</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>learn_business</name>
    <server>${learnBusinessHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnBusinessName}</database>
    <port>${learnBusinessPort}</port>
    <username>${learnBusinessUser}</username>
    <password>${learnBusinessPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnBusinessPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_project</name>
    <server>${learnProjectHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnProjectName}</database>
    <port>${learnProjectPort}</port>
    <username>${learnProjectUser}</username>
    <password>${learnProjectPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnProjectPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>过滤记录</from>
      <to>分组</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组</from>
      <to>追加流</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 2</from>
      <to>追加流</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>将字段值设置为常量</from>
      <to>分组 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>追加流</from>
      <to>插入 / 更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查询学习项目</from>
      <to>过滤记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查询学习项目</from>
      <to>将字段值设置为常量</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>分组</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>org_id</name>
      </field>
      <field>
        <name>train_category_id</name>
      </field>
      <field>
        <name>month</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>train_num</aggregate>
        <subject>project_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>686</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>org_id</name>
      </field>
      <field>
        <name>train_category_id</name>
      </field>
      <field>
        <name>month</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>train_num</aggregate>
        <subject>project_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>880</xloc>
      <yloc>686</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>将字段值设置为常量</name>
    <type>SetValueConstant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <usevar>N</usevar>
    <fields>
      <field>
        <name>train_category_id</name>
        <value/>
        <mask/>
        <set_empty_string>Y</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>880</xloc>
      <yloc>542</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>插入 / 更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_business</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema>business_view</schema>
      <table>train_holding_statistics</table>
      <key>
        <name>org_id</name>
        <field>org_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>train_category_id</name>
        <field>train_category_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>month</name>
        <field>month</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>org_id</name>
        <rename>org_id</rename>
        <update>Y</update>
      </value>
      <value>
        <name>train_category_id</name>
        <rename>train_category_id</rename>
        <update>Y</update>
      </value>
      <value>
        <name>month</name>
        <rename>month</rename>
        <update>Y</update>
      </value>
      <value>
        <name>train_num</name>
        <rename>train_num</rename>
        <update>Y</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>1006</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>查询学习项目</name>
    <type>TableInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select a.org_id, a.train_category_id, CAST(DATE_ADD(a.start_time, interval - day(a.start_time) + 1 day) as DATE) month, a.id project_id
from project a
where a.is_publish = 1
  and a.is_del = 0
  and a.start_time >= last_day(date_sub(curdate(), interval 3 MONTH)) + interval 1 day
  and a.start_time &lt;= last_day(curdate()) + interval 1 day - interval 1 second
  and a.referenced_id = ''
order by a.id</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查询学习项目</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>train_category_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查询学习项目</origin>
        <comments>train_category_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Date</type>
        <storagetype>normal</storagetype>
        <name>month</name>
        <length>-1</length>
        <precision>-1</precision>
        <origin>查询学习项目</origin>
        <comments>month</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>project_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查询学习项目</origin>
        <comments>project_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>336</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>过滤记录</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to>分组</send_true_to>
    <send_false_to/>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>train_category_id</leftvalue>
        <function>&lt;></function>
        <rightvalue/>
        <value>
          <name>constant</name>
          <type>String</type>
          <text>''</text>
          <length>-1</length>
          <precision>-1</precision>
          <isnull>N</isnull>
          <mask/>
        </value>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>542</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>追加流</name>
    <type>Append</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <head_name>分组</head_name>
    <tail_name>分组 2</tail_name>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>846</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
