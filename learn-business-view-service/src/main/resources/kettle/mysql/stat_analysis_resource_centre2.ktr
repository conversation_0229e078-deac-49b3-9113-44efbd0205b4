<transformation>
  <info>
    <name>stat_analysis_resource_centre2</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>learnAppraiseHost</name>
        <default_value>************</default_value>
        <description>评价数据库主机</description>
      </parameter>
      <parameter>
        <name>learnAppraiseName</name>
        <default_value>appraise</default_value>
        <description>评价数据库名称</description>
      </parameter>
      <parameter>
        <name>learnAppraisePassword</name>
        <default_value>learnTest</default_value>
        <description>评价数据库密码</description>
      </parameter>
      <parameter>
        <name>learnAppraisePort</name>
        <default_value>30306</default_value>
        <description>评价数据库端口</description>
      </parameter>
      <parameter>
        <name>learnAppraiseUser</name>
        <default_value>wdxuexi</default_value>
        <description>评价数据库用户</description>
      </parameter>
      <parameter>
        <name>learnCertificationHost</name>
        <default_value>************</default_value>
        <description>证书数据库主机</description>
      </parameter>
      <parameter>
        <name>learnCertificationName</name>
        <default_value>certification</default_value>
        <description>证书数据库名称</description>
      </parameter>
      <parameter>
        <name>learnCertificationPassword</name>
        <default_value>learnTest</default_value>
        <description>证书数据库密码</description>
      </parameter>
      <parameter>
        <name>learnCertificationPort</name>
        <default_value>30306</default_value>
        <description>证书数据库端口</description>
      </parameter>
      <parameter>
        <name>learnCertificationUser</name>
        <default_value>wdxuexi</default_value>
        <description>证书数据库用户</description>
      </parameter>
      <parameter>
        <name>learnCommentHost</name>
        <default_value>************</default_value>
        <description>评论数据库主机</description>
      </parameter>
      <parameter>
        <name>learnCommentName</name>
        <default_value>comment</default_value>
        <description>评论数据库名称</description>
      </parameter>
      <parameter>
        <name>learnCommentPassword</name>
        <default_value>learnTest</default_value>
        <description>评论数据库密码</description>
      </parameter>
      <parameter>
        <name>learnCommentPort</name>
        <default_value>30306</default_value>
        <description>评论数据库端口</description>
      </parameter>
      <parameter>
        <name>learnCommentUser</name>
        <default_value>wdxuexi</default_value>
        <description>评论数据库用户</description>
      </parameter>
      <parameter>
        <name>learnCourseHost</name>
        <default_value>************</default_value>
        <description>课程数据库主机</description>
      </parameter>
      <parameter>
        <name>learnCourseName</name>
        <default_value>course</default_value>
        <description>课程数据库名称</description>
      </parameter>
      <parameter>
        <name>learnCoursePassword</name>
        <default_value>learnTest</default_value>
        <description>课程数据库密码</description>
      </parameter>
      <parameter>
        <name>learnCoursePort</name>
        <default_value>30306</default_value>
        <description>课程数据库端口</description>
      </parameter>
      <parameter>
        <name>learnCourseUser</name>
        <default_value>wdxuexi</default_value>
        <description>课程数据库用户</description>
      </parameter>
      <parameter>
        <name>learnEvaluationHost</name>
        <default_value>************</default_value>
        <description>评估数据库主机</description>
      </parameter>
      <parameter>
        <name>learnEvaluationName</name>
        <default_value>evaluation</default_value>
        <description>评估数据库名称</description>
      </parameter>
      <parameter>
        <name>learnEvaluationPassword</name>
        <default_value>learnTest</default_value>
        <description>评估数据库密码</description>
      </parameter>
      <parameter>
        <name>learnEvaluationPort</name>
        <default_value>30306</default_value>
        <description>评估数据库端口</description>
      </parameter>
      <parameter>
        <name>learnEvaluationUser</name>
        <default_value>wdxuexi</default_value>
        <description>评估数据库用户</description>
      </parameter>
      <parameter>
        <name>learnExamHost</name>
        <default_value>************</default_value>
        <description>考试数据库主机</description>
      </parameter>
      <parameter>
        <name>learnExamName</name>
        <default_value>exam</default_value>
        <description>考试数据库名称</description>
      </parameter>
      <parameter>
        <name>learnExamPassword</name>
        <default_value>learnTest</default_value>
        <description>考试数据库密码</description>
      </parameter>
      <parameter>
        <name>learnExamPort</name>
        <default_value>30306</default_value>
        <description>考试数据库端口</description>
      </parameter>
      <parameter>
        <name>learnExamUser</name>
        <default_value>wdxuexi</default_value>
        <description>考试数据库用户</description>
      </parameter>
      <parameter>
        <name>learnExampleHost</name>
        <default_value>************</default_value>
        <description>案例库数据库主机</description>
      </parameter>
      <parameter>
        <name>learnExampleName</name>
        <default_value>example</default_value>
        <description>案例库数据库名称</description>
      </parameter>
      <parameter>
        <name>learnExamplePassword</name>
        <default_value>learnTest</default_value>
        <description>案例库数据库密码</description>
      </parameter>
      <parameter>
        <name>learnExamplePort</name>
        <default_value>30306</default_value>
        <description>案例库数据库端口</description>
      </parameter>
      <parameter>
        <name>learnExampleUser</name>
        <default_value>wdxuexi</default_value>
        <description>案例库数据库用户</description>
      </parameter>
      <parameter>
        <name>learnExcitationHost</name>
        <default_value>************</default_value>
        <description>激励数据库主机</description>
      </parameter>
      <parameter>
        <name>learnExcitationName</name>
        <default_value>excitation</default_value>
        <description>激励数据库名称</description>
      </parameter>
      <parameter>
        <name>learnExcitationPassword</name>
        <default_value>learnTest</default_value>
        <description>激励数据库密码</description>
      </parameter>
      <parameter>
        <name>learnExcitationPort</name>
        <default_value>30306</default_value>
        <description>激励数据库端口</description>
      </parameter>
      <parameter>
        <name>learnExcitationUser</name>
        <default_value>wdxuexi</default_value>
        <description>激励数据库用户</description>
      </parameter>
      <parameter>
        <name>learnForumHost</name>
        <default_value>************</default_value>
        <description>论坛数据库主机</description>
      </parameter>
      <parameter>
        <name>learnForumName</name>
        <default_value>forum</default_value>
        <description>论坛数据库名称</description>
      </parameter>
      <parameter>
        <name>learnForumPassword</name>
        <default_value>learnTest</default_value>
        <description>论坛数据库密码</description>
      </parameter>
      <parameter>
        <name>learnForumPort</name>
        <default_value>30306</default_value>
        <description>论坛数据库端口</description>
      </parameter>
      <parameter>
        <name>learnForumUser</name>
        <default_value>wdxuexi</default_value>
        <description>论坛数据库用户</description>
      </parameter>
      <parameter>
        <name>learnInfoHost</name>
        <default_value>************</default_value>
        <description>资讯数据库主机</description>
      </parameter>
      <parameter>
        <name>learnInfoName</name>
        <default_value>info</default_value>
        <description>资讯数据库名称</description>
      </parameter>
      <parameter>
        <name>learnInfoPassword</name>
        <default_value>learnTest</default_value>
        <description>资讯数据库密码</description>
      </parameter>
      <parameter>
        <name>learnInfoPort</name>
        <default_value>30306</default_value>
        <description>资讯数据库端口</description>
      </parameter>
      <parameter>
        <name>learnInfoUser</name>
        <default_value>wdxuexi</default_value>
        <description>资讯数据库用户</description>
      </parameter>
      <parameter>
        <name>learnLecturerHost</name>
        <default_value>************</default_value>
        <description>讲师数据库主机</description>
      </parameter>
      <parameter>
        <name>learnLecturerName</name>
        <default_value>lecturer</default_value>
        <description>讲师数据库名称</description>
      </parameter>
      <parameter>
        <name>learnLecturerPassword</name>
        <default_value>learnTest</default_value>
        <description>讲师数据库密码</description>
      </parameter>
      <parameter>
        <name>learnLecturerPort</name>
        <default_value>30306</default_value>
        <description>讲师数据库端口</description>
      </parameter>
      <parameter>
        <name>learnLecturerUser</name>
        <default_value>wdxuexi</default_value>
        <description>讲师数据库用户</description>
      </parameter>
      <parameter>
        <name>learnLiveHost</name>
        <default_value>************</default_value>
        <description>直播数据库主机</description>
      </parameter>
      <parameter>
        <name>learnLiveName</name>
        <default_value>live</default_value>
        <description>直播数据库名称</description>
      </parameter>
      <parameter>
        <name>learnLivePassword</name>
        <default_value>learnTest</default_value>
        <description>直播数据库密码</description>
      </parameter>
      <parameter>
        <name>learnLivePort</name>
        <default_value>30306</default_value>
        <description>直播数据库端口</description>
      </parameter>
      <parameter>
        <name>learnLiveUser</name>
        <default_value>wdxuexi</default_value>
        <description>直播数据库用户</description>
      </parameter>
      <parameter>
        <name>learnMarketHost</name>
        <default_value>************</default_value>
        <description>推广数据库主机</description>
      </parameter>
      <parameter>
        <name>learnMarketName</name>
        <default_value>market</default_value>
        <description>推广数据库名称</description>
      </parameter>
      <parameter>
        <name>learnMarketPassword</name>
        <default_value>learnTest</default_value>
        <description>推广数据库密码</description>
      </parameter>
      <parameter>
        <name>learnMarketPort</name>
        <default_value>30306</default_value>
        <description>推广数据库端口</description>
      </parameter>
      <parameter>
        <name>learnMarketUser</name>
        <default_value>wdxuexi</default_value>
        <description>推广数据库用户</description>
      </parameter>
      <parameter>
        <name>learnProjectHost</name>
        <default_value>************</default_value>
        <description>学习项目数据库主机</description>
      </parameter>
      <parameter>
        <name>learnProjectName</name>
        <default_value>project</default_value>
        <description>学习项目数据库名称</description>
      </parameter>
      <parameter>
        <name>learnProjectPassword</name>
        <default_value>learnTest</default_value>
        <description>学习项目数据库密码</description>
      </parameter>
      <parameter>
        <name>learnProjectPort</name>
        <default_value>30306</default_value>
        <description>学习项目数据库端口</description>
      </parameter>
      <parameter>
        <name>learnProjectUser</name>
        <default_value>wdxuexi</default_value>
        <description>学习项目数据库用户</description>
      </parameter>
      <parameter>
        <name>learnPromotedGameHost</name>
        <default_value>************</default_value>
        <description>闯关数据库主机</description>
      </parameter>
      <parameter>
        <name>learnPromotedGameName</name>
        <default_value>promotedgame</default_value>
        <description>闯关数据库名称</description>
      </parameter>
      <parameter>
        <name>learnPromotedGamePassword</name>
        <default_value>learnTest</default_value>
        <description>闯关数据库密码</description>
      </parameter>
      <parameter>
        <name>learnPromotedGamePort</name>
        <default_value>30306</default_value>
        <description>闯关数据库端口</description>
      </parameter>
      <parameter>
        <name>learnPromotedGameUser</name>
        <default_value>wdxuexi</default_value>
        <description>闯关数据库用户</description>
      </parameter>
      <parameter>
        <name>learnPushHost</name>
        <default_value>************</default_value>
        <description>推送数据库主机</description>
      </parameter>
      <parameter>
        <name>learnPushName</name>
        <default_value>push</default_value>
        <description>推送数据库名称</description>
      </parameter>
      <parameter>
        <name>learnPushPassword</name>
        <default_value>learnTest</default_value>
        <description>推送数据库密码</description>
      </parameter>
      <parameter>
        <name>learnPushPort</name>
        <default_value>30306</default_value>
        <description>推送数据库端口</description>
      </parameter>
      <parameter>
        <name>learnPushUser</name>
        <default_value>wdxuexi</default_value>
        <description>推送数据库用户</description>
      </parameter>
      <parameter>
        <name>learnReadingHost</name>
        <default_value>************</default_value>
        <description>共读数据库主机</description>
      </parameter>
      <parameter>
        <name>learnReadingName</name>
        <default_value>reading</default_value>
        <description>共读数据库名称</description>
      </parameter>
      <parameter>
        <name>learnReadingPassword</name>
        <default_value>learnTest</default_value>
        <description>共读数据库密码</description>
      </parameter>
      <parameter>
        <name>learnReadingPort</name>
        <default_value>30306</default_value>
        <description>共读数据库端口</description>
      </parameter>
      <parameter>
        <name>learnReadingUser</name>
        <default_value>wdxuexi</default_value>
        <description>共读数据库用户</description>
      </parameter>
      <parameter>
        <name>learnRecruitingHost</name>
        <default_value>************</default_value>
        <description>招募数据库主机</description>
      </parameter>
      <parameter>
        <name>learnRecruitingName</name>
        <default_value>recruiting</default_value>
        <description>招募数据库名称</description>
      </parameter>
      <parameter>
        <name>learnRecruitingPassword</name>
        <default_value>learnTest</default_value>
        <description>招募数据库密码</description>
      </parameter>
      <parameter>
        <name>learnRecruitingPort</name>
        <default_value>30306</default_value>
        <description>招募数据库端口</description>
      </parameter>
      <parameter>
        <name>learnRecruitingUser</name>
        <default_value>wdxuexi</default_value>
        <description>招募数据库用户</description>
      </parameter>
      <parameter>
        <name>learnSpecialHost</name>
        <default_value>************</default_value>
        <description>专题数据库主机</description>
      </parameter>
      <parameter>
        <name>learnSpecialName</name>
        <default_value>special</default_value>
        <description>专题数据库名称</description>
      </parameter>
      <parameter>
        <name>learnSpecialPassword</name>
        <default_value>learnTest</default_value>
        <description>专题数据库密码</description>
      </parameter>
      <parameter>
        <name>learnSpecialPort</name>
        <default_value>30306</default_value>
        <description>专题数据库端口</description>
      </parameter>
      <parameter>
        <name>learnSpecialUser</name>
        <default_value>wdxuexi</default_value>
        <description>专题数据库用户</description>
      </parameter>
      <parameter>
        <name>learnStatisticsHost</name>
        <default_value>************</default_value>
        <description>统计分析数据库主机</description>
      </parameter>
      <parameter>
        <name>learnStatisticsName</name>
        <default_value>business-view</default_value>
        <description>统计分析数据库名称</description>
      </parameter>
      <parameter>
        <name>learnStatisticsPassword</name>
        <default_value>learnTest</default_value>
        <description>统计分析数据库密码</description>
      </parameter>
      <parameter>
        <name>learnStatisticsPort</name>
        <default_value>30306</default_value>
        <description>统计分析数据库端口</description>
      </parameter>
      <parameter>
        <name>learnStatisticsUser</name>
        <default_value>wdxuexi</default_value>
        <description>统计分析数据库用户</description>
      </parameter>
      <parameter>
        <name>learnSurveyHost</name>
        <default_value>************</default_value>
        <description>调研数据库主机</description>
      </parameter>
      <parameter>
        <name>learnSurveyName</name>
        <default_value>survey</default_value>
        <description>调研数据库名称</description>
      </parameter>
      <parameter>
        <name>learnSurveyPassword</name>
        <default_value>learnTest</default_value>
        <description>调研数据库密码</description>
      </parameter>
      <parameter>
        <name>learnSurveyPort</name>
        <default_value>30306</default_value>
        <description>调研数据库端口</description>
      </parameter>
      <parameter>
        <name>learnSurveyUser</name>
        <default_value>wdxuexi</default_value>
        <description>调研数据库用户</description>
      </parameter>
      <parameter>
        <name>learnTrainHost</name>
        <default_value>************</default_value>
        <description>培训项目数据库主机</description>
      </parameter>
      <parameter>
        <name>learnTrainName</name>
        <default_value>train</default_value>
        <description>培训项目数据库名称</description>
      </parameter>
      <parameter>
        <name>learnTrainPassword</name>
        <default_value>learnTest</default_value>
        <description>培训项目数据库密码</description>
      </parameter>
      <parameter>
        <name>learnTrainPort</name>
        <default_value>30306</default_value>
        <description>培训项目数据库端口</description>
      </parameter>
      <parameter>
        <name>learnTrainUser</name>
        <default_value>wdxuexi</default_value>
        <description>培训项目数据库培训项目</description>
      </parameter>
      <parameter>
        <name>learnUserHost</name>
        <default_value>************</default_value>
        <description>用户数据库主机</description>
      </parameter>
      <parameter>
        <name>learnUserName</name>
        <default_value>user</default_value>
        <description>用户数据库名称</description>
      </parameter>
      <parameter>
        <name>learnUserPassword</name>
        <default_value>learnTest</default_value>
        <description>用户数据库密码</description>
      </parameter>
      <parameter>
        <name>learnUserPort</name>
        <default_value>30306</default_value>
        <description>用户数据库端口</description>
      </parameter>
      <parameter>
        <name>learnUserUser</name>
        <default_value>wdxuexi</default_value>
        <description>用户数据库用户</description>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2025/05/17 15:58:50.335</created_date>
    <modified_user>-</modified_user>
    <modified_date>2025/05/21 16:06:29.573</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>learn_appraise</name>
    <server>${learnAppraiseHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnAppraiseName}</database>
    <port>${learnAppraisePort}</port>
    <username>${learnAppraiseUser}</username>
    <password>${learnAppraisePassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnAppraisePort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_certification</name>
    <server>${learnCertificationHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnCertificationName}</database>
    <port>${learnCertificationPort}</port>
    <username>${learnCertificationUser}</username>
    <password>${learnCertificationPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnCertificationPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_emigrated</name>
    <server>${learnPromotedGameHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnPromotedGameName}</database>
    <port>${learnPromotedGamePort}</port>
    <username>${learnPromotedGameUser}</username>
    <password>${learnPromotedGamePassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnPromotedGamePort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_exam</name>
    <server>${learnExamHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnExamName}</database>
    <port>${learnExamPort}</port>
    <username>${learnExamUser}</username>
    <password>${learnExamPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnExamPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_forum</name>
    <server>${learnForumHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnForumName}</database>
    <port>${learnForumPort}</port>
    <username>${learnForumUser}</username>
    <password>${learnForumPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnForumPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_market</name>
    <server>${learnMarketHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnMarketName}</database>
    <port>${learnMarketPort}</port>
    <username>${learnMarketUser}</username>
    <password>${learnMarketPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnMarketPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_reading</name>
    <server>${learnReadingHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnReadingName}</database>
    <port>${learnReadingPort}</port>
    <username>${learnReadingUser}</username>
    <password>${learnReadingPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnReadingPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_recruiting</name>
    <server>${learnRecruitingHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnRecruitingName}</database>
    <port>${learnRecruitingPort}</port>
    <username>${learnRecruitingUser}</username>
    <password>${learnRecruitingPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnRecruitingPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_statistics</name>
    <server>${learnStatisticsHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnStatisticsName}</database>
    <port>${learnStatisticsPort}</port>
    <username>${learnStatisticsUser}</username>
    <password>${learnStatisticsPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnStatisticsPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_survey</name>
    <server>${learnSurveyHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnSurveyName}</database>
    <port>${learnSurveyPort}</port>
    <username>${learnSurveyUser}</username>
    <password>${learnSurveyPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnSurveyPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_train</name>
    <server>${learnTrainHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnTrainName}</database>
    <port>${learnTrainPort}</port>
    <username>${learnTrainUser}</username>
    <password>${learnTrainPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnTrainPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_user</name>
    <server>${learnUserHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnUserName}</database>
    <port>${learnUserPort}</port>
    <username>${learnUserUser}</username>
    <password>${learnUserPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnUserPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录</from>
      <to>记录集连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 2</from>
      <to>记录集连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录集连接</from>
      <to>字段选择</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 3 2 2 2 2 2</from>
      <to>排序记录 10 2 3 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 3 2 2 2 2 2</from>
      <to>增加常量 5 3 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 3 2 2 2 2 2</from>
      <to>所有学习地图插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有学习地图数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 3 2 2 2 2 2</from>
      <to>分组 7 5 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 3 2 2 2 2 2</from>
      <to>分组 6 2 3 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2 2</from>
      <to>排序记录 9 2 3 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 3 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 3 2 2</from>
      <to>分组 6 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 3 2 2</from>
      <to>分组 7 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 3 2 2</from>
      <to>排序记录 9 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2 2</from>
      <to>本年度学习地图执行插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 3 2 2</from>
      <to>增加常量 5 2 2 3 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 3 2 2</from>
      <to>排序记录 10 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 3 2 2</from>
      <to>排序记录 10 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 3 2 2</from>
      <to>增加常量 5 2 3 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 3 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 3 2 2</from>
      <to>分组 7 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 3 2 2</from>
      <to>分组 6 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度学习地图数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度学习地图执行数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 3 2 2</from>
      <to>排序记录 9 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2 2</from>
      <to>本年度学习地图插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 3 2 2 2 2 3</from>
      <to>排序记录 10 2 3 2 3 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 3 2 2 2 2 3</from>
      <to>增加常量 5 3 2 3 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 3 2 2 2 2 3</from>
      <to>所有任职资格插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有任职资格数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 3 2 2 2 2 3</from>
      <to>分组 7 5 2 3 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 3 2 2 2 2 3</from>
      <to>分组 6 2 3 2 3 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2 3</from>
      <to>排序记录 9 2 3 2 3 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 3 2 3</from>
      <to>排序记录 10 2 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 3 2 3</from>
      <to>增加常量 5 2 3 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 3 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 3 2 3</from>
      <to>分组 7 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 3 2 3</from>
      <to>分组 6 2 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度新增任职资格数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 3 2 3</from>
      <to>排序记录 9 2 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2 3</from>
      <to>本年度新增任职资格插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 3 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 3 2 3</from>
      <to>分组 6 3 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 3 2 3</from>
      <to>分组 7 3 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 3 2 3</from>
      <to>排序记录 9 3 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2 3</from>
      <to>本年度通过认证的人数插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 3 2 3</from>
      <to>增加常量 5 2 2 3 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 3 2 3</from>
      <to>排序记录 10 3 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度认证通过的人数</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查出全部管理员及其管辖范围</from>
      <to>排序记录 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查出全部管理员及其部门 2</from>
      <to>排序记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 2</from>
      <to>排序记录 10 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 2</from>
      <to>排序记录 10 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 2</from>
      <to>增加常量 5 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 2</from>
      <to>增加常量 5 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 2</from>
      <to>增加常量 5 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 2</from>
      <to>所有调研插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有调研数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 2</from>
      <to>分组 7 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 2</from>
      <to>分组 7 5 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 2</from>
      <to>分组 7 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 2</from>
      <to>分组 6 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 2</from>
      <to>分组 6 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 2</from>
      <to>分组 6 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月其他调研举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月调研举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 2</from>
      <to>排序记录 9 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 2</from>
      <to>排序记录 9 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 2</from>
      <to>排序记录 9 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2</from>
      <to>本月其他调研举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2</from>
      <to>本月调研举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 2 2</from>
      <to>排序记录 10 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 2 2</from>
      <to>排序记录 10 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 2 2</from>
      <to>增加常量 5 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 2 2</from>
      <to>增加常量 5 2 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 2 2</from>
      <to>增加常量 5 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 2 2</from>
      <to>所有投票插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有投票数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 2 2</from>
      <to>分组 7 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 2 2</from>
      <to>分组 7 5 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 2 2</from>
      <to>分组 7 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 2 2</from>
      <to>分组 6 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 2 2</from>
      <to>分组 6 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 2 2</from>
      <to>分组 6 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月其他投票举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月投票举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2</from>
      <to>排序记录 9 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 2 2</from>
      <to>排序记录 9 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 2 2</from>
      <to>排序记录 9 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2</from>
      <to>本月其他投票举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2</from>
      <to>本月投票举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 2 2 2</from>
      <to>排序记录 10 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 2 2 2</from>
      <to>排序记录 10 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 2 2 2</from>
      <to>增加常量 5 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 2 2 2</from>
      <to>增加常量 5 2 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 2 2 2</from>
      <to>增加常量 5 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 2 2 2</from>
      <to>所有pk赛插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有pk赛数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 2 2 2</from>
      <to>分组 7 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 2 2 2</from>
      <to>分组 7 5 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 2 2 2</from>
      <to>分组 7 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 2 2 2</from>
      <to>分组 6 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 2 2 2</from>
      <to>分组 6 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 2 2 2</from>
      <to>分组 6 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度pk赛举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月pk赛举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2</from>
      <to>排序记录 9 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2</from>
      <to>排序记录 9 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 2 2 2</from>
      <to>排序记录 9 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2</from>
      <to>本年度pk赛举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2</from>
      <to>本月pk赛举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 2 2 2 2</from>
      <to>排序记录 10 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 2 2 2 2</from>
      <to>排序记录 10 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 2 2 2 2</from>
      <to>增加常量 5 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 2 2 2 2</from>
      <to>增加常量 5 2 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 2 2 2 2</from>
      <to>增加常量 5 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 2 2 2 2</from>
      <to>所有闯关插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有闯关数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 2 2 2 2</from>
      <to>分组 7 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 2 2 2 2</from>
      <to>分组 7 5 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 2 2 2 2</from>
      <to>分组 7 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 2 2 2 2</from>
      <to>分组 6 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 2 2 2 2</from>
      <to>分组 6 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 2 2 2 2</from>
      <to>分组 6 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度闯关举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月闯关举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2</from>
      <to>排序记录 9 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2</from>
      <to>排序记录 9 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2</from>
      <to>排序记录 9 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2</from>
      <to>本年度闯关举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2</from>
      <to>本月闯关举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 2 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 2 2 2 2 2</from>
      <to>排序记录 10 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 2 2 2 2 2</from>
      <to>排序记录 10 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 2 2 2 2 2</from>
      <to>增加常量 5 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 2 2 2 2 2</from>
      <to>增加常量 5 2 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 2 2 2 2 2</from>
      <to>增加常量 5 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 2 2 2 2 2</from>
      <to>所有招募插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有招募数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 2 2 2 2 2</from>
      <to>分组 7 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 2 2 2 2 2</from>
      <to>分组 7 5 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 2 2 2 2 2</from>
      <to>分组 7 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 2 2 2 2 2</from>
      <to>分组 6 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 2 2 2 2 2</from>
      <to>分组 6 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 2 2 2 2 2</from>
      <to>分组 6 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度招募举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月招募举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2</from>
      <to>排序记录 9 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2</from>
      <to>排序记录 9 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2</from>
      <to>排序记录 9 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2</from>
      <to>本年度招募举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2</from>
      <to>本月招募举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 2 2 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 2 2 2 2 2 2</from>
      <to>排序记录 10 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 2 2 2 2 2 2</from>
      <to>排序记录 10 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 2 2 2 2 2 2</from>
      <to>增加常量 5 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 2 2 2 2 2 2</from>
      <to>增加常量 5 2 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 2 2 2 2 2 2</from>
      <to>增加常量 5 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 2 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 2 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 2 2 2 2 2 2</from>
      <to>所有评价插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有评价数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 2 2 2 2 2 2</from>
      <to>分组 7 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 2 2 2 2 2 2</from>
      <to>分组 7 5 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 2 2 2 2 2 2</from>
      <to>分组 7 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 2 2 2 2 2 2</from>
      <to>分组 6 2 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 2 2 2 2 2 2</from>
      <to>分组 6 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 2 2 2 2 2 2</from>
      <to>分组 6 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度评价举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月评价举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2 2</from>
      <to>排序记录 9 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2 2</from>
      <to>排序记录 9 2 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2 2</from>
      <to>排序记录 9 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2 2</from>
      <to>本年度评价举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2 2</from>
      <to>本月评价举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 2 2 2 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 2 2 2 2 2 2 2</from>
      <to>排序记录 10 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 2 2 2 2 2 2 2</from>
      <to>排序记录 10 3 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 2 2 2 2 2 2 2</from>
      <to>增加常量 5 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 2 2 2 2 2 2 2</from>
      <to>增加常量 5 2 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 2 2 2 2 2 2 2</from>
      <to>增加常量 5 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 2 2 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 2 2 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 2 2 2 2 2 2 2</from>
      <to>所有共读插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有共读数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 2 2 2 2 2 2 2</from>
      <to>分组 7 2 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 2 2 2 2 2 2 2</from>
      <to>分组 7 5 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 2 2 2 2 2 2 2</from>
      <to>分组 7 3 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 2 2 2 2 2 2 2</from>
      <to>分组 6 2 2 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 2 2 2 2 2 2 2</from>
      <to>分组 6 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 2 2 2 2 2 2 2</from>
      <to>分组 6 3 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度共读举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月共读举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2 2 2</from>
      <to>排序记录 9 3 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2 2 2</from>
      <to>排序记录 9 2 2 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2 2 2</from>
      <to>排序记录 9 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2</from>
      <to>本年度共读举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2 2 2</from>
      <to>本月共读举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>上个月所有话题数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2</from>
      <to>排序记录 10 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3</from>
      <to>排序记录 10 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2</from>
      <to>排序记录 10 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 4 2</from>
      <to>排序记录 10 4 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2</from>
      <to>增加常量 5 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2</from>
      <to>增加常量 5 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 4 2</from>
      <to>增加常量 5 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5</from>
      <to>增加常量 5 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3</from>
      <to>所有话题插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有话题数量</from>
      <to>记录关联 (笛卡尔输出) 6 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2</from>
      <to>分组 7 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3</from>
      <to>分组 7 5</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2</from>
      <to>分组 7 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 4 2</from>
      <to>分组 7 4 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2</from>
      <to>分组 6 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3</from>
      <to>分组 6 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2</from>
      <to>分组 6 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 4 2</from>
      <to>分组 6 4 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>昨日所有话题数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月所有话题数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 2 2</from>
      <to>排序记录 9 4 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3</from>
      <to>排序记录 9 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3</from>
      <to>排序记录 9 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3</from>
      <to>排序记录 9 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2</from>
      <to>上个月话题插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3</from>
      <to>本月话题插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3</from>
      <to>昨日话题插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>分组 6 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1306</xloc>
      <yloc>1610</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1309</xloc>
      <yloc>2107</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1321</xloc>
      <yloc>2604</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1327</xloc>
      <yloc>3115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1330</xloc>
      <yloc>3623</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1318</xloc>
      <yloc>540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 3 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1305</xloc>
      <yloc>1082</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1322</xloc>
      <yloc>1466</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1325</xloc>
      <yloc>1963</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1337</xloc>
      <yloc>2460</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1343</xloc>
      <yloc>2971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1346</xloc>
      <yloc>3479</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 3 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1334</xloc>
      <yloc>396</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 3 2 2 2 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1321</xloc>
      <yloc>938</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1306</xloc>
      <yloc>1770</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1309</xloc>
      <yloc>2267</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1321</xloc>
      <yloc>2764</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1327</xloc>
      <yloc>3275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1330</xloc>
      <yloc>3783</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1318</xloc>
      <yloc>684</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 3 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1308</xloc>
      <yloc>1237</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1482</xloc>
      <yloc>1610</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1485</xloc>
      <yloc>2107</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1497</xloc>
      <yloc>2604</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1503</xloc>
      <yloc>3115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1506</xloc>
      <yloc>3623</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1494</xloc>
      <yloc>540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 3 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1481</xloc>
      <yloc>1082</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1482</xloc>
      <yloc>1770</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1485</xloc>
      <yloc>2267</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1497</xloc>
      <yloc>2764</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1503</xloc>
      <yloc>3275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1506</xloc>
      <yloc>3783</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1494</xloc>
      <yloc>684</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 3 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1484</xloc>
      <yloc>1237</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1498</xloc>
      <yloc>1466</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1501</xloc>
      <yloc>1963</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1513</xloc>
      <yloc>2460</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1519</xloc>
      <yloc>2971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1522</xloc>
      <yloc>3479</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 3 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1510</xloc>
      <yloc>396</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 3 2 2 2 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1497</xloc>
      <yloc>938</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>13</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1610</xloc>
      <yloc>1770</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>14</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1613</xloc>
      <yloc>2267</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>15</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1625</xloc>
      <yloc>2764</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>16</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1631</xloc>
      <yloc>3275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>17</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1634</xloc>
      <yloc>3783</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>22</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1622</xloc>
      <yloc>684</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 3 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>23</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1612</xloc>
      <yloc>1237</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>13</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1610</xloc>
      <yloc>1610</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>14</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1613</xloc>
      <yloc>2107</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>15</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1625</xloc>
      <yloc>2604</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>16</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1631</xloc>
      <yloc>3115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>17</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1634</xloc>
      <yloc>3623</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>22</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1622</xloc>
      <yloc>540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 3 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>23</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1609</xloc>
      <yloc>1082</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>13</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1610</xloc>
      <yloc>1466</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>14</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1613</xloc>
      <yloc>1963</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>15</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1625</xloc>
      <yloc>2460</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>16</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1631</xloc>
      <yloc>2971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>17</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1634</xloc>
      <yloc>3479</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 3 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>22</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1622</xloc>
      <yloc>396</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 3 2 2 2 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>23</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1609</xloc>
      <yloc>938</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字段选择</name>
    <type>SelectValues</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>level_path</name>
        <rename>user_level_path</rename>
      </field>
      <field>
        <name>user_id</name>
      </field>
      <field>
        <name>org_id</name>
        <rename>user_org_id</rename>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>848</xloc>
      <yloc>4832</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有pk赛插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1737</xloc>
      <yloc>2460</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有pk赛数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from ex_exam_competition l
inner join sys_org g on g.id = l.create_org_id
where l.is_del = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1081</xloc>
      <yloc>2364</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有任职资格插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1737</xloc>
      <yloc>938</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有任职资格数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_certification</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from job_qualification l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 </sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1065</xloc>
      <yloc>842</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有学习地图插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1750</xloc>
      <yloc>396</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有学习地图数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_train</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from train_learn_map l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 </sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1078</xloc>
      <yloc>300</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有投票插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1725</xloc>
      <yloc>1963</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有投票数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_market</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from vote l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.referenced_type = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1069</xloc>
      <yloc>1867</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有招募插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1746</xloc>
      <yloc>3479</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有招募数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_recruiting</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from recruiting l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.referenced_type = 0 and l.type != 1</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1090</xloc>
      <yloc>3383</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有调研插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1722</xloc>
      <yloc>1466</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有调研数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_survey</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from survey l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.train = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1066</xloc>
      <yloc>1370</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有闯关插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1743</xloc>
      <yloc>2971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有闯关数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_emigrated</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from emigrated l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1087</xloc>
      <yloc>2875</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录</name>
    <type>SortRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>640</xloc>
      <yloc>4880</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1386</xloc>
      <yloc>1610</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1389</xloc>
      <yloc>2107</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1401</xloc>
      <yloc>2604</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1407</xloc>
      <yloc>3115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1410</xloc>
      <yloc>3623</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1398</xloc>
      <yloc>540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 3 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1385</xloc>
      <yloc>1082</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1402</xloc>
      <yloc>1466</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1405</xloc>
      <yloc>1963</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1417</xloc>
      <yloc>2460</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1423</xloc>
      <yloc>2971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1426</xloc>
      <yloc>3479</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 3 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1414</xloc>
      <yloc>396</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 3 2 2 2 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1401</xloc>
      <yloc>938</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1386</xloc>
      <yloc>1770</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1389</xloc>
      <yloc>2267</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1401</xloc>
      <yloc>2764</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1407</xloc>
      <yloc>3275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1410</xloc>
      <yloc>3783</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1398</xloc>
      <yloc>684</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 3 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1388</xloc>
      <yloc>1237</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>656</xloc>
      <yloc>4752</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1226</xloc>
      <yloc>1610</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1229</xloc>
      <yloc>2107</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1241</xloc>
      <yloc>2604</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1247</xloc>
      <yloc>3115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1250</xloc>
      <yloc>3623</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1238</xloc>
      <yloc>540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 3 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1225</xloc>
      <yloc>1082</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1242</xloc>
      <yloc>1466</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1245</xloc>
      <yloc>1963</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1257</xloc>
      <yloc>2460</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1263</xloc>
      <yloc>2971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1266</xloc>
      <yloc>3479</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 3 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1254</xloc>
      <yloc>396</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 3 2 2 2 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1241</xloc>
      <yloc>938</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1226</xloc>
      <yloc>1770</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1229</xloc>
      <yloc>2267</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1241</xloc>
      <yloc>2764</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1247</xloc>
      <yloc>3275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1250</xloc>
      <yloc>3783</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1238</xloc>
      <yloc>684</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 3 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1228</xloc>
      <yloc>1237</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度pk赛举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from ex_exam_competition  l
inner join sys_org g on g.id = l.create_org_id
where l.is_del = 0  
 and YEAR(l.create_time) = YEAR(CURDATE())</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1088</xloc>
      <yloc>2688</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度pk赛举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2041</xloc>
      <yloc>2764</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度学习地图执行插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2038</xloc>
      <yloc>684</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度学习地图执行数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_train</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from  train_learn_map_exec l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and YEAR(l.create_time) = YEAR(CURDATE())
</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1078</xloc>
      <yloc>620</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度学习地图插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2038</xloc>
      <yloc>540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度学习地图数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_train</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from train_learn_map l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and YEAR(l.create_time) = YEAR(CURDATE())
</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1078</xloc>
      <yloc>476</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度招募举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_recruiting</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from recruiting  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0   and l.referenced_type = 0 and l.type != 1
 and YEAR(l.open_begin_time) = YEAR(CURDATE())</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1075</xloc>
      <yloc>3708</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度招募举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2050</xloc>
      <yloc>3783</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度新增任职资格插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2025</xloc>
      <yloc>1082</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度新增任职资格数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_certification</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from job_qualification l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and YEAR(l.create_time) = YEAR(CURDATE())</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1065</xloc>
      <yloc>1018</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度认证通过的人数</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_certification</connection>
    <sql>select jaar.id
	,l.org_id
	,g.level_path
	,l.create_by
from job_authentication l LEFT JOIN job_auth_apply_record jaar on l.id = jaar.authentication_id
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and YEAR(l.create_time) = YEAR(CURDATE()) and jaar.is_del = 0 and jaar.status = 1</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1068</xloc>
      <yloc>1173</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度通过认证的人数插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2028</xloc>
      <yloc>1237</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度闯关举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_emigrated</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from emigrated  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0  
 and YEAR(l.begin_time) = YEAR(CURDATE())</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1072</xloc>
      <yloc>3200</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度闯关举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2047</xloc>
      <yloc>3275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月pk赛举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from ex_exam_competition  l
inner join sys_org g on g.id = l.create_org_id
where l.is_del = 0  
and date_format( l.start_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1081</xloc>
      <yloc>2540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月pk赛举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2041</xloc>
      <yloc>2604</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月其他投票举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_market</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from vote l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.referenced_type != 0 
and date_format( l.start_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1069</xloc>
      <yloc>2203</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月其他投票举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2029</xloc>
      <yloc>2267</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月其他调研举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_survey</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from survey l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.train != 0 
and date_format( l.start_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1066</xloc>
      <yloc>1706</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月其他调研举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2026</xloc>
      <yloc>1770</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月投票举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_market</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from vote  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.referenced_type = 0  
and date_format( l.start_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1069</xloc>
      <yloc>2043</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月投票举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2029</xloc>
      <yloc>2107</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月招募举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_recruiting</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from recruiting  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0   and l.referenced_type = 0 and l.type != 1
and date_format( l.open_begin_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1090</xloc>
      <yloc>3559</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月招募举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2050</xloc>
      <yloc>3623</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月调研举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_survey</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from survey l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.train = 0  
and date_format( l.start_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1066</xloc>
      <yloc>1546</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月调研举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2026</xloc>
      <yloc>1610</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月闯关举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_emigrated</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from emigrated  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0  
and date_format( l.begin_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1087</xloc>
      <yloc>3051</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月闯关举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2047</xloc>
      <yloc>3115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>查出全部管理员及其管辖范围</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_user</connection>
    <sql>select
	suo.user_id,
	so.level_path
from
	sys_user_org suo
	inner join sys_org so on suo.org_id = so.id
where
	suo.user_id in (
	select distinct
		su.id 
	from
		sys_user su
		left join sys_role_user sru on su.id = sru.user_id
	where
		sru.role_id != 10 
		and su.is_del = 0
	) 
	and so.is_del = 0
	and so.is_available = 1
	and suo.relation_type = 'UserManageArea'</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查出全部管理员及其管辖范围</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>查出全部管理员及其管辖范围</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>4704</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>查出全部管理员及其部门 2</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_user</connection>
    <sql>select
	distinct su.org_id,su.id
from
	sys_user su
	left join sys_role_user sru on su.id = sru.user_id
where
	sru.role_id != 10 
	and su.is_del = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查出全部管理员及其部门</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查出全部管理员及其部门</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>4880</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1066</xloc>
      <yloc>1770</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1069</xloc>
      <yloc>2267</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1081</xloc>
      <yloc>2764</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1087</xloc>
      <yloc>3275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1090</xloc>
      <yloc>3783</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1078</xloc>
      <yloc>684</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 3 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1068</xloc>
      <yloc>1237</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1066</xloc>
      <yloc>1610</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1069</xloc>
      <yloc>2107</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1081</xloc>
      <yloc>2604</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1087</xloc>
      <yloc>3115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1090</xloc>
      <yloc>3623</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1078</xloc>
      <yloc>540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 3 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1065</xloc>
      <yloc>1082</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1066</xloc>
      <yloc>1466</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1069</xloc>
      <yloc>1963</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1081</xloc>
      <yloc>2460</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1087</xloc>
      <yloc>2971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1090</xloc>
      <yloc>3479</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1078</xloc>
      <yloc>396</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1065</xloc>
      <yloc>938</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录集连接</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>排序记录 2</step1>
    <step2>排序记录</step2>
    <keys_1>
      <key>user_id</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>768</xloc>
      <yloc>4832</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有调研插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1834</xloc>
      <yloc>1770</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有投票插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1837</xloc>
      <yloc>2267</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有pk赛插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1849</xloc>
      <yloc>2764</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有闯关插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1855</xloc>
      <yloc>3275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有招募插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1858</xloc>
      <yloc>3783</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有学习地图插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1846</xloc>
      <yloc>684</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有任职资格插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1836</xloc>
      <yloc>1237</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有调研插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1818</xloc>
      <yloc>1610</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有投票插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1821</xloc>
      <yloc>2107</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有pk赛插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1833</xloc>
      <yloc>2604</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有闯关插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1839</xloc>
      <yloc>3115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有招募插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1842</xloc>
      <yloc>3623</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有学习地图插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1830</xloc>
      <yloc>540</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有任职资格插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1817</xloc>
      <yloc>1082</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1350</xloc>
      <yloc>4115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1366</xloc>
      <yloc>3971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1350</xloc>
      <yloc>4275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1526</xloc>
      <yloc>4115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1526</xloc>
      <yloc>4275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1542</xloc>
      <yloc>3971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>18</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1654</xloc>
      <yloc>4275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>18</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1654</xloc>
      <yloc>4115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>18</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1654</xloc>
      <yloc>3971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有评价插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1766</xloc>
      <yloc>3971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有评价数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_appraise</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from appraise l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.referenced_type = 0 and l.type != 1</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1110</xloc>
      <yloc>3875</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1430</xloc>
      <yloc>4115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1446</xloc>
      <yloc>3971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1430</xloc>
      <yloc>4275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1270</xloc>
      <yloc>4115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1286</xloc>
      <yloc>3971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1270</xloc>
      <yloc>4275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度评价举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_appraise</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from appraise  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0   and l.referenced_type = 0 and l.type != 1
 and YEAR(l.judge_begin_time) = YEAR(CURDATE())</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1095</xloc>
      <yloc>4200</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度评价举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2070</xloc>
      <yloc>4275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月评价举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_appraise</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from appraise  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0   and l.referenced_type = 0 and l.type != 1
and date_format( l.judge_begin_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1110</xloc>
      <yloc>4051</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月评价举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2070</xloc>
      <yloc>4115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1110</xloc>
      <yloc>4275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1110</xloc>
      <yloc>4115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1110</xloc>
      <yloc>3971</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有评价插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1878</xloc>
      <yloc>4275</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有评价插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1862</xloc>
      <yloc>4115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1363</xloc>
      <yloc>4621</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1379</xloc>
      <yloc>4477</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1363</xloc>
      <yloc>4781</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1539</xloc>
      <yloc>4621</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1539</xloc>
      <yloc>4781</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 2 2 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1555</xloc>
      <yloc>4477</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 2 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>19</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1667</xloc>
      <yloc>4781</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 2 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>19</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1667</xloc>
      <yloc>4621</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 2 2 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>19</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1667</xloc>
      <yloc>4477</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有共读插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1779</xloc>
      <yloc>4477</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有共读数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_reading</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from reading_activity l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0  </sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有调研数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1123</xloc>
      <yloc>4381</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1443</xloc>
      <yloc>4621</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1459</xloc>
      <yloc>4477</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1443</xloc>
      <yloc>4781</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1283</xloc>
      <yloc>4621</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1299</xloc>
      <yloc>4477</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 2 2 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1283</xloc>
      <yloc>4781</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度共读举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_reading</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from reading_activity  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0   
 and YEAR(l.start_time) = YEAR(CURDATE())</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月其他考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1136</xloc>
      <yloc>4704</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度共读举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2083</xloc>
      <yloc>4781</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月共读举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_reading</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from reading_activity  l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0   
and date_format( l.start_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月考试举办场次</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1123</xloc>
      <yloc>4557</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月共读举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2083</xloc>
      <yloc>4621</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 2 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1123</xloc>
      <yloc>4781</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 2 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1123</xloc>
      <yloc>4621</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 2 2 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1123</xloc>
      <yloc>4477</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有共读插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1891</xloc>
      <yloc>4781</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有共读插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1875</xloc>
      <yloc>4621</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>上个月所有话题数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_forum</connection>
    <sql>select l.post_id id
	,l.org_id
	,g.level_path
	,l.create_by
from post l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0
and period_diff( date_format( now( ) , '%Y%m' ) , date_format( l.create_time, '%Y%m' ) ) =1</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1129</xloc>
      <yloc>5334</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>上个月话题插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>last_month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2089</xloc>
      <yloc>5398</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1369</xloc>
      <yloc>5110</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1385</xloc>
      <yloc>4966</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1369</xloc>
      <yloc>5254</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 4 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1369</xloc>
      <yloc>5398</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1545</xloc>
      <yloc>5110</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1545</xloc>
      <yloc>5254</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 4 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1545</xloc>
      <yloc>5398</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1561</xloc>
      <yloc>4966</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>20</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1689</xloc>
      <yloc>5398</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>20</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1673</xloc>
      <yloc>5254</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>20</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1673</xloc>
      <yloc>5110</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>20</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1673</xloc>
      <yloc>4966</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有话题插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1785</xloc>
      <yloc>4966</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有话题数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_forum</connection>
    <sql>select l.post_id id
	,l.org_id
	,g.level_path
	,l.create_by
from post l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1129</xloc>
      <yloc>4870</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1449</xloc>
      <yloc>5110</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1465</xloc>
      <yloc>4966</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1449</xloc>
      <yloc>5254</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 4 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1449</xloc>
      <yloc>5398</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1289</xloc>
      <yloc>5110</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1305</xloc>
      <yloc>4966</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1289</xloc>
      <yloc>5254</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 4 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1289</xloc>
      <yloc>5398</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>昨日所有话题数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_forum</connection>
    <sql>select l.post_id id
	,l.org_id
	,g.level_path
	,l.create_by
from post l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0
AND date(l.create_time) = date_sub(curdate(),interval 1 day);
</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1129</xloc>
      <yloc>5046</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>昨日话题插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2089</xloc>
      <yloc>5110</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月所有话题数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_forum</connection>
    <sql>select l.post_id id
	,l.org_id
	,g.level_path
	,l.create_by
from post l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0
and date_format( l.create_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1129</xloc>
      <yloc>5190</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月话题插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>2089</xloc>
      <yloc>5254</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>上个月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1129</xloc>
      <yloc>5398</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1129</xloc>
      <yloc>5254</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1129</xloc>
      <yloc>5110</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1129</xloc>
      <yloc>4966</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有话题插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1897</xloc>
      <yloc>5398</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有话题插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1897</xloc>
      <yloc>5254</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有话题插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1881</xloc>
      <yloc>5110</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
