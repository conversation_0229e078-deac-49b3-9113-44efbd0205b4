<transformation>
  <info>
    <name>stat_analysis_learn_record_day</name>
    <description>学员档案每日报表</description>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>dbHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>dbName</name>
        <default_value> </default_value>
        <description/>
      </parameter>
      <parameter>
        <name>dbPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>dbPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>dbUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>handleTime</name>
        <default_value>2025-02-17</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentName</name>
        <default_value>comment_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCourseHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCourseName</name>
        <default_value>course_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCoursePassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCoursePort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCourseUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamName</name>
        <default_value>exam_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationName</name>
        <default_value>excitation_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumName</name>
        <default_value>forun_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoName</name>
        <default_value>info_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLiveHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLiveName</name>
        <default_value>live_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLivePassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLivePort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLiveUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectName</name>
        <default_value>project_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsName</name>
        <default_value>learn_statistics</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyName</name>
        <default_value>survey_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnTrainHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnTrainName</name>
        <default_value>train_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnTrainPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnTrainPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnTrainUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserHost</name>
        <default_value>jms.wunding.com</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserName</name>
        <default_value>user_wunding</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserPassword</name>
        <default_value>BHj8uYee9TKaiO1i</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserPort</name>
        <default_value>33061</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserUser</name>
        <default_value>6eedb0b9-7c14-40c6-9539-39e5e511cb4c</default_value>
        <description/>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2020/08/04 13:15:20.549</created_date>
    <modified_user>-</modified_user>
    <modified_date>2025/02/19 09:35:36.263</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>learn_comment</name>
    <server>${learnCommentHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnCommentName}</database>
    <port>${learnCommentPort}</port>
    <username>${learnCommentUser}</username>
    <password>${learnCommentPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnCommentPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_course</name>
    <server>${learnCourseHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnCourseName}</database>
    <port>${learnCoursePort}</port>
    <username>${learnCourseUser}</username>
    <password>${learnCoursePassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnCoursePort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_exam</name>
    <server>${learnExamHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnExamName}</database>
    <port>${learnExamPort}</port>
    <username>${learnExamUser}</username>
    <password>${learnExamPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnExamPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_info</name>
    <server>${learnInfoHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnInfoName}</database>
    <port>${learnInfoPort}</port>
    <username>${learnInfoUser}</username>
    <password>${learnInfoPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnInfoPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_project</name>
    <server>${learnProjectHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnProjectName}</database>
    <port>${learnProjectPort}</port>
    <username>${learnProjectUser}</username>
    <password>${learnProjectPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnProjectPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_statistics</name>
    <server>${learnStatisticsHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnStatisticsName}</database>
    <port>${learnStatisticsPort}</port>
    <username>${learnStatisticsUser}</username>
    <password>${learnStatisticsPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnStatisticsPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_survey</name>
    <server>${learnSurveyHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnSurveyName}</database>
    <port>${learnSurveyPort}</port>
    <username>${learnSurveyUser}</username>
    <password>${learnSurveyPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnSurveyPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_train</name>
    <server>${learnTrainHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnTrainName}</database>
    <port>${learnTrainPort}</port>
    <username>${learnTrainUser}</username>
    <password>${learnTrainPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnTrainPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_user</name>
    <server>${learnUserHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnUserName}</database>
    <port>${learnUserPort}</port>
    <username>${learnUserUser}</username>
    <password>${learnUserPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnUserPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>移除多余字段</from>
      <to>替换NULL值</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>合并数据</from>
      <to>去掉id为空记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>去掉id为空记录</from>
      <to>移除多余字段</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>替换NULL值</from>
      <to>插入 / 更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>获取用户相关信息</from>
      <to>排序记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>调研数</from>
      <to>排序记录 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>点赞数</from>
      <to>排序记录 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>获取学习时长</from>
      <to>排序记录 4</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>参与学习项目数量</from>
      <to>排序记录 5</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>参与考试数</from>
      <to>排序记录 6</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>资讯数</from>
      <to>排序记录 7</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>评论数</from>
      <to>排序记录 8</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>新学课程数</from>
      <to>排序记录 9</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 2</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 3</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 4</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 5</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 6</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 7</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 8</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>参与面授班级数量</from>
      <to>排序记录10</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>参与培训项目数量</from>
      <to>排序记录11</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录11</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录10</from>
      <to>合并数据</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>去掉id为空记录</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to>移除多余字段</send_true_to>
    <send_false_to/>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>ID</leftvalue>
        <function>IS NOT NULL</function>
        <rightvalue/>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>参与培训项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_train</connection>
    <sql>select
	a.user_id userid,
	count( a.id ) train_num 
from
	train_progress a
	INNER join train b ON a.train_id = b.id
where
	date_format( a.start_time, '%Y-%m-%d' ) = '${handleTime}'
group by
	a.user_id 
ORDER BY
	a.user_id</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>参与培训项目数量</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>train_num</name>
        <length>15</length>
        <precision>0</precision>
        <origin>参与培训项目数量</origin>
        <comments>train_num</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>736</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>参与学习项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select
	a.user_id userid,
	count( a.id ) project_num 
from
	project_progress a
	INNER join project b ON a.pro_id = b.id
where
	date_format( a.start_time, '%Y-%m-%d' ) = '${handleTime}'
	and ( b.flag = 0 or b.flag is null )
	and a.status = 1
	and b.type = 0
	and b.project_type = 0
	and b.referenced_type = 0
group by
	a.user_id 
ORDER BY
	a.user_id</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>参与学习项目数量</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>project_num</name>
        <length>15</length>
        <precision>0</precision>
        <origin>参与学习项目数量</origin>
        <comments>project_num</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>64</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>参与考试数</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select a.user_id userid, count(a.id) exam_num
          from ex_answer_record a inner join ex_exam e ON a.exam_id = e.id
         where a.is_post = 1
       and date_format(a.answer_time, '%Y-%m-%d') = '${handleTime}'
group by a.user_id order by a.user_id</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>参与考试数</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>exam_num</name>
        <length>15</length>
        <precision>0</precision>
        <origin>参与考试数</origin>
        <comments>exam_num</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>384</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>参与面授班级数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select
	a.user_id userid,
	count( a.id ) face_project_num
from
	project_progress a
	INNER join project b ON a.pro_id = b.id
where
	date_format( a.start_time, '%Y-%m-%d' ) = '${handleTime}'
	and ( b.flag = 0 or b.flag is null )
	and a.status = 1
	and b.type = 0
	and b.project_type = 3
	and b.referenced_type = 0
group by
	a.user_id 
ORDER BY
	a.user_id</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>参与面授班级数量</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>face_project_num</name>
        <length>15</length>
        <precision>0</precision>
        <origin>参与面授班级数量</origin>
        <comments>face_project_num</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>合并数据</name>
    <type>MultiwayMergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>FULL OUTER</join_type>
    <step0>排序记录</step0>
    <step1>排序记录 2</step1>
    <step2>排序记录 3</step2>
    <step3>排序记录 4</step3>
    <step4>排序记录 5</step4>
    <step5>排序记录 6</step5>
    <step6>排序记录 7</step6>
    <step7>排序记录 8</step7>
    <step8>排序记录 9</step8>
    <step9>排序记录10</step9>
    <step10>排序记录11</step10>
    <number_input>11</number_input>
    <keys>
      <key>id</key>
      <key>userid</key>
      <key>USERID</key>
      <key>userid</key>
      <key>userid</key>
      <key>userid</key>
      <key>userid</key>
      <key>userid</key>
      <key>userid</key>
      <key>userid</key>
      <key>userid</key>
    </keys>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>720</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>128</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>USERID</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>192</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 4</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>256</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 5</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>64</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 6</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>384</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 7</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 8</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>576</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录10</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录11</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>userid</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>352</xloc>
      <yloc>736</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>插入 / 更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_learn_record_day</table>
      <key>
        <name>handle_time</name>
        <field>handle_time</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>ID</name>
        <field>ID</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>id</name>
        <rename>id</rename>
        <update>N</update>
      </value>
      <value>
        <name>login_name</name>
        <rename>login_name</rename>
        <update>Y</update>
      </value>
      <value>
        <name>full_name</name>
        <rename>full_name</rename>
        <update>Y</update>
      </value>
      <value>
        <name>nike_name</name>
        <rename>nike_name</rename>
        <update>Y</update>
      </value>
      <value>
        <name>org_id</name>
        <rename>org_id</rename>
        <update>Y</update>
      </value>
      <value>
        <name>org_name</name>
        <rename>org_name</rename>
        <update>Y</update>
      </value>
      <value>
        <name>level_path</name>
        <rename>level_path</rename>
        <update>Y</update>
      </value>
      <value>
        <name>post_id</name>
        <rename>post_id</rename>
        <update>Y</update>
      </value>
      <value>
        <name>post_name</name>
        <rename>post_name</rename>
        <update>Y</update>
      </value>
      <value>
        <name>sex</name>
        <rename>sex</rename>
        <update>Y</update>
      </value>
      <value>
        <name>telephone</name>
        <rename>telephone</rename>
        <update>Y</update>
      </value>
      <value>
        <name>email</name>
        <rename>email</rename>
        <update>Y</update>
      </value>
      <value>
        <name>birthday</name>
        <rename>birthday</rename>
        <update>Y</update>
      </value>
      <value>
        <name>join_date</name>
        <rename>join_date</rename>
        <update>Y</update>
      </value>
      <value>
        <name>handle_time</name>
        <rename>handle_time</rename>
        <update>N</update>
      </value>
      <value>
        <name>update_time</name>
        <rename>update_time</rename>
        <update>Y</update>
      </value>
      <value>
        <name>total_learn</name>
        <rename>total_learn</rename>
        <update>Y</update>
      </value>
      <value>
        <name>project_num</name>
        <rename>project_num</rename>
        <update>Y</update>
      </value>
      <value>
        <name>exam_num</name>
        <rename>exam_num</rename>
        <update>Y</update>
      </value>
      <value>
        <name>news_num</name>
        <rename>news_num</rename>
        <update>Y</update>
      </value>
      <value>
        <name>survey_num</name>
        <rename>survey_num</rename>
        <update>Y</update>
      </value>
      <value>
        <name>total_agree</name>
        <rename>total_agree</rename>
        <update>Y</update>
      </value>
      <value>
        <name>total_comment</name>
        <rename>total_comment</rename>
        <update>Y</update>
      </value>
      <value>
        <name>total_course</name>
        <rename>total_course</rename>
        <update>Y</update>
      </value>
      <value>
        <name>is_available</name>
        <rename>is_available</rename>
        <update>Y</update>
      </value>
      <value>
        <name>face_project_num</name>
        <rename>face_project_num</rename>
        <update>Y</update>
      </value>
      <value>
        <name>train_num</name>
        <rename>train_num</rename>
        <update>Y</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>新学课程数</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_course</connection>
    <sql>select
        a.create_by as userid,
        count( a.id ) total_course 
from
        user_course_record a
        inner join course b on a.course_id = b.id
where
        a.create_time >= STR_TO_DATE('${handleTime}', '%Y-%m-%d') and a.create_time &lt; date_add(STR_TO_DATE('${handleTime}', '%Y-%m-%d'), interval 1 day)
group by
        a.create_by 
order by 
        a.create_by</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>新学课程数</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>total_course</name>
        <length>15</length>
        <precision>0</precision>
        <origin>新学课程数</origin>
        <comments>total_course</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>576</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>替换NULL值</name>
    <type>IfNull</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <replaceAllByValue/>
    <replaceAllMask/>
    <selectFields>N</selectFields>
    <selectValuesType>Y</selectValuesType>
    <setEmptyStringAll>N</setEmptyStringAll>
    <valuetypes>
      <valuetype>
        <name>BigNumber</name>
        <value>0</value>
        <mask/>
        <set_type_empty_string>N</set_type_empty_string>
      </valuetype>
      <valuetype>
        <name>Integer</name>
        <value>0</value>
        <mask/>
        <set_type_empty_string>N</set_type_empty_string>
      </valuetype>
    </valuetypes>
    <fields>
      <field>
        <name>EXAMNUM</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1008</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>点赞数</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_course</connection>
    <sql>
select
	create_by as USERID,
	IFNULL(sum( cw.vote_count ), 0) total_agree
from
	course_vote cw
where
	date_format( cw.create_time, '%Y-%m-%d' ) = STR_TO_DATE( '${handleTime}', '%Y-%m-%d' )
group by create_by order by create_by</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>USERID</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>点赞数</origin>
        <comments>USERID</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>total_agree</name>
        <length>25</length>
        <precision>0</precision>
        <origin>点赞数</origin>
        <comments>total_agree</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>192</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>移除多余字段</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <select_unspecified>N</select_unspecified>
      <remove>
        <name>userid_1</name>
      </remove>
      <remove>
        <name>userid_2</name>
      </remove>
      <remove>
        <name>userid_3</name>
      </remove>
      <remove>
        <name>userid_4</name>
      </remove>
      <remove>
        <name>userid_6</name>
      </remove>
      <remove>
        <name>userid_7</name>
      </remove>
      <remove>
        <name>userid_5</name>
      </remove>
      <remove>
        <name>userid</name>
      </remove>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>928</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>获取学习时长</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_course</connection>
    <sql>select view_by userid, sum(duration) total_learn from course_view_duration
where view_time >= STR_TO_DATE('${handleTime}', '%Y-%m-%d') and view_time &lt; date_add(STR_TO_DATE('${handleTime}', '%Y-%m-%d'), interval 1 day)
group by view_by order by view_by</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>获取学习时长</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>total_learn</name>
        <length>32</length>
        <precision>0</precision>
        <origin>获取学习时长</origin>
        <comments>total_learn</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>256</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>获取用户相关信息</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_user</connection>
    <sql>select
       su.id,
       su.login_name,
       su.full_name,
       su.nike_name,
       su.org_id,
       so.org_name,
       so.level_path,
       sui.identity_id post_id,
       i.name post_name,
       su.sex,
       su.telephone,
       su.email,
       su.birthday,
       su.join_date,
	   '${handleTime}' as handle_time,
	   now() as update_time,
	   su.is_available
from sys_user su,
     user_identity sui,
     new_identity i,
     sys_org so
where
    su.id = sui.user_id
    and su.org_id = so.id
and sui.identity_id = i.id
and su.is_del = 0
and i.category_id = '4'
order by su.id asc</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>login_name</name>
        <length>80</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>login_name</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>full_name</name>
        <length>100</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>full_name</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>nike_name</name>
        <length>100</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>nike_name</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_name</name>
        <length>300</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>org_name</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>post_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>post_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>post_name</name>
        <length>100</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>post_name</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>sex</name>
        <length>2</length>
        <precision>0</precision>
        <origin>获取用户相关信息</origin>
        <comments>sex</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>telephone</name>
        <length>80</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>telephone</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>email</name>
        <length>80</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>email</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Timestamp</type>
        <storagetype>normal</storagetype>
        <name>birthday</name>
        <length>0</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>birthday</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Timestamp</type>
        <storagetype>normal</storagetype>
        <name>join_date</name>
        <length>0</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>join_date</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>handle_time</name>
        <length>10</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>handle_time</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Timestamp</type>
        <storagetype>normal</storagetype>
        <name>update_time</name>
        <length>0</length>
        <precision>-1</precision>
        <origin>获取用户相关信息</origin>
        <comments>update_time</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>评论数</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_comment</connection>
    <sql>select comment_by userid, count(id) total_comment
          from resource_comment
          where resource_type = 0 and date_format(comment_time, '%Y-%m-%d') = '${handleTime}'
          group by comment_by order by comment_by</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>评论数</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>total_comment</name>
        <length>15</length>
        <precision>0</precision>
        <origin>评论数</origin>
        <comments>total_comment</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>调研数</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_survey</connection>
    <sql>select sr.create_by userid, count(s.id) survey_num
          	from survey_record sr
        inner join survey s on sr.survey_id = s.id
          where
			date_format(sr.create_time, '%Y-%m-%d') = '${handleTime}'
group by sr.create_by order by sr.create_by</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>调研数</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>survey_num</name>
        <length>15</length>
        <precision>0</precision>
        <origin>调研数</origin>
        <comments>survey_num</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>128</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>资讯数</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_info</connection>
    <sql>select iv.create_by userid, count(iv.id) news_num
          from info_view iv inner join info i ON iv.info_id = i.id
         where 
           date_format(iv.create_time, '%Y-%m-%d') = '${handleTime}'
group by iv.create_by order by iv.create_by</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>userid</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>资讯数</origin>
        <comments>userid</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>news_num</name>
        <length>15</length>
        <precision>0</precision>
        <origin>资讯数</origin>
        <comments>news_num</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>208</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
