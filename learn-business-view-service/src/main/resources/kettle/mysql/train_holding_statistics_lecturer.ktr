<transformation>
  <info>
    <name>train_holding_statistics_lecturer</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>learnBusinessHost</name>
        <default_value>************</default_value>
        <description>统计-主机host</description>
      </parameter>
      <parameter>
        <name>learnBusinessName</name>
        <default_value>business_view</default_value>
        <description>统计-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnBusinessPassword</name>
        <default_value>123456</default_value>
        <description>统计-密码</description>
      </parameter>
      <parameter>
        <name>learnBusinessPort</name>
        <default_value>30020</default_value>
        <description>统计-端口</description>
      </parameter>
      <parameter>
        <name>learnBusinessUser</name>
        <default_value>root</default_value>
        <description>统计-用户名</description>
      </parameter>
      <parameter>
        <name>learnCourseHost</name>
        <default_value>************</default_value>
        <description>课程-主机host</description>
      </parameter>
      <parameter>
        <name>learnCourseName</name>
        <default_value>course</default_value>
        <description>课程-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnCoursePassword</name>
        <default_value>123456</default_value>
        <description>课程-密码</description>
      </parameter>
      <parameter>
        <name>learnCoursePort</name>
        <default_value>30020</default_value>
        <description>课程-端口</description>
      </parameter>
      <parameter>
        <name>learnCourseUser</name>
        <default_value>root</default_value>
        <description>课程-用户名</description>
      </parameter>
      <parameter>
        <name>learnEvaluationHost</name>
        <default_value>************</default_value>
        <description>评估-主机host</description>
      </parameter>
      <parameter>
        <name>learnEvaluationName</name>
        <default_value>evaluation</default_value>
        <description>评估-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnEvaluationPassword</name>
        <default_value>123456</default_value>
        <description>评估-密码</description>
      </parameter>
      <parameter>
        <name>learnEvaluationPort</name>
        <default_value>30020</default_value>
        <description>评估-端口</description>
      </parameter>
      <parameter>
        <name>learnEvaluationUser</name>
        <default_value>root</default_value>
        <description>评估-用户名</description>
      </parameter>
      <parameter>
        <name>learnLecturerHost</name>
        <default_value>************</default_value>
        <description>讲师-主机host</description>
      </parameter>
      <parameter>
        <name>learnLecturerName</name>
        <default_value>lecturer</default_value>
        <description>讲师-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnLecturerPassword</name>
        <default_value>123456</default_value>
        <description>讲师-密码</description>
      </parameter>
      <parameter>
        <name>learnLecturerPort</name>
        <default_value>30020</default_value>
        <description>讲师-端口</description>
      </parameter>
      <parameter>
        <name>learnLecturerUser</name>
        <default_value>root</default_value>
        <description>讲师-用户名</description>
      </parameter>
      <parameter>
        <name>learnPlanHost</name>
        <default_value>************</default_value>
        <description>培训计划-主机host</description>
      </parameter>
      <parameter>
        <name>learnPlanName</name>
        <default_value>plan</default_value>
        <description>培训计划-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnPlanPassword</name>
        <default_value>123456</default_value>
        <description>培训计划-密码</description>
      </parameter>
      <parameter>
        <name>learnPlanPort</name>
        <default_value>30020</default_value>
        <description>培训计划端口</description>
      </parameter>
      <parameter>
        <name>learnPlanUser</name>
        <default_value>root</default_value>
        <description>培训计划-用户名</description>
      </parameter>
      <parameter>
        <name>learnProjectHost</name>
        <default_value>************</default_value>
        <description>学习项目-主机host</description>
      </parameter>
      <parameter>
        <name>learnProjectName</name>
        <default_value>project</default_value>
        <description>学习项目-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnProjectPassword</name>
        <default_value>123456</default_value>
        <description>学习项目-密码</description>
      </parameter>
      <parameter>
        <name>learnProjectPort</name>
        <default_value>30020</default_value>
        <description>学习项目-端口</description>
      </parameter>
      <parameter>
        <name>learnProjectUser</name>
        <default_value>root</default_value>
        <description>学习项目-用户名</description>
      </parameter>
      <parameter>
        <name>learnTrainHost</name>
        <default_value>************</default_value>
        <description>培训-主机host</description>
      </parameter>
      <parameter>
        <name>learnTrainName</name>
        <default_value>train</default_value>
        <description>培训-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnTrainPassword</name>
        <default_value>123456</default_value>
        <description>培训-密码</description>
      </parameter>
      <parameter>
        <name>learnTrainPort</name>
        <default_value>30020</default_value>
        <description>培训-端口</description>
      </parameter>
      <parameter>
        <name>learnTrainUser</name>
        <default_value>root</default_value>
        <description>培训-用户名</description>
      </parameter>
      <parameter>
        <name>learnUserHost</name>
        <default_value>************</default_value>
        <description>用户-主机host</description>
      </parameter>
      <parameter>
        <name>learnUserName</name>
        <default_value>user</default_value>
        <description>用户-数据库实例名</description>
      </parameter>
      <parameter>
        <name>learnUserPassword</name>
        <default_value>123456</default_value>
        <description>用户-密码</description>
      </parameter>
      <parameter>
        <name>learnUserPort</name>
        <default_value>30020</default_value>
        <description>用户-端口</description>
      </parameter>
      <parameter>
        <name>learnUserUser</name>
        <default_value>root</default_value>
        <description>用户-用户名</description>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2023/05/12 10:49:38.816</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/05/12 11:12:27.964</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>learn_business</name>
    <server>${learnBusinessHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnBusinessName}</database>
    <port>${learnBusinessPort}</port>
    <username>${learnBusinessUser}</username>
    <password>${learnBusinessPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnBusinessPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_lecturer</name>
    <server>${learnLecturerHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnLecturerName}</database>
    <port>${learnLecturerPort}</port>
    <username>${learnLecturerUser}</username>
    <password>${learnLecturerPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnLecturerPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_project</name>
    <server>${learnProjectHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnProjectName}</database>
    <port>${learnProjectPort}</port>
    <username>${learnProjectUser}</username>
    <password>${learnProjectPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnProjectPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>取学习项目主键</from>
      <to>查询培训授课记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>授课记录排序-by学习项目id</from>
      <to>记录集连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查询学习项目</from>
      <to>取学习项目主键</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查询学习项目</from>
      <to>排序记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>追加流</from>
      <to>插入 / 更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录</from>
      <to>记录集连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查询培训授课记录</from>
      <to>授课记录排序-by学习项目id</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>过滤记录</from>
      <to>分组-计算讲师人次</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>去除重复讲师</from>
      <to>分组-计算讲师人数</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>过滤记录</from>
      <to>去除重复讲师</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>去除重复讲师 2</from>
      <to>全部-计算讲师人数</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>将字段值设置为常量</from>
      <to>去除重复讲师 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>将字段值设置为常量</from>
      <to>全部-计算讲师人次</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录集连接</from>
      <to>字段选择</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>过滤记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>将字段值设置为常量</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组-计算讲师人数</from>
      <to>排序记录 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组-计算讲师人次</from>
      <to>排序记录 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>全部-计算讲师人数</from>
      <to>排序记录 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>全部-计算讲师人次</from>
      <to>排序记录 2 4</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 2</from>
      <to>记录集连接 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 2 2</from>
      <to>记录集连接 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 2 3</from>
      <to>记录集连接 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 2 4</from>
      <to>记录集连接 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录集连接 2</from>
      <to>字段选择 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录集连接 2 2</from>
      <to>字段选择 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择 2</from>
      <to>追加流</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择 2 2</from>
      <to>追加流</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>取学习项目主键</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>project_id</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1152</xloc>
      <yloc>784</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>授课记录排序-by学习项目id</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>project_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>848</xloc>
      <yloc>784</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>查询培训授课记录</name>
    <type>TableInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_lecturer</connection>
    <sql>select a.id examination_id, a.train_pro_id project_id, b.id lecturer_id, 1
from lecturer_examination a
inner join lecturer b on a.lecturer_id = b.id
where a.is_del = 0
and b.is_del = 0
and a.train_pro_id = ?</sql>
    <limit>0</limit>
    <lookup>取学习项目主键</lookup>
    <execute_each_row>Y</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>examination_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查询培训授课记录</origin>
        <comments>examination_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>project_id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>查询培训授课记录</origin>
        <comments>project_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>instruction_time</name>
        <length>21</length>
        <precision>2</precision>
        <origin>查询培训授课记录</origin>
        <comments>instruction_time</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>1</name>
        <length>15</length>
        <precision>0</precision>
        <origin>查询培训授课记录</origin>
        <comments>1</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>992</xloc>
      <yloc>784</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>查询学习项目</name>
    <type>TableInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select a.org_id, a.train_category_id, CAST(DATE_ADD(a.start_time, interval - day(a.start_time) + 1 day) as DATE) month, a.id project_id
from project a
where a.is_publish = 1
  and a.is_del = 0
  and a.start_time >= last_day(date_sub(curdate(), interval 3 MONTH)) + interval 1 day
  and a.start_time &lt;= last_day(curdate()) + interval 1 day - interval 1 second
  and a.referenced_id = ''
order by a.id</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查询学习项目</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>train_category_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查询学习项目</origin>
        <comments>train_category_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Date</type>
        <storagetype>normal</storagetype>
        <name>month</name>
        <length>-1</length>
        <precision>-1</precision>
        <origin>查询学习项目</origin>
        <comments>month</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>project_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查询学习项目</origin>
        <comments>project_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>704</xloc>
      <yloc>480</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录集连接</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>授课记录排序-by学习项目id</step1>
    <step2>排序记录</step2>
    <keys_1>
      <key>project_id</key>
    </keys_1>
    <keys_2>
      <key>project_id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>656</xloc>
      <yloc>784</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>过滤记录</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to>去除重复讲师</send_true_to>
    <send_false_to/>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>train_category_id</leftvalue>
        <function>&lt;></function>
        <rightvalue/>
        <value>
          <name>constant</name>
          <type>String</type>
          <text>''</text>
          <length>-1</length>
          <precision>-1</precision>
          <isnull>N</isnull>
          <mask/>
        </value>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>464</xloc>
      <yloc>1136</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>将字段值设置为常量</name>
    <type>SetValueConstant</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <usevar>N</usevar>
    <fields>
      <field>
        <name>train_category_id</name>
        <value/>
        <mask/>
        <set_empty_string>Y</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>1136</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>插入 / 更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_business</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema>business_view</schema>
      <table>train_holding_statistics</table>
      <key>
        <name>org_id</name>
        <field>org_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>train_category_id</name>
        <field>train_category_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>month</name>
        <field>month</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>org_id</name>
        <rename>org_id</rename>
        <update>Y</update>
      </value>
      <value>
        <name>train_category_id</name>
        <rename>train_category_id</rename>
        <update>Y</update>
      </value>
      <value>
        <name>month</name>
        <rename>month</rename>
        <update>Y</update>
      </value>
      <value>
        <name>lecturer_num</name>
        <rename>lecturer_num</rename>
        <update>Y</update>
      </value>
      <value>
        <name>lecturer_times</name>
        <rename>lecturer_times</rename>
        <update>Y</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>656</xloc>
      <yloc>1984</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>追加流</name>
    <type>Append</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <head_name>字段选择 2</head_name>
    <tail_name>字段选择 2 2</tail_name>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>656</xloc>
      <yloc>1840</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>project_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>262</xloc>
      <yloc>784</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组-计算讲师人数</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>org_id</name>
      </field>
      <field>
        <name>train_category_id</name>
      </field>
      <field>
        <name>month</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>lecturer_num</aggregate>
        <subject>lecturer_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>320</xloc>
      <yloc>1424</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组-计算讲师人次</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>org_id</name>
      </field>
      <field>
        <name>train_category_id</name>
      </field>
      <field>
        <name>month</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>lecturer_times</aggregate>
        <subject>lecturer_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>608</xloc>
      <yloc>1296</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>去除重复讲师</name>
    <type>Unique</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <count_rows>N</count_rows>
    <count_field/>
    <reject_duplicate_row>N</reject_duplicate_row>
    <error_description/>
    <fields>
      <field>
        <name>org_id</name>
        <case_insensitive>N</case_insensitive>
      </field>
      <field>
        <name>month</name>
        <case_insensitive>N</case_insensitive>
      </field>
      <field>
        <name>train_category_id</name>
        <case_insensitive>N</case_insensitive>
      </field>
      <field>
        <name>lecturer_id</name>
        <case_insensitive>N</case_insensitive>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>320</xloc>
      <yloc>1280</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>全部-计算讲师人数</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>org_id</name>
      </field>
      <field>
        <name>train_category_id</name>
      </field>
      <field>
        <name>month</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>lecturer_num</aggregate>
        <subject>lecturer_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>704</xloc>
      <yloc>1440</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>全部-计算讲师人次</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>org_id</name>
      </field>
      <field>
        <name>train_category_id</name>
      </field>
      <field>
        <name>month</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>lecturer_times</aggregate>
        <subject>lecturer_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>976</xloc>
      <yloc>1280</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>去除重复讲师 2</name>
    <type>Unique</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <count_rows>N</count_rows>
    <count_field/>
    <reject_duplicate_row>N</reject_duplicate_row>
    <error_description/>
    <fields>
      <field>
        <name>org_id</name>
        <case_insensitive>N</case_insensitive>
      </field>
      <field>
        <name>month</name>
        <case_insensitive>N</case_insensitive>
      </field>
      <field>
        <name>train_category_id</name>
        <case_insensitive>N</case_insensitive>
      </field>
      <field>
        <name>lecturer_id</name>
        <case_insensitive>N</case_insensitive>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>704</xloc>
      <yloc>1296</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字段选择</name>
    <type>SelectValues</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>lecturer_id</name>
        <rename/>
      </field>
      <field>
        <name>org_id</name>
        <rename/>
      </field>
      <field>
        <name>train_category_id</name>
        <rename/>
      </field>
      <field>
        <name>month</name>
        <rename/>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>656</xloc>
      <yloc>944</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>org_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>month</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>train_category_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>320</xloc>
      <yloc>1568</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>org_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>month</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>train_category_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>608</xloc>
      <yloc>1568</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>org_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>month</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>train_category_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>704</xloc>
      <yloc>1568</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 2 4</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>org_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>month</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>train_category_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>976</xloc>
      <yloc>1568</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录集连接 2</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>排序记录 2</step1>
    <step2>排序记录 2 2</step2>
    <keys_1>
      <key>org_id</key>
      <key>train_category_id</key>
      <key>month</key>
    </keys_1>
    <keys_2>
      <key>org_id</key>
      <key>train_category_id</key>
      <key>month</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>464</xloc>
      <yloc>1712</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录集连接 2 2</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>排序记录 2 3</step1>
    <step2>排序记录 2 4</step2>
    <keys_1>
      <key>org_id</key>
      <key>train_category_id</key>
      <key>month</key>
    </keys_1>
    <keys_2>
      <key>org_id</key>
      <key>train_category_id</key>
      <key>month</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>848</xloc>
      <yloc>1712</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字段选择 2</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>org_id</name>
        <rename/>
      </field>
      <field>
        <name>train_category_id</name>
        <rename/>
      </field>
      <field>
        <name>month</name>
        <rename/>
      </field>
      <field>
        <name>lecturer_num</name>
        <rename/>
      </field>
      <field>
        <name>lecturer_times</name>
        <rename/>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>464</xloc>
      <yloc>1840</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字段选择 2 2</name>
    <type>SelectValues</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>org_id</name>
        <rename/>
      </field>
      <field>
        <name>train_category_id</name>
        <rename/>
      </field>
      <field>
        <name>month</name>
        <rename/>
      </field>
      <field>
        <name>lecturer_num</name>
        <rename/>
      </field>
      <field>
        <name>lecturer_times</name>
        <rename/>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>848</xloc>
      <yloc>1840</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
