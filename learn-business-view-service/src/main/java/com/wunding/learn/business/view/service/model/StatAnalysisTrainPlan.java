package com.wunding.learn.business.view.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 统计分析-培训计划统计
 *
 * <AUTHOR> href="mailto:<EMAIL>">ydq</a>
 * @since 2022-11-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stat_analysis_train_plan")
@Schema(name = "StatAnalysisTrainPlan对象", description = "统计分析-培训计划统计")
public class StatAnalysisTrainPlan implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * trainPlanId 培训计划id
     */
    @Schema(description = "trainPlanId 培训计划id")
    @TableId(value = "tp_id", type = IdType.INPUT)
    private String tpId;


    /**
     * 培训计划名称
     */
    @Schema(description = "培训计划名称")
    @TableField("train_plan_name")
    private String trainPlanName;


    /**
     * 年份
     */
    @Schema(description = "年份")
    @TableField("particular_year")
    private String particularYear;


    /**
     * 发布状态：0-未发布 1-已发布
     */
    @Schema(description = "发布状态：0-未发布 1-已发布")
    @TableField("train_status")
    private Integer trainStatus;


    /**
     * 费用预算(元)
     */
    @Schema(description = "费用预算(元)")
    @TableField("train_fee")
    private BigDecimal trainFee;


    /**
     * 主办单位id
     */
    @Schema(description = "主办单位id")
    @TableField("host_unit_id")
    private String hostUnitId;


    /**
     * 主办单位名称
     */
    @Schema(description = "主办单位名称")
    @TableField("host_unit_name")
    private String hostUnitName;


    /**
     * 进度,最多99期
     */
    @Schema(description = "进度,最多99期")
    @TableField("train_period")
    private Integer trainPeriod;


    /**
     * 计划人次
     */
    @Schema(description = "计划人次")
    @TableField("train_trips")
    private Long trainTrips;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 项目和培训计划关系是否删除
     */
    @Schema(description = "项目和培训计划关系是否删除")
    @TableField("tppr_is_del")
    private Integer tpprIsDel;


    /**
     * 已办期数
     */
    @Schema(description = "已办期数")
    @TableField("actual_period")
    private Integer actualPeriod;


    /**
     * 实际费用(元)
     */
    @Schema(description = "实际费用(元)")
    @TableField("actual_cost")
    private Integer actualCost;


    /**
     * 实际人次
     */
    @Schema(description = "实际人次")
    @TableField("actual_person")
    private Integer actualPerson;


}
