package com.wunding.learn.business.view.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.model.StatMonthData;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 累计月统计数据表，此表中的数据是累计数据，不是单月数据 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-10-09
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface StatMonthDataMapper extends BaseMapper<StatMonthData> {

    /**
     * 汇总每个部门的登录人数
     *
     * @param resultOrg 结果组织
     * @return {@link List}<{@link StatMonthData}>
     */
    List<StatMonthData> getMonthData(@Param("orgIds") Set<String> resultOrg);
}
