package com.wunding.learn.business.view.service.model.excitation;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 金币兑换记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exchange_record")
@Schema(name = "ExchangeRecord", description = "金币兑换记录")
public class ExchangeRecord implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;


    /**
     * 激励类型主键
     */
    @Schema(description = "激励类型主键")
    @TableField("excitation_type_id")
    private String excitationTypeId;


    /**
     * 消耗积分、学分、学时的数量
     */
    @Schema(description = "消耗积分、学分、学时的数量")
    @TableField("consume_num")
    private BigDecimal consumeNum;


    /**
     * 兑换的数量
     */
    @Schema(description = "兑换的数量")
    @TableField("reward_num")
    private BigDecimal rewardNum;


    /**
     * 兑换结余
     */
    @Schema(description = "兑换结余")
    @TableField("rest_num")
    private BigDecimal restNum;


    /**
     * 兑换方式:1-自动兑换;2-手动兑换
     */
    @Schema(description = "兑换方式:1-自动兑换;2-手动兑换")
    @TableField("type")
    private Integer type;


    /**
     * 备注
     */
    @Schema(description = "备注")
    @TableField("remark")
    private String remark;


    /**
     * 添加人
     */
    @Schema(description = "添加人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 添加时间
     */
    @Schema(description = "添加时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


}
