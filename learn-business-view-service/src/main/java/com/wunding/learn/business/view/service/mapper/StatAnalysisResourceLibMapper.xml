<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisResourceLibMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisResourceLibMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatAnalysisResourceLib">
        <!--@Table stat_analysis_resource_lib-->
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="courseware_lib" jdbcType="INTEGER"
          property="coursewareLib"/>
        <result column="courseware_package" jdbcType="INTEGER"
          property="coursewarePackage"/>
        <result column="ex_question_lib" jdbcType="INTEGER"
          property="exQuestionLib"/>
        <result column="ex_paper_lib" jdbcType="INTEGER"
          property="exPaperLib"/>
        <result column="ex_exercise_lib" jdbcType="INTEGER"
          property="exExerciseLib"/>
        <result column="evaluation_lib" jdbcType="INTEGER"
          property="evaluationLib"/>
        <result column="survey_lib" jdbcType="INTEGER"
          property="surveyLib"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, courseware_lib, courseware_package, ex_question_lib, ex_paper_lib, ex_exercise_lib, evaluation_lib, survey_lib
    </sql>
</mapper>
