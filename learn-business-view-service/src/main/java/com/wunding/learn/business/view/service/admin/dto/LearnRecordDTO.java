package com.wunding.learn.business.view.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 学员档案
 *
 * <AUTHOR>
 * @date 2022/11/8
 */
@Data
@Accessors(chain = true)
@Schema(name = "LearnRecordDTO", description = "学员档案统计数据dto对象")
public class LearnRecordDTO {

    @Schema(description = "用户ID")
    private String id;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "账号")
    private String loginName;

    @Schema(description = "性别 1-男 2-女")
    private Integer sex;

    @Schema(description = "电话")
    private String telephone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "生日")
    private Date birthday;

    @Schema(description = "入职日期")
    private Date joinDate;

    @Schema(description = "部门名称")
    private String orgName;

    @Schema(description = "岗位名称")
    private String postName;

    @Schema(description = "新学课程数")
    private Long totalCourse;

    @Schema(description = "学习时长")
    private Long totalLearn;

    @Schema(description = "参加项目数")
    private Long projectNum;

    @Schema(description = "参加面授班级数")
    private Long faceProjectNum;

    @Schema(description = "参加培训项目数")
    private Long trainNum;

    @Schema(description = "小计")
    private Long subtotal;

    @Schema(description = "参加考试数")
    private Long examNum;

    @Schema(description = "浏览资讯数")
    private Long newsNum;

    @Schema(description = "参加调研数")
    private Long surveyNum;

    @Schema(description = "点赞数")
    private Long totalAgree;

    @Schema(description = "评论数")
    private Long totalComment;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;

    @Schema(description = "部门id", hidden = true)
    private String orgId;

}