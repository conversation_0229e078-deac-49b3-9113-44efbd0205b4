package com.wunding.learn.business.view.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 内部讲师统计-按类别表
 *
 * <AUTHOR> href="mailto:<EMAIL>">cjn</a>
 * @since 2022-11-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stat_analysis_inside_lecturer_category")
@Schema(name = "StatAnalysisInsideLecturerCategory对象", description = "内部讲师统计-按类别表")
public class StatAnalysisInsideLecturerCategory implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 讲师类别id
     */
    @Schema(description = "讲师类别id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 讲师分类code
     */
    @Schema(description = "讲师分类code")
    @TableField("code")
    private String code;


    /**
     * 讲师分类名称
     */
    @Schema(description = "讲师分类名称")
    @TableField("name")
    private String name;


    /**
     * 是否启用:0-禁用,1-启用
     */
    @Schema(description = "是否启用:0-禁用,1-启用")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 是否删除:0-未删除,1-已删除
     */
    @Schema(description = "是否删除:0-未删除,1-已删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 总人数
     */
    @Schema(description = "总人数")
    @TableField("people_num")
    private Long peopleNum;


    /**
     * 满意度
     */
    @Schema(description = "满意度")
    @TableField("overall_score")
    private BigDecimal overallScore;


    /**
     * 年龄
     */
    @Schema(description = "年龄")
    @TableField("age")
    private Integer age;


    /**
     * 学历
     */
    @Schema(description = "学历")
    @TableField("education")
    private String education;


    /**
     * 职级
     */
    @Schema(description = "职级")
    @TableField("job_level")
    private String jobLevel;


    /**
     * 授课课时（分钟）
     */
    @Schema(description = "授课课时（分钟）")
    @TableField("instruction_time")
    private BigDecimal instructionTime;


}
