package com.wunding.learn.business.view.service.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 日期月份对照表
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stat_base_time")
@Schema(name = "StatBaseTime", description = "日期月份对照表")
public class StatBaseTime implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 日期
     */
    @Schema(description = "日期")
    @TableField("access_date")
    private Date accessDate;


    /**
     * 日期对应的月份
     */
    @Schema(description = "日期对应的月份")
    @TableField("access_month")
    private Date accessMonth;


}
