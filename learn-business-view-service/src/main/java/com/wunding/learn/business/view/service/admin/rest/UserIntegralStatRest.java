package com.wunding.learn.business.view.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.business.view.service.admin.dto.LecturerIntegralDTO;
import com.wunding.learn.business.view.service.admin.dto.UserIntegralDTO;
import com.wunding.learn.business.view.service.admin.dto.UserIntegralDetailDTO;
import com.wunding.learn.business.view.service.admin.query.IntegralClearQuery;
import com.wunding.learn.business.view.service.admin.query.LecturerIntegralQuery;
import com.wunding.learn.business.view.service.admin.query.UserIntegralQuery;
import com.wunding.learn.business.view.service.service.ExcitationService;
import com.wunding.learn.business.view.service.service.IStatAnalysisUserIntegralDetailService;
import com.wunding.learn.business.view.service.service.IStatAnalysisUserIntegralService;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * </p> 积分统计表
 *
 * <AUTHOR> href="mailto:<EMAIL>">suchenyu</a>
 * @since 2022-11-10
 */
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping("${module.businessView.contentPath:/}userIntegral")
@Tag(description = "积分统计", name = "UserIntegralStatRest")
public class UserIntegralStatRest {

    private final IStatAnalysisUserIntegralService statAnalysisUserIntegralService;
    private final IStatAnalysisUserIntegralDetailService statAnalysisUserIntegralDetailService;
    private final ExcitationService excitationService;

    @GetMapping("/list")
    @Operation(operationId = "list_UserIntegral", summary = "积分统计查询", description = "积分统计查询")
    public Result<PageInfo<UserIntegralDTO>> list(@ParameterObject @Valid UserIntegralQuery userIntegralQuery) {
        PageInfo<UserIntegralDTO> data = statAnalysisUserIntegralService.listData(userIntegralQuery);
        return Result.success(data);
    }

    @GetMapping("/{userId}/detail")
    @Operation(operationId = "detail_UserIntegral", summary = "积分详情查询", description = "积分详情查询")
    public Result<PageInfo<UserIntegralDetailDTO>> detail(
        @PathVariable @Parameter(description = "用户id", example = "admin", required = true) String userId,
        BasePageQuery basePageQuery) {
        PageInfo<UserIntegralDetailDTO> data = statAnalysisUserIntegralDetailService.detail(userId, basePageQuery);
        return Result.success(data);
    }

    @PostMapping("/exportData")
    @Operation(operationId = "exportData_UserIntegral", summary = "导出积分统计列表的数据", description = "导出积分统计列表的数据")
    public Result<ExportResultDTO> exportData(@ParameterObject @Valid UserIntegralQuery userIntegralQuery) {
        statAnalysisUserIntegralService.export(userIntegralQuery);
        return Result.success();
    }


    @GetMapping("/listLecturer")
    @Operation(operationId = "listLecturer", summary = "积分统计查询-按讲师", description = "积分统计查询-按讲师")
    public Result<PageInfo<LecturerIntegralDTO>> listLecturer(@ParameterObject @Valid LecturerIntegralQuery query) {
        PageInfo<LecturerIntegralDTO> data = statAnalysisUserIntegralService.listLecturer(query);
        return Result.success(data);
    }

    @PostMapping("/exportListLecturerData")
    @Operation(operationId = "exportListLecturerData", summary = "导出积分统计查询-按讲师", description = "导出积分统计查询-按讲师")
    public Result<ExportResultDTO> exportListLecturerData(@RequestBody @Valid LecturerIntegralQuery query) {
        statAnalysisUserIntegralService.exportListLecturerData(query);
        return Result.success();
    }

    @PutMapping("/clean")
    @Operation(operationId = "cleanIntegral_UserIntegral", summary = "积分清零", description = "积分清零")
    public Result<Void> cleanIntegral(IntegralClearQuery query) {
        query.setCurrentUserId(UserThreadContext.getUserId());
        excitationService.cleanIntegral(query);
        // 操作成功，但需要返回自定义提示信息
        return Result.fail(ErrorNoEnum.SUCCESS.getErrorCode(), "操作成功,积分数据将会在稍后进行更新,请等待");
    }
}