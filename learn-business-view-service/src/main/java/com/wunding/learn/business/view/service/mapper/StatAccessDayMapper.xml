<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatAccessDayMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatAccessDayMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatAccessDay">
            <!--@Table stat_access_day-->
                    <result column="access_date" jdbcType="TIMESTAMP"
                            property="accessDate"/>
                    <result column="org_id" jdbcType="VARCHAR"
                            property="orgId"/>
                    <result column="access_num" jdbcType="INTEGER"
                            property="accessNum"/>
                    <result column="online_num" jdbcType="INTEGER"
                            property="onlineNum"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            access_date, org_id, access_num, online_num
        </sql>

    <select id="getAccessMonthNum" resultType="java.lang.Integer" useCache="false">
        select sum(access_num)
        from stat_access_day
        where org_id = #{orgId}
          and date_format(access_date, '%Y-%m') = date_format(#{lastMonth}, '%Y-%m')
    </select>

</mapper>
