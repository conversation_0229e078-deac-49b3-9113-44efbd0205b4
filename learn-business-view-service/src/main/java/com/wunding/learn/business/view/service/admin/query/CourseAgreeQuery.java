package com.wunding.learn.business.view.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Collection;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * 课程同意查询
 *
 * <AUTHOR>
 * @date 2022/11/08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CourseAgreeQuery extends BasePageQuery {

    @Parameter(description = "课程id", required = true)
    @NotBlank
    @Length(max = 36, message = "课程id长度错误")
    private String courseId;

    @Parameter(description = "userOrLoginNameLike")
    private String userOrLoginNameLike;

    @Parameter(description = "按用户id查询", hidden = true)
    private Collection<String> userIds;
}
