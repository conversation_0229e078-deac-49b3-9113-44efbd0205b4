package com.wunding.learn.business.view.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 课件学习统计详情
 *
 * <AUTHOR>
 * @date 2022/11/8
 */
@Data
@Accessors(chain = true)
@Schema(name = "UserCoursewareLearnDetailDTO", description = "课件学习记录对象")
public class UserCoursewareLearnDetailDTO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "课件id")
    private String cwId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "学习状态")
    private Integer isLearned;

}