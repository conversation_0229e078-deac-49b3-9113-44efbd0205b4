<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisExamStateMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisExamStateMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatAnalysisExamState">
            <!--@Table stat_analysis_exam_state-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="exam_name" jdbcType="VARCHAR"
                            property="examName"/>
                    <result column="org_id" jdbcType="VARCHAR"
                            property="orgId"/>
                    <result column="org_name" jdbcType="VARCHAR"
                            property="orgName"/>
                    <result column="level_path" jdbcType="VARCHAR"
                            property="levelPath"/>
                    <result column="start_time" jdbcType="TIMESTAMP"
                            property="startTime"/>
                    <result column="question_count" jdbcType="INTEGER"
                            property="questionCount"/>
                    <result column="part_count" jdbcType="INTEGER"
                            property="partCount"/>
                    <result column="post_count" jdbcType="INTEGER"
                            property="postCount"/>
                    <result column="pass_count" jdbcType="INTEGER"
                            property="passCount"/>
                    <result column="pass_rate" jdbcType="INTEGER"
                            property="passRate"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, exam_name, org_id, org_name, level_path, start_time, question_count, part_count, post_count, pass_count, pass_rate
        </sql>

</mapper>
