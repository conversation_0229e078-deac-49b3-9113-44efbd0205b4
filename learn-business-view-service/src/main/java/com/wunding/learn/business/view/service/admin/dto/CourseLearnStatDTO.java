package com.wunding.learn.business.view.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 课程学习统计数据
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@Data
@Accessors(chain = true)
@Schema(name = "CourseLearnStatDTO", description = "课程学习统计数据dto对象")
public class CourseLearnStatDTO {

    @Schema(description = "课程ID")
    private String courseId;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "类别名称")
    private String categoryName;

    @Schema(description = "发布时间")
    private Date publishTime;

    @Schema(description = "课件数")
    private String totalCw;

    @Schema(description = "已学完")
    private String finishNum;

    @Schema(description = "未学完")
    private String underwayNum;

    @Schema(description = "点赞数")
    private Integer totalAgree;

    @Schema(description = "综合星级")
    private BigDecimal synthesizeStar;

    @Schema(description = "评星人数")
    private Integer starUserCount;

    @Schema(description = "评论次数")
    private Integer totalComment;

    @Schema(description = "评论人数")
    private Integer totalCommPerson;

    @Schema(description = "下载次数")
    private Integer totalDownload;
}