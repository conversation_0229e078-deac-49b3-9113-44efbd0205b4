package com.wunding.learn.business.view.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 上线用户统计数据dto对象
 *
 * <AUTHOR>
 * @date 2022/11/10 10:39
 */
@Data
@Builder
@Accessors(chain = true)
@Schema(name = "StatAnalysisOnlineUserDTO", description = "上线用户统计数据dto对象")
public class StatAnalysisOnlineUserDTO {
    /**
     * 日期-按天
     */
    @Schema(description = "日期-按天")
    private String accessDate;

    /**
     * 日期-按月
     */
    @Schema(description = "日期-按月")
    private String accessMonth;

    /**
     * 部门
     */
    @Schema(description = "部门")
    private String orgName;

    /**
     * 部门全路径名称
     */
    @Schema(description = "部门全路径名称")
    private String levelPathName;

    /**
     * 部门用户数
     */
    @Schema(description = "部门用户数")
    private Integer totalUserNum;


    /**
     * 上线用户数
     */
    @Schema(description = "上线用户数")
    private Integer onlineNum;

    /**
     * 上线用户占比
     */
    @Schema(description = "上线用户占比")
    private String proportionOfOnlineUsers;

    /**
     * 第一次上线用户
     */
    @Schema(description = "第一次上线用户")
    private Integer newLoginNum;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;

}
