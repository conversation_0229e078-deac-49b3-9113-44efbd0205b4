package com.wunding.learn.business.view.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stat_analysis_resource")
@Schema(name = "StatAnalysisResource", description = "")
public class StatAnalysisResource implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableId(value = "user_id", type = IdType.INPUT)
    private String userId;


    /**
     * 资源类型:0课程 1练习 2案例 3知识 4教室
     */
    @Schema(description = "资源类型")
    private Integer type;


    /**
     * 资源总数
     */
    @Schema(description = "资源总数")
    @TableField("total")
    private Integer total;


    /**
     * 昨日新增
     */
    @Schema(description = "昨日新增")
    @TableField("yesterday_increase")
    private Integer yesterdayIncrease;


    /**
     * 本月新增
     */
    @Schema(description = "本月新增")
    @TableField("month_increase")
    private Integer monthIncrease;


    /**
     * 上个月新增
     */
    @Schema(description = "上个月新增")
    @TableField("last_month_increase")
    private Integer lastMonthIncrease;


}
