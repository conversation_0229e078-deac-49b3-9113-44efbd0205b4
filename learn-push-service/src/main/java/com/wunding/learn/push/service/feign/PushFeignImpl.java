package com.wunding.learn.push.service.feign;

import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.push.api.dto.EnablePushNoticeChannelDTO;
import com.wunding.learn.push.api.dto.MailSendDTO;
import com.wunding.learn.push.api.dto.PushEventDTO;
import com.wunding.learn.push.api.dto.SavePushManageDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.push.service.biz.IPushNoticeChannelBiz;
import com.wunding.learn.push.service.biz.IPushSendBiz;
import com.wunding.learn.push.service.model.PushNoticeEvent;
import com.wunding.learn.push.service.service.IPushManageService;
import com.wunding.learn.push.service.service.IPushNoticeEventService;
import com.wunding.learn.push.service.service.IPushSendService;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/9/22
 */
@RestController
@RequestMapping("${module.push.contentPath:/}")
@Slf4j
public class PushFeignImpl implements PushFeign {

    @Resource
    private IPushManageService pushManageService;
    @Resource
    private IPushSendService pushSendService;
    @Resource
    private IPushSendBiz pushSendBiz;
    @Resource
    private IPushNoticeChannelBiz pushNoticeChannelBiz;
    @Resource
    private IPushNoticeEventService pushNoticeEventService;

    @Override
    public void sendPush(SendPushDTO sendPushDTO) {
        pushSendBiz.sendPush(sendPushDTO);
    }

    @Override
    public void enablePushNoticeChannel(EnablePushNoticeChannelDTO enablePushNoticeChannelDTO) {
        pushNoticeChannelBiz.enablePushNoticeChannel(enablePushNoticeChannelDTO);
    }

    @Override
    public void savePush(SavePushManageDTO savePushManageDTO) {
        //  Noncompliance - method is empty
    }

    @Override
    public void mailSend(MailSendDTO dto) {
        pushSendService.mailSend(dto);
    }

    @Override
    public List<PushEventDTO> getPushEvent(String functionModuleCode, Integer type) {
        List<PushNoticeEvent> list = pushNoticeEventService.lambdaQuery()
            .eq(PushNoticeEvent::getFunctionModuleCode, functionModuleCode)
            .eq(PushNoticeEvent::getType, type)
            .list();
        return BeanListUtils.copyList(list, PushEventDTO.class);
    }

    @Override
    public List<PushEventDTO> getCompletePushEvent() {
        List<PushNoticeEvent> list = pushNoticeEventService.lambdaQuery()
            .eq(PushNoticeEvent::getFunctionSubModuleCode, "complete")
            .list();
        return BeanListUtils.copyList(list, PushEventDTO.class);
    }
}
