package com.wunding.learn.push.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 推送消息详情表
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("push_message_detail")
@Schema(name = "PushMessageDetail对象", description = "推送消息详情表")
public class PushMessageDetail implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 消息id
     */
    @Schema(description = "消息id")
    @TableField("msg_id")
    private String msgId;


    /**
     * 通知渠道id
     */
    @Schema(description = "通知渠道id")
    @TableField("channel_id")
    private String channelId;

    /**
     * 企业微信类型（支持配置多个企业微信）：0-澳美点击 1-奥美领航
     */
    @Schema(description = "企业微信类型（支持配置多个企业微信）：0-澳美点击 1-奥美领航")
    @TableField("multi_channel_type")
    private Integer multiChannelType;


    /**
     * 推送模板标题
     */
    @Schema(description = "推送模板标题")
    @TableField("push_template_title")
    private String pushTemplateTitle;


    /**
     * 推送模板内容
     */
    @Schema(description = "推送模板内容")
    @TableField("push_template_content")
    private String pushTemplateContent;


    /**
     * 推送标题
     */
    @Schema(description = "推送标题")
    @TableField("push_title")
    private String pushTitle;


    /**
     * 推送内容，指业务名称
     */
    @Schema(description = "推送内容，指业务名称")
    @TableField("push_content")
    private String pushContent;


    /**
     * 推送点击后跳转的链接，第三方对接渠道必填
     */
    @Schema(description = "推送点击后跳转的链接，第三方对接渠道必填")
    @TableField("push_url")
    private String pushUrl;


    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    @TableField("extra")
    private String extra;

    /**
     * 是否删除 0否 1是
     */
    @Schema(description = "是否删除 0否 1是")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 是否启用 0否 1是
     */
    @Schema(description = "是否启用 0否 1是")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
