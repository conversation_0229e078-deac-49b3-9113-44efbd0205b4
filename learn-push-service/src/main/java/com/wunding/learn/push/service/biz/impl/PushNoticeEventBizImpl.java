package com.wunding.learn.push.service.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.dto.IdName;
import com.wunding.learn.common.enums.language.LanguageEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.push.PushModuleRouterEnum;
import com.wunding.learn.common.enums.push.PushNoticeEventNameEnum;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.push.api.enums.PushChannelEnum;
import com.wunding.learn.push.service.admin.dto.PushModuleDTO;
import com.wunding.learn.push.service.admin.dto.PushMsgBizModuleDTO;
import com.wunding.learn.push.service.admin.dto.PushNoticeChannelDTO;
import com.wunding.learn.push.service.admin.dto.PushNoticeEventDTO;
import com.wunding.learn.push.service.admin.dto.PushNoticeEventSetDTO;
import com.wunding.learn.push.service.admin.query.PushMessageEventQuery;
import com.wunding.learn.push.service.admin.query.PushNoticeChannelSetQuery;
import com.wunding.learn.push.service.admin.query.PushNoticeEventQuery;
import com.wunding.learn.push.service.admin.query.PushNoticeEventSetQuery;
import com.wunding.learn.push.service.biz.IPushNoticeEventBiz;
import com.wunding.learn.push.service.constant.PushCycleEnum;
import com.wunding.learn.push.service.constant.PushTypeEnum;
import com.wunding.learn.push.service.model.PushMessageTemplateGlobal;
import com.wunding.learn.push.service.model.PushMessageTemplateResource;
import com.wunding.learn.push.service.model.PushNoticeChannel;
import com.wunding.learn.push.service.model.PushNoticeEvent;
import com.wunding.learn.push.service.service.IPushMessageTemplateGlobalService;
import com.wunding.learn.push.service.service.IPushMessageTemplateResourceService;
import com.wunding.learn.push.service.service.IPushNoticeChannelService;
import com.wunding.learn.push.service.service.IPushNoticeEventService;
import com.wunding.learn.user.api.service.RouterFeign;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 推送通知事件 业务服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-07-05
 */
@Slf4j
@Service("pushNoticeEventBiz")
public class PushNoticeEventBizImpl implements IPushNoticeEventBiz {

    @Resource
    private IPushNoticeEventService pushNoticeEventService;
    @Resource
    private IPushNoticeChannelService pushNoticeChannelService;
    @Resource
    private IPushMessageTemplateGlobalService pushMessageTemplateGlobalService;
    @Resource
    private IPushMessageTemplateResourceService pushMessageTemplateResourceService;
    @Resource
    private RouterFeign routerFeign;

    @Override
    public List<PushModuleDTO> getFunctionModule(String parentModule) {
        List<PushModuleDTO> list;
        List<String> routerIds = routerFeign.getRouterNames();
        if (StringUtils.isBlank(parentModule)) {
            list = pushNoticeEventService.list(
                new LambdaQueryWrapper<PushNoticeEvent>().select(PushNoticeEvent::getFunctionModule,
                    PushNoticeEvent::getFunctionModuleCode)
                    .groupBy(Arrays.asList(PushNoticeEvent::getFunctionModule, PushNoticeEvent::getFunctionModuleCode))
                    .orderByAsc(PushNoticeEvent::getFunctionModuleCode)).stream().map(event -> {
                PushModuleDTO dto = new PushModuleDTO();
                dto.setModuleName(event.getFunctionModule());
                dto.setModuleCode(event.getFunctionModuleCode());
                return dto;
            }).filter(pushModuleDTO -> PushModuleRouterEnum.isModuleAccessible(pushModuleDTO.getModuleName(), routerIds))
                    .collect(Collectors.toList());
            list.forEach(pushModuleDTO -> pushModuleDTO.setModuleName(I18nUtil.getMessage(pushModuleDTO.getModuleName())));
        } else {
            list = pushNoticeEventService.list(
                new LambdaQueryWrapper<PushNoticeEvent>().select(PushNoticeEvent::getFunctionSubModule,
                    PushNoticeEvent::getFunctionSubModuleCode).eq(PushNoticeEvent::getFunctionModuleCode, parentModule)
                    .groupBy(
                        Arrays.asList(PushNoticeEvent::getFunctionSubModule, PushNoticeEvent::getFunctionSubModuleCode))
                    .orderByAsc(PushNoticeEvent::getFunctionSubModuleCode)).stream().map(event -> {
                PushModuleDTO dto = new PushModuleDTO();
                dto.setModuleName(event.getFunctionSubModule());
                dto.setModuleCode(event.getFunctionSubModuleCode());
                return dto;
            }).filter(pushModuleDTO -> StringUtils.isNotBlank(pushModuleDTO.getModuleCode()))
                    .filter(pushModuleDTO -> PushModuleRouterEnum.isModuleAccessible(pushModuleDTO.getModuleName(), routerIds))
                    .collect(Collectors.toList());
            list.forEach(pushModuleDTO -> pushModuleDTO.setModuleName(I18nUtil.getMessage(pushModuleDTO.getModuleName())));
        }
        return list;
    }

    @Override
    public PageInfo<PushNoticeEventDTO> queryPage(PushNoticeEventQuery query) {
        List<String> routerIds = routerFeign.getRouterNames();
        LambdaQueryWrapper<PushNoticeEvent> queryWrapper = new LambdaQueryWrapper<PushNoticeEvent>()
                .eq(StringUtils.isNotBlank(query.getFunctionModuleCode()),
                        PushNoticeEvent::getFunctionModuleCode, query.getFunctionModuleCode())
                .eq(StringUtils.isNotBlank(query.getFunctionSubModuleCode()),
                        PushNoticeEvent::getFunctionSubModuleCode, query.getFunctionSubModuleCode())
                .orderByAsc(PushNoticeEvent::getNum);
        List<PushNoticeEvent> allData = pushNoticeEventService.list(queryWrapper);

        List<PushNoticeEvent> filtered = allData.stream()
                .filter(p -> PushModuleRouterEnum.isModuleAccessible(p.getFunctionModule(), routerIds))
                .collect(Collectors.toList());

        int from = (query.getPageNo() - 1) * query.getPageSize();
        int to = Math.min(from + query.getPageSize(), filtered.size());
        List<PushNoticeEvent> pageData = filtered.subList(from, to);
        PageInfo<PushNoticeEvent> pageInfo = new PageInfo<>(pageData);
        pageInfo.setTotal(filtered.size());

        // 如果没有查到数据直接返回
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            PageInfo<PushNoticeEventDTO> pageInfoDTO = new PageInfo<>();
            BeanUtils.copyProperties(pageInfo, pageInfoDTO);
            pageInfoDTO.setList(Collections.emptyList());
            return pageInfoDTO;
        }

        // 查询所有支持的通知渠道
        List<PushNoticeChannel> channels = pushNoticeChannelService.list(
            new LambdaQueryWrapper<PushNoticeChannel>().select(PushNoticeChannel::getId, PushNoticeChannel::getCode,
                PushNoticeChannel::getName).eq(PushNoticeChannel::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
                .orderByAsc(PushNoticeChannel::getCode));

        // 查询指定事件全局消息配置信息
        Map<String, PushMessageTemplateGlobal> globalMap = pushMessageTemplateGlobalService.list(
            new LambdaQueryWrapper<PushMessageTemplateGlobal>().select(PushMessageTemplateGlobal::getEventId,
                PushMessageTemplateGlobal::getChannelId, PushMessageTemplateGlobal::getLanguage,
                PushMessageTemplateGlobal::getEnableDefaultAutomaticPush).in(PushMessageTemplateGlobal::getEventId,
                pageInfo.getList().stream().map(PushNoticeEvent::getId).collect(Collectors.toSet()))).stream().collect(
            Collectors.toMap(global -> global.getEventId().concat(global.getChannelId()).concat(global.getLanguage()),
                Function.identity()));

        // 响应数据处理
        List<PushNoticeEventDTO> eventDTOList = new ArrayList<>();
        pageInfo.getList().forEach(event -> {
            PushNoticeEventDTO eventDTO = new PushNoticeEventDTO();
            BeanUtils.copyProperties(event, eventDTO);
            List<PushNoticeChannelDTO> channelDTOList = new ArrayList<>();
            channels.forEach(channel -> {
                PushNoticeChannelDTO channelDTO = new PushNoticeChannelDTO();
                BeanUtils.copyProperties(channel, channelDTO);

                // 获取指定事件、渠道、语言的全局消息模板配置
                PushMessageTemplateGlobal global = globalMap.get(
                    event.getId().concat(channel.getId()).concat(LanguageEnum.ENGLISH.getLang()));
                channelDTO
                    .setEnableDefaultAutomaticPush(global == null ? null : global.getEnableDefaultAutomaticPush());
                channelDTO.setName(I18nUtil.getMessage(channelDTO.getName()));
                channelDTOList.add(channelDTO);
            });
            eventDTO.setFunctionModule(I18nUtil.getMessage(eventDTO.getFunctionModule()));
            eventDTO.setFunctionSubModule(I18nUtil.getMessage(eventDTO.getFunctionSubModule()));
            eventDTO.setName(I18nUtil.getMessage(eventDTO.getName()));
            eventDTO.setPushNoticeChannelDTOList(channelDTOList);
            //企微不支持a标签，依据产品要求，现阶段过滤掉
            eventDTO.setContentLabel(filerLinkContentLabel(eventDTO.getContentLabel()));
            eventDTO.setContentLabelEn(filerLinkContentLabel(eventDTO.getContentLabelEn()));
            eventDTOList.add(eventDTO);
        });
        PageInfo<PushNoticeEventDTO> pageInfoDTO = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, pageInfoDTO);
        pageInfoDTO.setList(eventDTOList);

        return pageInfoDTO;
    }

    /**
     * 过滤链接
     *
     * @param contentLabel 消息内容标签
     * @return 过滤后的消息内容标签
     */
    public String filerLinkContentLabel(String contentLabel) {
        String[] labelArray = contentLabel.split("\\|");
        StringBuilder str = new StringBuilder();
        for (String label : labelArray) {
            if (!label.contains("链接") && !label.contains("Link")) {
                str.append(label).append("|");
            }
        }
        return str.substring(0, str.length() - 1);
    }

    @Override
    public List<PushMsgBizModuleDTO> getPushMessageModuleList() {
        List<String> routerIds = routerFeign.getRouterNames();
        return pushNoticeEventService.list(
            new LambdaQueryWrapper<PushNoticeEvent>().select(PushNoticeEvent::getFunctionModule,
                PushNoticeEvent::getFunctionModuleCode, PushNoticeEvent::getFunctionSubModule,
                PushNoticeEvent::getFunctionSubModuleCode).ne(PushNoticeEvent::getFunctionModuleCode, "quick_project").
                groupBy(
                    Arrays.asList(PushNoticeEvent::getFunctionModule, PushNoticeEvent::getFunctionModuleCode,
                        PushNoticeEvent::getFunctionSubModule, PushNoticeEvent::getFunctionSubModuleCode)).orderByAsc(
                Arrays.asList(PushNoticeEvent::getFunctionModule, PushNoticeEvent::getFunctionModuleCode,
                    PushNoticeEvent::getFunctionSubModule, PushNoticeEvent::getFunctionSubModuleCode))).stream()
            .map(event -> {
                PushMsgBizModuleDTO dto = new PushMsgBizModuleDTO();
                BeanUtils.copyProperties(event, dto);
                dto.setFunctionModule(I18nUtil.getDefaultMessage(dto.getFunctionModule()));
                dto.setFunctionSubModule(I18nUtil.getDefaultMessage(dto.getFunctionSubModule()));
                dto.setId(event.getFunctionModuleCode().concat("_").concat(event.getFunctionSubModuleCode()));
                return dto;
            }).filter(p -> PushModuleRouterEnum.isModuleAccessible(p.getFunctionModule(), routerIds)).collect(Collectors.toList());
    }

    @Override
    public List<IdName> getPushMessageEventList(PushMessageEventQuery query) {
        List<PushNoticeEvent> events = pushNoticeEventService.list(
            new LambdaQueryWrapper<PushNoticeEvent>().eq(PushNoticeEvent::getFunctionModuleCode,
                query.getFunctionModuleCode()).eq(PushNoticeEvent::getFunctionSubModuleCode,
                StringUtils.isBlank(query.getFunctionSubModuleCode()) ? "" : query.getFunctionSubModuleCode())
                .orderByAsc(PushNoticeEvent::getNum));
        return BeanListUtils.copyList(events, IdName.class);
    }

    @Override
    public List<PushNoticeEventSetDTO> getPushNoticeEventSetList(PushNoticeEventSetQuery query) {
        // 查询指定模块通知事件列表
        List<PushNoticeEvent> events = pushNoticeEventService.list(
            new LambdaQueryWrapper<PushNoticeEvent>().eq(PushNoticeEvent::getFunctionModuleCode,
                query.getFunctionModuleCode()).eq(PushNoticeEvent::getFunctionSubModuleCode,
                StringUtils.isBlank(query.getFunctionSubModuleCode()) ? "" : query.getFunctionSubModuleCode())
                .orderByAsc(PushNoticeEvent::getNum));

        // 通知事件ID集合
        Set<String> eventIds = events.stream().map(PushNoticeEvent::getId).collect(Collectors.toSet());

        // 全局消息模板配置 查询已开启默认自动推送的 通知事件
        Set<String> enableDefaultAutomaticPushEventIds = pushMessageTemplateGlobalService.list(
            new LambdaQueryWrapper<PushMessageTemplateGlobal>().select(PushMessageTemplateGlobal::getEventId)
                .in(!eventIds.isEmpty(), PushMessageTemplateGlobal::getEventId, eventIds)
                .eq(PushMessageTemplateGlobal::getLanguage, LanguageEnum.ENGLISH.getLang())
                .eq(PushMessageTemplateGlobal::getEnableDefaultAutomaticPush, GeneralJudgeEnum.CONFIRM.getValue()))
            .stream().map(PushMessageTemplateGlobal::getEventId).collect(Collectors.toSet());

        // 资源消息模板配置 查询已开启自动推送的 通知事件
        Map<String, PushMessageTemplateResource> templateResourceMap = new HashMap<>();

        // 资源操作状态是否添加
        boolean isAdd = "add".equals(query.getOperateState());
        if (!isAdd) {
            templateResourceMap = pushMessageTemplateResourceService.list(
                    new LambdaQueryWrapper<PushMessageTemplateResource>().in(PushMessageTemplateResource::getEventId,
                            eventIds).eq(PushMessageTemplateResource::getResourceId, query.getResourceId())
                        .eq(PushMessageTemplateResource::getResourceType, query.getResourceType())
                        .eq(PushMessageTemplateResource::getEnableAutomaticPush, GeneralJudgeEnum.CONFIRM.getValue()))
                .stream().collect(Collectors.toMap(PushMessageTemplateResource::getEventId, Function.identity(),
                    (key1, key2) -> key1));

        }

        Map<String, PushMessageTemplateResource> finalTemplateResourceMap = templateResourceMap;
        return events.stream().map(event -> {
            PushNoticeEventSetDTO eventSetDTO = new PushNoticeEventSetDTO();
            BeanUtils.copyProperties(event, eventSetDTO);
            PushNoticeEventNameEnum noticeEvenNameEnum = PushNoticeEventNameEnum.getNoticeEvenNameByKey(
                event.getName());
            if (noticeEvenNameEnum == null) {
                eventSetDTO.setName(event.getName());
            } else {
                eventSetDTO.setName(noticeEvenNameEnum.getName());
            }
            eventSetDTO.setName(I18nUtil.getMessage(eventSetDTO.getName()));
            // 开启默认推送渠道 0否 1是，为0时提示管理员未为此类型配置发送渠道，所有设置项禁用
            eventSetDTO.setEnableDefaultPushChannel(
                enableDefaultAutomaticPushEventIds.contains(event.getId()) ? GeneralJudgeEnum.CONFIRM.getValue()
                    : GeneralJudgeEnum.NEGATIVE.getValue());
            // 允许开启手动推送 添加时不允许手动推送
            eventSetDTO.setAllowEnableManualPush(
                isAdd ? GeneralJudgeEnum.NEGATIVE.getValue() : event.getEnableManualPush());
            // 允许开启自动推送
            eventSetDTO.setAllowEnableAutomaticPush(event.getEnableAutomaticPush());

            //企微不支持a标签，依据产品要求，现阶段过滤掉
            eventSetDTO.setContentLabel(filerLinkContentLabel(event.getContentLabel()));
            eventSetDTO.setContentLabelEn(filerLinkContentLabel(event.getContentLabelEn()));

            // 编辑时渲染配置项处理
            PushMessageTemplateResource templateResource = finalTemplateResourceMap.get(event.getId());
            if (!isAdd && null != templateResource) {
                extractEditSet(eventSetDTO, templateResource);


            } else {
                // 开启自动推送 0否
                eventSetDTO.setEnableAutomaticPush(GeneralJudgeEnum.NEGATIVE.getValue());
            }

            return eventSetDTO;
        }).collect(Collectors.toList());

    }

    private static void extractEditSet(PushNoticeEventSetDTO eventSetDTO,
        PushMessageTemplateResource templateResource) {
        // 通知设置id
        eventSetDTO.setNoticeSetId(templateResource.getId());
        // 开启自动推送 1是
        eventSetDTO.setEnableAutomaticPush(GeneralJudgeEnum.CONFIRM.getValue());
        // 自动发送时间
        eventSetDTO.setAutomaticPushTime(templateResource.getAutomaticPushTime());

        // 周期推送频次数据回填
        eventSetDTO.setPushType(Optional.ofNullable(templateResource.getPushType()).orElse(0));
        eventSetDTO.setDisturb(Optional.ofNullable(templateResource.getDisturb()).orElse(0));
        eventSetDTO.setPushCycleType(Optional.ofNullable(templateResource.getPushCycleType()).orElse(0));
        if (PushTypeEnum.PERIOD.getCode().equals(templateResource.getPushType())) {
            eventSetDTO.setPushCycleType(templateResource.getPushCycleType());
            if (PushCycleEnum.CUSTOM.getCode().equals(templateResource.getPushCycleType())) {
                eventSetDTO.setPushCycleValues(Arrays.stream(templateResource.getPushCycleValues().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList()));
            } else {
                eventSetDTO.setPushCycleValues(new ArrayList<>());
            }

        }
    }

    @Override
    public List<PushNoticeChannelDTO> getPushNoticeChannelSetList(PushNoticeChannelSetQuery query) {
        // 查询所有支持通知渠道
        Map<String, PushNoticeChannel> channelMap = pushNoticeChannelService.list().stream()
            .collect(Collectors.toMap(PushNoticeChannel::getId, Function.identity()));

        // 查询已开启默认自动通知渠道
        Set<String> globalChannelIds = pushMessageTemplateGlobalService.list(
            new LambdaQueryWrapper<PushMessageTemplateGlobal>().select(PushMessageTemplateGlobal::getChannelId)
                .eq(PushMessageTemplateGlobal::getEventId, query.getEventId())
                .eq(PushMessageTemplateGlobal::getLanguage, LanguageEnum.ENGLISH.getLang())
                .eq(PushMessageTemplateGlobal::getEnableDefaultAutomaticPush, GeneralJudgeEnum.CONFIRM.getValue()))
            .stream().map(PushMessageTemplateGlobal::getChannelId).collect(Collectors.toSet());

        List<PushNoticeChannelDTO> list = new ArrayList<>();
        globalChannelIds.forEach(globalChannelId -> {
            PushNoticeChannelDTO dto = new PushNoticeChannelDTO();
            BeanUtils.copyProperties(channelMap.get(globalChannelId), dto);
            dto.setName(PushChannelEnum.getDesc(dto.getCode()));
            dto.setEnableDefaultAutomaticPush(GeneralJudgeEnum.CONFIRM.getValue());
            list.add(dto);
        });

        return list.stream().sorted(Comparator.comparing(PushNoticeChannelDTO::getCode)).collect(Collectors.toList());
    }

}
