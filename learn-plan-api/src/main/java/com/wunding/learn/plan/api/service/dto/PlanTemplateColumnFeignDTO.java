package com.wunding.learn.plan.api.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@Accessors(chain = true)
@Schema(name = "PlanTemplateColumnFeignDTO", description = "动态表头模板列对象")
public class PlanTemplateColumnFeignDTO {

    @Schema(description = "列code")
    private String columnCode;

    @Schema(description = "列名称")
    private String columnName;

    @Schema(description = "列别名")
    private String columnAlias;

    @Schema(description = "列类型 0-文本 1-日期 2-数字")
    private Integer columnType;

    @Schema(description = "限制输入长度")
    private Integer maxLength;

    @Schema(description = "格式正则校验")
    private String checkValue;

    @Schema(description = "格式提示文字")
    private String checkPrompt;

    @Schema(description = "管理列表显示 0否 1是")
    private Integer isManageView;

    @Schema(description = "关联列表显示 0-否 1-是")
    private Integer isCorrelationView;

    @Schema(description = "是否必填 0-否 1-是")
    private Integer isRequired;

    @Schema(description = "日期格式 0-日期 1-年月 ")
    private Integer dateFormat;

    @Schema(description = "是否为查询条件 0-否 1-是")
    private Integer isSearch;

    @Schema(description = "下拉框输入 以半角分号做为分隔")
    private String selectValue;

}
