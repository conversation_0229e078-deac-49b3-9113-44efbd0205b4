package com.wunding.learn.exam.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.exam.service.admin.dto.BindTopicDTO;
import com.wunding.learn.exam.service.admin.dto.SaveExamCompetitionTopicDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientHomePageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientInvitePageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientListPageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientMatchPopPageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientPkPageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientRankPageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientResultPageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientStartCountDownPageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientTimingPageDTO;
import com.wunding.learn.exam.service.client.dto.topic.ClientWrongPageDTO;
import com.wunding.learn.exam.service.model.ExamTopic;

/**
 * <p> 竞赛关联主题表 服务类
 *
 * <AUTHOR> href="mailto:"></a>
 * @since 2023-10-16
 */
public interface IExamTopicService extends IService<ExamTopic> {


    /**
     * 绑定主题
     *
     * @param saveExamCompetitionTopicDTO 保存考试竞赛主题dto
     */
    void bindTopic(SaveExamCompetitionTopicDTO saveExamCompetitionTopicDTO);

    /**
     * 获取绑定主题id
     *
     * @param competitionId 竞争id
     * @return {@link BindTopicDTO}
     */
    BindTopicDTO getBindTopicIdReturnEmpty(String competitionId);

    /**
     * 获取首页页面属性
     *
     * @param competitionId 竞争id
     * @return {@link ClientHomePageDTO}
     */
    ClientHomePageDTO getHomePage(String competitionId);

    /**
     * 获取pk页面
     *
     * @param competitionId 竞争id
     * @return {@link ClientPkPageDTO}
     */
    ClientPkPageDTO getPkPage(String competitionId);

    /**
     * 获取邀请页面
     *
     * @param competitionId 竞争id
     * @return {@link ClientInvitePageDTO}
     */
    ClientInvitePageDTO getInvitePage(String competitionId);

    /**
     * 获取结果页面
     *
     * @param competitionId 竞争id
     * @return {@link ClientResultPageDTO}
     */
    ClientResultPageDTO getResultPage(String competitionId);

    /**
     * 获取排名页面
     *
     * @param competitionId 竞争id
     * @return {@link ClientRankPageDTO}
     */
    ClientRankPageDTO getRankPage(String competitionId);

    /**
     * 获取匹配弹出页面
     *
     * @param competitionId 竞争id
     * @return {@link ClientMatchPopPageDTO}
     */
    ClientMatchPopPageDTO getMatchPopPage(String competitionId);

    /**
     * 获取计时页面
     *
     * @param competitionId 竞争id
     * @return {@link ClientTimingPageDTO}
     */
    ClientTimingPageDTO getTimingPage(String competitionId);

    /**
     * 获取开始倒计时页面
     *
     * @param competitionId 竞争id
     * @return {@link ClientStartCountDownPageDTO}
     */
    ClientStartCountDownPageDTO getStartCountDownPage(String competitionId);

    /**
     * 获取列表页
     *
     * @return {@link ClientListPageDTO}
     */
    ClientListPageDTO getListPage();

    /**
     * 错题集页
     *
     * @return {@link ClientWrongPageDTO}
     */
    ClientWrongPageDTO getWrongPage();

}
