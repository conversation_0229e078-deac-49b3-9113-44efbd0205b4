package com.wunding.learn.exam.service.helper;


import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

/**
 * 考试竞赛redisHelper
 *
 * <AUTHOR>
 * @date 2023年10月26
 */
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ExamCompetitionRedisHelper {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 在线心跳时长 60s
     */
    public static final Long HEARTBEAT_MILLISECONDS = 120L * 1000;

    private static final String FORMAT = "examCompetition:online:";

    /**
     * 上线
     */
    public void goOnLine(String competitionId, String userId) {
        String redisKey = getRedisKey(competitionId);
        long currentTime = System.currentTimeMillis();
        DefaultRedisScript<Void> script = new DefaultRedisScript<>();
        script.setScriptText("""
            local redisKey = KEYS[1]
            local userId = ARGV[1]
            local currentTime = tonumber(ARGV[2])
            local heartbeatMilliseconds = tonumber(ARGV[3])
            
            redis.call('ZADD', redisKey, currentTime, userId)
            redis.call('EXPIRE', redisKey, heartbeatMilliseconds / 1000)
            redis.call('ZREMRANGEBYSCORE', redisKey, 0, currentTime - heartbeatMilliseconds)
            """);

        List<String> keys = Collections.singletonList(redisKey);
        Object[] args = {userId, currentTime, HEARTBEAT_MILLISECONDS};
        redisTemplate.execute(script, keys, args);
    }

    /**
     * 查询在线人数
     */
    public Long getOnlineUserCount(String competitionId) {
        String redisKey = getRedisKey(competitionId);
        long currentTime = System.currentTimeMillis();
        long minTimestamp = currentTime - HEARTBEAT_MILLISECONDS;
        return redisTemplate.opsForZSet().count(redisKey, minTimestamp, currentTime);
    }

    public String getRedisKey(String competitionId) {
        return FORMAT + competitionId;
    }
}
