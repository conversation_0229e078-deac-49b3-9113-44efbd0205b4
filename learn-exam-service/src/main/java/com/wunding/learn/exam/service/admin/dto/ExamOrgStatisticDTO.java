package com.wunding.learn.exam.service.admin.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <p>
 * 考试部门成绩统计
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/5/18 17:37
 */
@Data
@Schema(name = "ExamOrgStatisticDTO", description = "按部门考试成绩统计")
public class ExamOrgStatisticDTO {


    /**
     * 考试id
     */
    @Schema(description = "记录ID")
    private String id;


    /**
     * 考试ID
     */
    @Schema(description = "考试ID")
    private String examId;


    /**
     * 组织id
     */
    @Schema(description = "组织id")
    private String orgId;

    /**
     * 组织名称
     */
    @Schema(description = "组织名称")
    private String orgName;


    /**
     * 未参加人数
     */
    @Schema(description = "未参加人数")
    private Integer notJoinNum;


    /**
     * 未及格人数
     */
    @Schema(description = "未及格人数")
    private Integer notPassNum;


    /**
     * 及格人数
     */
    @Schema(description = "及格人数")
    private Integer passNum;


    /**
     * 及格率
     */
    @Schema(description = "及格率",hidden = true)
    private BigDecimal passRate;


    /**
     * 首次及格率
     */
    @Schema(description = "首次及格率",hidden = true)
    private BigDecimal firstPassRate;


    /**
     * 及格率
     */
    @Schema(description = "及格率显示字符串")
    private String passRateStr;


    /**
     * 首次及格率
     */
    @Schema(description = "首次及格率显示字符串")
    private String firstPassRateStr;


    @Schema(description = "部门全称 orgPath")
    private String orgPath;
}
