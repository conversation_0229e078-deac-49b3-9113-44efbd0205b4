package com.wunding.learn.exam.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache;
import com.wunding.learn.exam.service.model.ExamResourceTaskExecute;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p> 课程学习统计按部门 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">z<PERSON><PERSON><PERSON><PERSON></a>
    * @since 2023-09-28
 */
@Mapper
@CacheNamespace(implementation = MyBatisPlusRedisCache.class, eviction = MyBatisPlusRedisCache.class)
public interface ExamResourceTaskExecuteMapper extends BaseMapper<ExamResourceTaskExecute> {



    /**
     * 获取部门最后一次统计时间
     * @param resourceId
     * @return
     */
    String getDeptLastCollectTime(String resourceId);




    /**
    * 获取资源下发用户id
    * @param viewLimitId
    * @return
    */
    Set<String> getLimitUserIdsBak(Long viewLimitId,String viewLimitUserPartition);


}
