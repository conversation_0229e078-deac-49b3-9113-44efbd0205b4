package com.wunding.learn.exam.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListDTO;
import com.wunding.learn.exam.service.client.dto.SchemaQuestionCountDTO;
import com.wunding.learn.exam.service.enums.ExamQuestionTypeEnum;
import com.wunding.learn.exam.service.mapper.SchemaDetailMapper;
import com.wunding.learn.exam.service.model.SchemaDetail;
import com.wunding.learn.exam.service.service.ISchemaDetailService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 组卷策略详情表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-03-19
 */
@Slf4j
@Service("schemaDetailService")
public class SchemaDetailServiceImpl extends ServiceImpl<SchemaDetailMapper, SchemaDetail>
    implements ISchemaDetailService {

    @Override
    public String getSchemaType(String schemaId) {
        return baseMapper.getSchemaType(schemaId);
    }

    @Override public List<SchemaDetail> getDetailsBySchemaId(String schemaId) {
        return baseMapper.selectList(
            new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, schemaId).eq(SchemaDetail::getIsDel, 0)
                .orderByAsc(SchemaDetail::getSortNo).orderByAsc(SchemaDetail::getQuestionType));
    }

    @Override
    public Integer getQuestionCountBySchemaId(String schemaId) {
        List<SchemaDetail> schemaDetails = baseMapper.selectList(
            new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, schemaId));
        // 计算题目总数
        return schemaDetails.stream().mapToInt(SchemaDetail::getQuestionNum).sum();
    }

    @Override
    public List<ExamCorrectExamListDTO> getCountSchemaId(Collection<String> schemaIds){
        List<ExamCorrectExamListDTO> result = new ArrayList<>();
        List<SchemaDetail> schemaDetails = baseMapper.selectList(
            new LambdaQueryWrapper<SchemaDetail>().in(SchemaDetail::getSchemaId, schemaIds));
        if(CollectionUtils.isEmpty(schemaDetails)){
            return result;
        }
        Map<String,List<SchemaDetail>> groupMap = schemaDetails.stream().collect(Collectors.groupingBy(SchemaDetail::getSchemaId));

        //按schemaId分组,重新组装返回
        for(Map.Entry<String,List<SchemaDetail>> entry : groupMap.entrySet()){
            String schemaId = entry.getKey();
            List<SchemaDetail> list = entry.getValue();
            ExamCorrectExamListDTO dto = new ExamCorrectExamListDTO();
            Integer questionCount = 0;
            Integer subjectiveQuestionCount = 0;
            if(!CollectionUtils.isEmpty(list)){
                for(SchemaDetail detail : list){
                    if( Integer.parseInt(detail.getQuestionType()) == ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() ||
                        Integer.parseInt(detail.getQuestionType()) == ExamQuestionTypeEnum.QUESTION_TYPE_QA.getType()){
                        subjectiveQuestionCount += detail.getQuestionNum();
                    }
                    questionCount += detail.getQuestionNum();
                }
            }

            dto.setSchemaId(schemaId);
            dto.setQuestionCount(questionCount);
            dto.setSubjectiveQuestionCount(subjectiveQuestionCount);
            result.add(dto);
        }
        return result;
    }



    @Override
    public List<SchemaQuestionCountDTO> getQuestionCountBySchemaIdList(Collection<String> schemaIdSet) {
        if (CollectionUtils.isEmpty(schemaIdSet)) {
            return Lists.newArrayList();
        }
        return baseMapper.getQuestionCountBySchemaIdList(schemaIdSet);
    }

    @Override
    public Integer getSubjectiveQuestionCountBySchemaId(String schemaId) {
        List<SchemaDetail> schemaDetails = baseMapper.selectList(
            new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, schemaId));

        return schemaDetails.stream().filter(schemaDetail ->
            Integer.parseInt(schemaDetail.getQuestionType()) == ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() ||
                Integer.parseInt(schemaDetail.getQuestionType()) == ExamQuestionTypeEnum.QUESTION_TYPE_QA.getType())
            .collect(Collectors.toList()).stream().mapToInt(SchemaDetail::getQuestionNum).sum();
    }

    @Override
    public BigDecimal getScoreBySchemaIdAndQuestionType(String schemaId, Integer questionType) {
        SchemaDetail schemaDetail = baseMapper.selectOne(
            new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, schemaId)
                .eq(SchemaDetail::getQuestionType, questionType));
        if (null == schemaDetail) {
            return new BigDecimal("0");
        }

        return schemaDetail.getQuestionScore();
    }

    @Override
    public BigDecimal getScoreBySchemaId(String schemaId) {
        LambdaQueryWrapper<SchemaDetail> query = new LambdaQueryWrapper<>();
        query.eq(SchemaDetail::getSchemaId, schemaId);
        List<SchemaDetail> schemaDetails = baseMapper.selectList(query);
        BigDecimal reduce = schemaDetails.stream().map(SchemaDetail::getQuestionScore)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        return reduce;
    }
}
