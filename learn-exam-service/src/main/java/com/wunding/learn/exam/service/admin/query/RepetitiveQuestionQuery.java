package com.wunding.learn.exam.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/29 14:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "RepetitiveQuestionQuery", description = "重复题目查询对象")
public class RepetitiveQuestionQuery extends BaseEntity implements Serializable {

    @Parameter(description = "考试ID")
    @NotBlank(message = "考试id不可为空")
    String testPaperId;

    @Parameter(description = "题目类型：1：单选 2：多选 3：填空 4：判断 5：问答 11：组合题 ")
    @NotNull(message = "题目类型不可为空")
    private Integer questionType;
}
