package com.wunding.learn.exam.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.exam.service.mapper.CompetitionScoreRuleMapper;
import com.wunding.learn.exam.service.model.CompetitionScoreRule;
import com.wunding.learn.exam.service.service.ICompetitionScoreRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 考试竞赛得分规则表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
    * @since 2023-09-18
 */
@Slf4j
@Service("competitionScoreRuleService")
public class CompetitionScoreRuleServiceImpl extends ServiceImpl<CompetitionScoreRuleMapper, CompetitionScoreRule> implements ICompetitionScoreRuleService {

    /**
     * 根据竞赛Id查询得分规则
     */
    @Override
    public CompetitionScoreRule findFirstByCompetitionId(String examCompetitionId) {
        return getOne(new LambdaQueryWrapper<CompetitionScoreRule>()
            .eq(CompetitionScoreRule::getBusinessId, examCompetitionId));
    }
}
