package com.wunding.learn.exam.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/29 14:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "ExamAnswerRecordInfoDTO", description = "考试成绩管理信息对象")
public class ExamAnswerRecordInfoDTO implements Serializable {

    /**
     * 考试名称
     */
    @Schema(description = "考试名称")
    private String examName;

    /**
     * 试题数量
     */
    @Schema(description = "试题数量")
    private Integer questionCount;

    /**
     * 总分
     */
    @Schema(description = "总分")
    private BigDecimal total;

    /**
     * 及格分
     */
    @Schema(description = "及格分")
    private BigDecimal passScore;

    /**
     * 允许考试次数
     */
    @Schema(description = "允许考试次数")
    private Integer reExamCount;

    /**
     * 参与考试人数
     */
    @Schema(description = "参与考试人数(包含未交卷)")
    private BigDecimal joinExamCount;

    /**
     * 及格人数
     */
    @Schema(description = "及格人数")
    private BigDecimal passExamCount;

    /**
     * 及格率
     */
    @Schema(description = "及格率")
    private BigDecimal passRate;

    /**
     * 交卷人数
     */
    @Schema(description = "已交卷人数")
    private BigDecimal completeCount;

    /**
     * 考试开始时间
     */
    @Schema(description = "考试开始时间")
    private Date startTime;


    /**
     * 考试结束时间
     */
    @Schema(description = "考试结束时间")
    private Date endTime;

    /**
     * 符合系统交卷的人数
     */
    @Schema(description = "符合系统交卷条件的人数")
    private int systemPostExamCount;


    /**
     * 是否限制切屏次数
     */
    @Schema(description = "是否限制切屏次数，0否 1是")
    private Integer isLimitScreenCuts;


    /**
     * 是否限制截图次数
     */
    @Schema(description = "是否限制截图次数，0否 1是")
    private Integer isLimitScreenShots;


    /**
     * 是否限制录屏次数
     */
    @Schema(description = "是否限制录屏次数，0否 1是")
    private Integer isLimitScreenRecords;

    /**
     * 是否能进行改卷
     */
    @Schema(description = "是否能进行改卷，0否 1是")
    private Integer checkExam;
}
