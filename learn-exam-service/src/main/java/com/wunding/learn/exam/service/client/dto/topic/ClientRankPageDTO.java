package com.wunding.learn.exam.service.client.dto.topic;

import com.wunding.learn.user.api.dto.topic.PageAttributeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客户端排名页面dto
 *
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
@Accessors(chain = true)
@Schema(name = "ClientRankPageDTO", description = "客户端排名页面dto")
public class ClientRankPageDTO {

    @Schema(description = "当前页id")
    private String id;

    @Schema(description = "排名页头部背景图")
    private PageAttributeDTO rankPageHeadImg;

    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "主题颜色")
    private String topicColor;
}
