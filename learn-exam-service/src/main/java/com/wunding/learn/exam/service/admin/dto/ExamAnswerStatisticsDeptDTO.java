package com.wunding.learn.exam.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 考试统计对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON><PERSON><PERSON></a>
 * @date 2023/11/2 14:14
 */
@Data
@Schema(name = "ExamAnswerStatisticsDeptDTO", description = "按部门考试成绩统计")
public class ExamAnswerStatisticsDeptDTO {


    /**
     * 组织id
     */
    @Schema(description = "组织id")
    private String orgId;


    /**
     * 组织路径
     */
    @Schema(description = "组织路径")
    private String levelPath;


    /**
     * 下发人数
     */
    @Schema(description = "下发人数")
    private Integer totalCount;

    /**
     * 参与人数
     */
    @Schema(description = "参与人数")
    private Integer partCount;

    /**
     * 交卷人数
     */
    @Schema(description = "交卷人数")
    private Integer postCount;

    /**
     * 已阅卷人数
     */
    @Schema(description = "已阅卷人数")
    private Integer checkCount;


    /**
     * 通过人数
     */
    @Schema(description = "通过人数")
    private Integer passCount;

    /**
     * 第一次通过人数
     */
    @Schema(description = "第一次通过人数")
    private Integer firstPassCount;


}
