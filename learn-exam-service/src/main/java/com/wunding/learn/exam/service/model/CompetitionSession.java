package com.wunding.learn.exam.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 竞赛场次表
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @since 2023-09-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ex_competition_session")
@Schema(name = "CompetitionSession对象", description = "竞赛场次表")
public class CompetitionSession implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;


    /**
     * 场次编码
     */
    @Schema(description = "场次编码")
    @TableField("code")
    private String code;

    /**
     * 场次邀请码
     */
    @Schema(description = "场次邀请码")
    @TableField("invitation_code")
    private String invitationCode;


    /**
     * 竞赛ID
     */
    @Schema(description = "竞赛ID")
    @TableField("competition_id")
    private String competitionId;


    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @TableField("start_time")
    private Date startTime;


    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @TableField("end_time")
    private Date endTime;


    /**
     * 组织id
     */
    @Schema(description = "组织id")
    @TableField("org_id")
    private String orgId;


    /**
     * 人员模式 single=单人 double=双人  multi=多人 group=分组
     */
    @Schema(description = "人员模式 single=单人 double=双人  multi=多人 group=分组")
    @TableField("people_model")
    private String peopleModel;


    @Schema(description = "答题模式 pk=PK答题模式  keep=一站到底 time=计时赛")
    @TableField("answer_model")
    private String answerModel;

    /**
     * 总计答题时间 单位秒
     */
    @Schema(description = "总计答题时间 单位秒")
    @TableField("total_answer_time")
    private Integer totalAnswerTime;


    /**
     * 每题答题时间 单位秒
     */
    @Schema(description = "每题答题时间 单位秒")
    @TableField("per_answer_time")
    private Integer perAnswerTime;

    @Schema(description = "该场次的总题目数")
    @TableField("question_num")
    private Integer questionNum;

    @Schema(description = "是否查看答案 0.不可查看答案 1.可以查看答案")
    @TableField("is_view_answer")
    private Integer isViewAnswer;


    /**
     * 允许错误次数
     */
    @Schema(description = "允许错误次数")
    @TableField("allow_wrong_num")
    private Integer allowWrongNum;


    /**
     * 可跳过题目次数
     */
    @Schema(description = "可跳过题目次数")
    @TableField("allow_skip_num")
    private Integer allowSkipNum;
    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;

    @Schema(description = "场次状态 0=默认值 1=准备中 2=进行中 3=已经结束")
    @TableField("status")
    private Integer status;

    @Schema(description = "PK模式，当前进行到第几题")
    @TableField("question_index")
    private Integer questionIndex;


    @Schema(description = "答题的时间")
    @TableField("question_time")
    private Date questionTime;

    /**
     * 使用机器人 0=不使用 1=使用
     */
    @Schema(description = "使用机器人 0=不使用 1=使用")
    @TableField("use_robot")
    private Integer useRobot;


    /**
     * 机器人段位下限
     */
    @Schema(description = "机器人段位下限")
    @TableField("robot_lower_limit")
    private Integer robotLowerLimit;


    /**
     * 机器人段位上限
     */
    @Schema(description = "机器人段位上限")
    @TableField("robot_upper_limit")
    private Integer robotUpperLimit;

    @Schema(description = "排名规则 0=默认值 1=按积分 2=按答题正确数")
    @TableField("rank_rule")
    private Integer rankRule;

    /**
     * 允许随机人员进入
     */
    @Schema(description = "允许随机人员进入")
    @TableField("random_join")
    private Integer randomJoin;

    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
