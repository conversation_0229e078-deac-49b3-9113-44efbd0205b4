package com.wunding.learn.exam.service.enums;

import java.util.LinkedHashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@ToString
@AllArgsConstructor
public enum ExamTypeEnum {

    /**
     * Exam 类型-考试
     */
    EXAM(1, "考试"),
    /**
     * Exam 类型-练习
     */
    EXERCISE(2, "练习");


    private Integer code;
    private String text;

    /**
     * 枚举所有的值返回一个map，方便其他业务类使用
     *
     * @return
     */
    public static Map<Integer, String> getExamTypes() {
        Map<Integer, String> map = new LinkedHashMap<>(8);
        ExamTypeEnum[] types = ExamTypeEnum.values();
        for (ExamTypeEnum t : types) {
            map.put(t.getCode(), t.getText());
        }
        return map;
    }

}
