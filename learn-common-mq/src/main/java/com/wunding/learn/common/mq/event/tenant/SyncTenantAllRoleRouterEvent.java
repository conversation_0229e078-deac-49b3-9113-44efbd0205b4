package com.wunding.learn.common.mq.event.tenant;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.wunding.learn.common.mq.event.AbstractEvent;
import lombok.Getter;
import lombok.ToString;

/**
 * 同步租户广告数据
 *
 * @author: chenjinneng
 * @create: 2024-12-04
 **/
@Getter
@ToString(callSuper = true)
public class SyncTenantAllRoleRouterEvent extends AbstractEvent {

    /**
     * 定义资源修改交换机
     */
    public static final String INTERVIEW_EXCHANGE = "syncTenantAllRoleRouterExchange";

    /**
     * 定义资源修改路由键
     */
    public static final String INTERVIEW_KEY = "syncTenantAllRoleRouterRoutingKey";

    /**
     * 资源修改交换机类型
     */
    public static final String RESOURCE_CHANGE_EXCHANGE_TYPE = EXCHANGE_TOPIC;


    @JsonCreator
    public SyncTenantAllRoleRouterEvent() {
        super(INTERVIEW_EXCHANGE, RESOURCE_CHANGE_EXCHANGE_TYPE, INTERVIEW_KEY);
    }
}
