package com.wunding.learn.common.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.wunding.learn.common.dto.SaveViewLimitDTO;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 资源可见范围事件
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/2/3 14:52
 */

@Getter
@ToString(callSuper = true)
@Slf4j
public class ResourceViewLimitChangeEvent extends AbstractEvent{


    /**
     * 定义资源完成交换机
     */
    public static final String RESOURCE_VIEW_LIMIT_EXCHANGE = "resource_view_limit_exchange";
    /**
     * 资源完成交换机类型
     */
    public static final String SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE = EXCHANGE_TOPIC;
    private static final long serialVersionUID = 7585273022134244409L;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 可见范围类型
     */
    private Integer viewType;

    /**
     * 需要变动的方案id
     */
    private Map<String,Long> viewLimitChangeMap;

    /**
     * 需要变动的数据
     */
    private Map<String,List<SaveViewLimitDTO>> saveViewLimitMap;

    @JsonCreator
    public ResourceViewLimitChangeEvent(String resourceType,Integer viewType,Map<String,Long> viewLimitChangeMap) {
        super(RESOURCE_VIEW_LIMIT_EXCHANGE, SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE, getRoutingKeyByResourceType(resourceType));
        this.viewLimitChangeMap = viewLimitChangeMap;
        this.resourceType = resourceType;
        this.viewType = viewType;
    }


    private static String getRoutingKeyByResourceType(String resourceType) {
        String routingKey = "";
        switch (resourceType) {
            case "course":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.COURSE_VIEW_LIMIT_CHANGE;
                break;
            case "survey":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.SURVEY_VIEW_LIMIT_CHANGE;
                break;
            case "exam":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.EXAM_VIEW_LIMIT_CHANGE;
                break;
            case "promotedGame":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.PROMOTED_GAME_VIEW_LIMIT_CHANGE;
                break;
            case "project":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.PROJECT_VIEW_LIMIT_CHANGE;
                break;
            case "appraise":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.APPRAISE_VIEW_LIMIT_CHANGE;
                break;
            case "recruit":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.RECRUIT_VIEW_LIMIT_CHANGE;
                break;
            case "lecturerLevel":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.LECTURER_LEVEL_VIEW_LIMIT_CHANGE;
                break;
            case "trainWithOut":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.TRAIN_WITHOUT_VIEW_LIMIT_CHANGE;
                break;
            case "live":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.LIVE_VIEW_LIMIT_CHANGE;
                break;
            case "vote":
                routingKey = ResourceViewLimitChangeEventRoutingKeyConstants.VOTE_VIEW_LIMIT_CHANGE;
                break;
            default:
                log.error("未找到对应资源的下发范围! resourceType:{}", resourceType);
                break;
        }
        return routingKey;
    }

    public void setSaveViewLimitMap(
        Map<String, List<SaveViewLimitDTO>> saveViewLimitMap) {
        this.saveViewLimitMap = saveViewLimitMap;
    }

    /**
     * 系统资源相关事件类型常量定义
     */
    public static class ResourceViewLimitChangeEventRoutingKeyConstants {

        /**
         * 课程下发范围变化
         */
        public static final String COURSE_VIEW_LIMIT_CHANGE = "CourseViewLimitChange";

        /**
         * 考试下发范围变化
         */
        public static final String EXAM_VIEW_LIMIT_CHANGE = "ExamViewLimitChange";

        /**
         * 调研下发范围变化
         */
        public static final String SURVEY_VIEW_LIMIT_CHANGE = "SurveyViewLimitChange";

        /**
         * 闯关游戏下发范围变化
         */
        public static final String PROMOTED_GAME_VIEW_LIMIT_CHANGE = "PromotedGameViewLimitChange";

        /**
         * 学习项目下发范围
         */
        public static final String PROJECT_VIEW_LIMIT_CHANGE = "ProjectViewLimitChange";

        /**
         * 评价下发范围
         */
        public static final String APPRAISE_VIEW_LIMIT_CHANGE = "AppraiseViewLimitChange";

        /**
         * 招募下发范围
         */
        public static final String RECRUIT_VIEW_LIMIT_CHANGE = "RecruitViewLimitChange";

        /**
         * 直播下发范围变化
         */
        public static final String LIVE_VIEW_LIMIT_CHANGE = "LiveViewLimitChange";

        /**
         * 投票下发范围变化
         */
        public static final String VOTE_VIEW_LIMIT_CHANGE = "VoteViewLimitChange";

        /**
         * 讲师下发范围
         */
        public static final String LECTURER_LEVEL_VIEW_LIMIT_CHANGE = "LecturerLevelViewLimitChange";

        /**
         * 外部培训下发范围
         */
        public static final String TRAIN_WITHOUT_VIEW_LIMIT_CHANGE = "TrainWithoutViewLimitChange";

        private ResourceViewLimitChangeEventRoutingKeyConstants() {
            throw new IllegalStateException("Utility class");
        }

    }
    

}
