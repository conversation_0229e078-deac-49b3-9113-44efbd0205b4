<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.project.service.mapper.ProjectTaskRecordMapper">

    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.project.service.mapper.ProjectTaskRecordMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.project.service.model.TaskRecord">
        <!--@Table project_task_record-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="task_template_id" jdbcType="VARCHAR"
          property="taskTemplateId"/>
        <result column="task_column_id" jdbcType="VARCHAR"
          property="taskColumnId"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="mentor_id" jdbcType="VARCHAR"
          property="mentorId"/>
        <result column="is_finish" jdbcType="TINYINT"
          property="isFinish"/>
        <result column="column_is_finish" jdbcType="TINYINT"
          property="columnIsFinish"/>
        <result column="column_finish_time" jdbcType="TINYINT"
          property="columnFinishTime"/>
        <result column="cycle_id" jdbcType="VARCHAR"
          property="cycleId"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , task_template_id, task_column_id, user_id, mentor_id, is_finish, column_is_finish, column_finish_time, cycle_id, is_del, create_by, create_time, update_by, update_time
    </sql>

    <resultMap id="FormTaskRecordResultMap" type="com.wunding.learn.project.service.client.dto.form.FormTaskRecordDTO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="task_column_id" property="columnId" jdbcType="VARCHAR"/>
        <result column="record_id" property="recordId" jdbcType="VARCHAR"/>
        <result column="flag" property="flag" jdbcType="VARCHAR"/>
        <result column="name" property="title" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="completeTime" property="completeTime" jdbcType="INTEGER"/>
        <result column="user_Id" property="userId" jdbcType="VARCHAR"/>
        <result column="identity" property="identity" jdbcType="VARCHAR"/>
        <result column="column_limit" property="visibleRange" jdbcType="INTEGER"/>
        <result column="hint_text" property="hintText" jdbcType="VARCHAR"/>
        <result column="text_content" property="content" jdbcType="VARCHAR"/>
        <result column="rejectcount" property="rejectCount" jdbcType="INTEGER"/>
        <result column="surveyId" property="surveyId" jdbcType="VARCHAR"/>
        <result column="evaluate_record_id" property="surveyUrl" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="REAL"/>
        <result column="surveyObject" property="surveyObject" jdbcType="VARCHAR"/>
        <result column="questioncount" property="questionCount" jdbcType="INTEGER"/>
        <result column="completeCount" property="completeCount" jdbcType="INTEGER"/>
        <result column="blockIdlist" property="blockIdList" jdbcType="VARCHAR"/>
        <result column="blockme" property="blockMe" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getList" parameterType="map" resultMap="FormTaskRecordResultMap" useCache="false">
        select a.id,
               a.record_id,
               a.task_template_id,
               a.task_column_id,
               a.status,
               a.text_content,
               a.evaluate_record_id,
               c.name,
               c.hint_text,
               d.column_limit,
               a.user_id,
               case
                   when c.writer_type = 1 then '学员'
                   else (select c.category_name
                         from categorys c
                         where c.category_type = 'MentorCate'
                           and c.id = c.mentor_type) end as identity,
       case
           when a.status = 0 then ''
           else date_format(a.update_time, '%Y-%m-%d %H:%i:%s')
        end
        as completeTime,
       case
           when c.content_type = 2 then 'survey'
           when c.content_type = 1 then 'text_file'
        end
        as flag,
       case
           when c.content_type = 1 then c.id
        end
        as surveyId,
       (select count(ftrr.id)
        from project_task_reject_detail ftrr
        where ftrr.detail_id = a.id)
                                                 as rejectcount,
       (select group_concat(fctb.category_id, ',')
        from project_form_column_record_block fctb
        where fctb.detail_id = a.id
       )
                                                 as blockIdlist,
       (select case when count(fctb.id) > 0 then 1 else 0 end
        from project_form_column_record_block fctb
        where fctb.detail_id = a.id
          and fctb.user_id = a.user_id
          and fctb.category_id = #{uid}
        )                                         as blockme
        from project_task_record_detail a
        inner join project_task_record b on a.record_id = b.id
        inner join project_form_template_column c on a.task_column_id = c.id
        inner join project_form_template d on c.form_template_id = d.id
        where b.cycle_id =         #{cycleId}
        and b.task_template_id = #{taskContentId}
        and b.user_id =          #{userId}
        and a.is_del = 0
        <if test="userId.toString() != uid.toString()">
            and a.`status` not in (2)
        </if>
        order by c.sort_no asc
    </select>


    <select id = "getTaskRecord" resultType="java.lang.String">
        select id
        from project_task_record
        where cycle_id = #{cycleId}
            and user_id = #{userId}
            and task_template_id = #{formTemplateId}
    </select>

</mapper>
