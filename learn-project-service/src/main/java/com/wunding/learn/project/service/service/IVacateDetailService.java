package com.wunding.learn.project.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.project.service.admin.dto.faceproject.FaceProjectVacateDetailListDTO;
import com.wunding.learn.project.service.admin.dto.faceproject.VacateDetailGroupByDTO;
import com.wunding.learn.project.service.model.VacateDetail;
import java.util.Collection;
import java.util.List;

/**
 * <p> 面授项目请假记录明细表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2024-03-22
 */
public interface IVacateDetailService extends IService<VacateDetail> {

    /**
     * 根据请假id获取请假日程明细
     *
     * @param vacateId
     * @return
     */
    List<FaceProjectVacateDetailListDTO> getVacateDetailListByVacateId(String vacateId);

    /**
     * 删除请假明细
     *
     * @param vacateList
     */
    void removeBatchByVacateIdList(List<String> vacateList);

    /**
     * 通过uer id和schedule id获取请假统计数据
     */
    List<VacateDetailGroupByDTO> getDetailGroupByUerIdAndScheduleId(Collection<String> scheduleIds);

}
