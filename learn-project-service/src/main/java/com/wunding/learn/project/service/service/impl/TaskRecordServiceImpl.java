package com.wunding.learn.project.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.project.service.mapper.ProjectTaskRecordMapper;
import com.wunding.learn.project.service.model.TaskRecord;
import com.wunding.learn.project.service.service.ITaskRecordService;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 辅导任务记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
@Slf4j
@Service("projectTaskRecordService")
public class TaskRecordServiceImpl extends ServiceImpl<ProjectTaskRecordMapper, TaskRecord> implements ITaskRecordService {

    @Override
    public List<TaskRecord> getByCoachIdAndUserId(String coachId, String userId) {
        if (StringUtils.isBlank(coachId)) {
            return new ArrayList<>(0);
        }
        return this.list(new LambdaQueryWrapper<TaskRecord>().eq(TaskRecord::getTaskTemplateId, coachId)
            .eq(TaskRecord::getCreateBy, userId));
    }
}
