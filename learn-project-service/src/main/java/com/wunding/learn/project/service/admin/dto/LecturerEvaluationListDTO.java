package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: LecturerEvaluationListDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/8/16 13:51
 */
@Data
@Schema(name = "LecturerEvaluationListDTO", description = "讲师授课明细评估列表对象")
public class LecturerEvaluationListDTO {

    /**
     * 评估id
     */
    @Schema(description = "评估id")
    private String id;

    /**
     * 讲师id
     */
    @Schema(description = "讲师id")
    private String lecturerId;

    /**
     * 讲师姓名
     */
    @Schema(description = "讲师姓名")
    private String lecturerName;

    /**
     * 讲师编号
     */
    @Schema(description = "讲师编号")
    private String lecturerNumber;

    /**
     * 讲师部门名称
     */
    @Schema(description = "讲师部门名称")
    private String lecturerOrgName;


    /**
     * 评估名称
     */
    @Schema(description = "评估名称")
    private String evalName;


    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date startTime;


    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 是否培训班评估
     */
    @Schema(description = "来源")
    private Integer isTrain;


    /**
     * 评估对象
     */
    @Schema(description = "评估对象")
    private String evaluationObject;

    /**
     * 参与的评估人数
     */
    @Schema(description = "参与的评估人数")
    private long evaluationUserCount;

    /**
     * 综合分
     */
    @Schema(description = "综合分")
    private Float avgScore;

    /**
     * 讲师类别的平均分
     */
    @Schema(description = "讲师类别的平均分")
    private Float lecturerAvgScore;

    /**
     * 授课内容分 = 模板中 课程 分的平均分
     */
    @Schema(description = "授课内容分 = 模板中 课程 分的平均分")
    private Float courseAvgScore;


    /**
     * 项目举办分 = 模板中  项目 的平均分
     */
    @Schema(description = "项目举办分 = 模板中  项目 的平均分")
    private Float projectAvgScore;

    /**
     * 评估所在的学习项目的名称
     */
    @Schema(description = "评估所在的学习项目的名称")
    private String projectName;
}
