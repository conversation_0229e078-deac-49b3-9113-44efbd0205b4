package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 可见范围方案表
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("w_view_limit")
@Schema(name = "ViewLimit对象", description = "可见范围方案表")
public class ViewLimit implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 方案名称，现阶段不管理，默认为空
     */
    @Schema(description = "方案名称，现阶段不管理，默认为空")
    @TableField("name")
    private String name;


    /**
     * 具体方案进行hash256后的值，用来判断是否同一方案
     */
    @Schema(description = "具体方案进行hash256后的值，用来判断是否同一方案")
    @TableField("hash")
    private String hash;


    /**
     * 同步状态，默认0-同步中，1-已同步
     */
    @Schema(description = "同步状态，默认0-同步中，1-已同步")
    @TableField("sync_status")
    private Integer syncStatus;


    /**
     * 最近同步时间
     */
    @Schema(description = "最近同步时间")
    @TableField("sync_time")
    private Date syncTime;


    /**
     * 添加人
     */
    @Schema(description = "添加人")
    @TableField("add_by")
    private String addBy;


    /**
     * 添加时间
     */
    @Schema(description = "添加时间")
    @TableField("add_date")
    private Date addDate;


}
