<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.project.service.mapper.ProjectTeamUserMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.project.service.mapper.ProjectTeamUserMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.project.service.model.TeamUser">
        <!--@Table project_team_user-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="team_id" jdbcType="VARCHAR"
          property="teamId"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="is_lock" jdbcType="TINYINT"
          property="isLock"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , team_id, user_id, is_lock
    </sql>

    <select id="getTeamUser" resultType="com.wunding.learn.project.service.model.TeamUser" useCache="false">
        select ptu.id      as id,
               ptu.team_id as teamId,
               ptu.user_id as userId,
               ptu.is_lock as isLock
        from project_team_user as ptu,
             project_team as pt,
             project_app as pa
        where ptu.team_id = pt.id
          and pt.app_id = pa.id
          and pt.is_del = 0
          and pa.project_id = #{projectId}
          and ptu.user_id = #{userId}
    </select>

    <delete id="deleteTeamUser">
        delete from project_team_user
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllTeamUser">
        delete
        from project_team_user
        where team_id = #{teamId}
    </delete>

    <delete id="deleteProjectTeamUser">
        delete
        from project_team_user
        where id in (select id
                     from (select b.id
                           from project_app a,
                                project_team_user b
                           where a.resource_id = b.team_id
                             and a.resource_type = 4
                             and a.project_id = #{projectId}
                             and b.user_id = #{userId}) f)
    </delete>
</mapper>
