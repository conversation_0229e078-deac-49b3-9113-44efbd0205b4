package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/7/12 14:14
 */
@Data
@Schema(name = "TeamSaveDTO", description = "团队保存对象")
public class TeamSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "项目id", required = true)
    @NotBlank(message = "项目id不可为空")
    private String projectId;

    @Schema(description = "团队名称", required = true)
    @NotBlank(message = "团队名称不可为空")
    private String teamName;

}
