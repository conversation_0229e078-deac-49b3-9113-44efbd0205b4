package com.wunding.learn.project.service.biz;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.WorkInfoDTO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.WorkSaveDTO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.WorkbenchWorkPageDTO;
import com.wunding.learn.project.service.client.query.lecturerworkbench.WorkbenchWorkPageQuery;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
public interface ILecturerWorkbenchWorkBiz {

    /**
     * 讲师工作台-保存作业
     *
     * @param saveDTO
     * @return
     */
    String saveResource(WorkSaveDTO saveDTO);

    /**
     * 讲师工作台-更新作业
     *
     * @param id
     * @param saveDTO
     */
    void updateResource(String id, WorkSaveDTO saveDTO);

    /**
     * 讲师工作台作业数据分页查询
     *
     * @param query
     * @return
     */
    PageInfo<WorkbenchWorkPageDTO> listResource(WorkbenchWorkPageQuery query);

    /**
     * 讲师工作台-作业应用详情
     *
     * @param id
     * @return
     */
    WorkInfoDTO getWorkInfo(String id);
}
