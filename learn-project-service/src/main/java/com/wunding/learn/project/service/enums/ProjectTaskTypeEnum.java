package com.wunding.learn.project.service.enums;

import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.i18n.util.EnumI18n;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.project.service.strategy.BaseStrategy;
import java.util.function.Function;
import lombok.Getter;

/**
 * 项目任务类型
 *
 * <AUTHOR> href="mailto:<EMAIL>">ZhangP<PERSON></a>
 * @since 4.9.4
 */
public enum ProjectTaskTypeEnum implements EnumI18n {


    /**
     * 课程
     */
    COURSE("course", "课程", "projectCourseStrategy", 0, true, quickProject -> {
        if (Boolean.TRUE.equals(quickProject)) {
            return PushType.QUICK_PROJECT_TASK_COURSE;
        }
        return PushType.PROJECT_TASK_COURSE;
    }),
    /**
     * 考试
     */
    EXAM("exam", "考试", "projectExamStrategy", 0, true, quickProject -> {
        if (Boolean.TRUE.equals(quickProject)) {
            return PushType.QUICK_PROJECT_TASK_EXAM;
        }
        return PushType.PROJECT_TASK_EXAM;
    }),
    /**
     * 练习
     */
    EXERCISE("exercise", "练习", "projectExerciseStrategy", 0, true, quickProject -> PushType.PROJECT_TASK_EXERCISE),
    /**
     * 调研
     */
    SURVEY("survey", "调研", "projectSurveyStrategy", 0, true, quickProject -> PushType.PROJECT_TASK_SURVEY),
    /**
     * 直播
     */
    LIVE("live", "直播", "projectLiveStrategy", 2, false, quickProject -> PushType.PROJECT_TASK_LIVE),
    /**
     * 培训班
     */
    TRAIN("train", "培训班", "", 2, true, quickProject -> PushType.PROJECT_TASK_TRAIN),
    /**
     * 项目
     */
    PROJECT("project", "项目", "projectProjectStrategy", 2, true, quickProject -> PushType.PROJECT_TASK_PROJECT),
    /**
     * 报名
     */
    APPLY("apply", "报名", "", 3, true, quickProject -> PushType.APPLY),
    /**
     * 签到
     */
    SIGN("sign", "签到", "", 3, true, null),
    /**
     * 评估
     */
    EVALUATION("evaluation", "评估", "", 3, true, null),
    /**
     * 表单
     */
    FORM("form", "表单", "", 3, true, quickProject -> PushType.PROJECT_TASK_FORM),
    /**
     * 实操
     */
    PRACTICAL_OPERATION("practicalOperation", "实操", "practicalOperationStrategy", 1, true, quickProject -> PushType.PROJECT_TASK_PRACTICAL_OPERATION),

    ;

    @Getter
    private String taskType;
    @EnumI18nProperty
    private String taskName;

    public String getTaskName() {
        return i18n(name(), taskName);
    }

    @Getter
    private String beanName;

    /**
     * 添加方式 0所有 1直接添加 2引用添加 3其他
     */
    @Getter
    private int addMethod;

    /**
     * 是否启用
     */
    @Getter
    private boolean enabled;
    @Getter
    private Function<Boolean, PushType> pushType;

    ProjectTaskTypeEnum(String taskType, String taskName, String beanName, int addMethod, boolean enabled,
        Function<Boolean, PushType> pushType) {
        this.taskType = taskType;
        this.taskName = taskName;
        this.beanName = beanName;
        this.addMethod = addMethod;
        this.enabled = enabled;
        this.pushType = pushType;
    }

    public static String getTaskName(String key) {
        for (ProjectTaskTypeEnum c : ProjectTaskTypeEnum.values()) {
            if (c.taskType.equals(key)) {
                return c.getTaskName();
            }
        }
        if (isForm(key)) {
            return "form";
        }
        if (isCourse(key)) {
            return "course";
        }
        return null;
    }

    public static BaseStrategy getStrategy(String key) {
        for (ProjectTaskTypeEnum c : ProjectTaskTypeEnum.values()) {
            if (c.taskType.equals(key)) {
                return SpringUtil.getBean(c.beanName, BaseStrategy.class);
            }
        }
        return null;
    }

    public static PushType getProjectOrQuickPushType(String taskType, boolean isQuickProject) {
        for (ProjectTaskTypeEnum c : ProjectTaskTypeEnum.values()) {
            if (c.taskType.equals(taskType)) {
                return c.getPushType().apply(isQuickProject);
            }
        }
        return null;
    }

    /**
     * 判断是否表单任务
     *
     * @param taskType 任务类型
     * @return 是否表单任务
     */
    public static boolean isForm(String taskType) {
        for (ProjectTaskTypeEnum ty : ProjectTaskTypeEnum.values()) {
            if (ty.taskType.equals(taskType)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 判断是否课程任务
     *
     * @param taskType 任务类型
     * @return 是否课程任务
     */
    public static boolean isCourse(String taskType) {
        return COURSE.taskType.equals(taskType);
    }

    public static String getTaskType(String taskName) {
        for (ProjectTaskTypeEnum c : ProjectTaskTypeEnum.values()) {
            if (c.taskName.equals(taskName)) {
                return c.taskType;
            }
        }
        return null;
    }


    public static ProjectTaskTypeEnum getItem(String taskType) {
        for (ProjectTaskTypeEnum c : ProjectTaskTypeEnum.values()) {
            if (c.taskType.equals(taskType)) {
                return c;
            }
        }
        return null;
    }
}
