package com.wunding.learn.project.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

/**
 * <p>
 * 学习项目部门统计查询对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/9/22 17:27
 */
@Data

public class StatisticTaskQuery extends BasePageQuery {

    /**
     * 学习项目ID
     */
    @Parameter(description = "学习项目ID")
    private String projectId;

    /**
     * 班主任
     */
    @Parameter(description = "班主任", hidden = true)
    private String leader;

    /**
     * 班主任不参与统计
     */
    @Parameter(description = "班主任不参与统计 0-否，班主任参与统计 1是，班主任不参与统计", hidden = true)
    private Integer leaderDontStat;

}
