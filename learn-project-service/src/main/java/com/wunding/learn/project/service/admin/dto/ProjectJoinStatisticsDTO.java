package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/4/26
 */
@Data
@Schema(name = "ProjectJoinStatisticsDTO", description = "培训项目参与统计数据对象")
@Accessors(chain = true)
public class ProjectJoinStatisticsDTO {

    @Schema(description = "部门名称")
    private String orgName;

    @Schema(description = "部门全路径")
    private String orgFullName;

    @Schema(description = "参与(培训)场次")
    private Integer joinTrainNum;

    @Schema(description = "覆盖人数")
    private Integer coverMemberNum;

    @Schema(description = "覆盖率")
    private BigDecimal coverMemberRate;

    @Schema(description = "覆盖率百分比")
    private String coverMemberRatePercent;

    @Schema(description = "培训人次")
    private Integer trainMemberNum;

    @Schema(description = "(平均)满意度")
    private BigDecimal satisfaction;

    @Schema(description = "人均课时")
    private BigDecimal memberAvgHours;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;

    @Schema(description = "部门id", hidden = true)
    private String orgId;
}
