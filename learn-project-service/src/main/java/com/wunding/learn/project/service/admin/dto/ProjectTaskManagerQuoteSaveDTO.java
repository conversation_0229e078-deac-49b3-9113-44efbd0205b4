package com.wunding.learn.project.service.admin.dto;

import com.wunding.learn.common.dto.SaveViewLimitDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @title: ProjectTaskManagerQuoteSaveDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/7/12 11:43
 */
@Data
@Schema(name = "ProjectTaskManagerQuoteSaveDTO", description = "引用创建学习项目任务")
public class ProjectTaskManagerQuoteSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "项目主键", hidden = true)
    @Length(max = 36, message = "id长度不能超过36")
    private String projectId;

    @Schema(description = "前置任务Id")
    @Length(max = 36, message = "id长度不能超过36")
    private String preTaskId;

    @Schema(description = "项目阶段id/主题id")
    @Length(max = 36, message = "项目阶段id/主题id长度不能超过36")
    private String phaseId;

    @Schema(description = "任务类型")
    @NotBlank(message = "任务类型不能为空")
    @Length(max = 50, message = "任务类型长度不能超过50")
    private String taskType;

    @Schema(description = "任务内容id")
    @NotBlank(message = "任务内容id不能为空")
    @Length(max = 50, message = "任务内容长度不能超过50")
    private String taskContent;

    @Schema(description = "任务名称")
    @NotBlank(message = "任务名称不能为空")
    @Length(max = 80, message = "任务名称长度不能超过80")
    private String taskName;

    @Schema(description = "任务开始时间")
    private Date startTime;

    @Schema(description = "任务结束时间")
    private Date endTime;

    @Schema(description = "序号")
    @Min(value = 0, message = "序号不能是负数")
    private Long sort;

    @Schema(description = "任务分")
    @NotNull(message = "任务分不能为空")
    @Min(value = 0, message = "项目阶段排序不能是负数")
    private Long score;

    @Schema(description = "是否发布 0不 1是")
    private Integer isPublish;

    @Schema(description = "推送通知设置")
    private transient PushNoticeSetDTO pushNoticeSetDTO;

    @Schema(description = "来源 0-本身 1-学习项目 2专题")
    private Integer isTrain;

    @Schema(description = "学习项目下发范围")
    private List<SaveViewLimitDTO> viewLimit;

    @Schema(description = "任务时长-考试时长等")
    private String duration;

    @Schema(description = "地点")
    @Length(max = 50, message = "地点长度不能超过50")
    private String address;

    @Schema(description = "验收方式(1:评估验收; 2:考试验收; 0:不涉及)")
    @Min(value = 0, message = "验收方式(1:评估验收; 2:考试验收; 0:不涉及)")
    @Max(value = 2, message = "验收方式(1:评估验收; 2:考试验收; 0:不涉及)")
    private Integer acceptanceType;

    @Schema(description = "是否单独计算学时 0-否，1-是")
    private Integer isIndependent;

    @Schema(description = "下发范围方案id")
    private Long programmeId;

    @Schema(description = "资源类型别名")
    @Length(max = 20, message = "别名长度不能超过20")
    private String taskTypeAlias;

    @Schema(description = "日程id")
    @Length(max = 36, message = "日程id长度不能超过36")
    private String scheduleId;

    @Schema(description = "课程任务是否必修(0 - 选修  1 - 必修)")
    private Integer isRequired;

    @Schema(description = "项目类型 0普通项目 1快速培训项目 2线上课程学习 3面授项目")
    private Integer projectType;

    @Schema(description = "任务补派状态 1 - 仅进行中学员参与  2 - 仅新学员参与  3 - 全部人参与")
    private Integer taskReplenishmentStatus;
}
