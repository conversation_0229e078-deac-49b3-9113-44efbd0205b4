package com.wunding.learn.project.service.admin.query.faceproject;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p> 面授项目请假查询
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2024-03-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class ScheduleStatQuery extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "项目ID", required = true)
    @NotBlank(message = "项目id不可为空")
    private String projectId;

    @Parameter(description = "用户id集合(以,分割)")
    private String userIds;

    @Parameter(description = "用户id集合", hidden = true)
    private List<String> userIdsVo;

    @Parameter(description = "组织id")
    private String orgId;

    @Parameter(description = "组织全路径", hidden = true)
    private String levelPath;

    @Parameter(description = "日程任务id列表(以,分割)", required = true)
    @NotEmpty(message = "日程任务不能为空")
    private String taskIds;

    @Parameter(description = "日程任务id列表", hidden = true)
    private List<String> taskIdList;
}
