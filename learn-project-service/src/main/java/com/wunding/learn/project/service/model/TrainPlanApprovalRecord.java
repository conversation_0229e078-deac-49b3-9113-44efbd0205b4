package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 培训计划审核记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("project_train_plan_approval_record")
@Schema(name = "TrainPlanApprovalRecord对象", description = "培训计划审核记录")
public class TrainPlanApprovalRecord implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 培训计划id
     */
    @Schema(description = "培训计划id")
    @TableField("train_plan_id")
    private String trainPlanId;


    /**
     * 预留字段，暂时不用
     */
    @Schema(description = "预留字段，暂时不用")
    @TableField("train_plan_no")
    private String trainPlanNo;


    /**
     * 计划名称
     */
    @Schema(description = "计划名称")
    @TableField("train_plan_name")
    private String trainPlanName;


    /**
     * 审核人id
     */
    @Schema(description = "审核人id")
    @TableField("app_rover_id")
    private String appRoverId;


    /**
     * 登陆账号
     */
    @Schema(description = "登陆账号")
    @TableField("app_rover_account")
    private String appRoverAccount;


    /**
     * 审核人姓名
     */
    @Schema(description = "审核人姓名")
    @TableField("app_rover_name")
    private String appRoverName;


    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    @TableField("app_rover_time")
    private Date appRoverTime;


    /**
     * 排序
     */
    @Schema(description = "排序")
    @TableField("sort_no")
    private Integer sortNo;


    /**
     * 角色id
     */
    @Schema(description = "角色id")
    @TableField("role_id")
    private String roleId;


    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    @TableField("role_name")
    private String roleName;


    /**
     * 组织id
     */
    @Schema(description = "组织id")
    @TableField("org_id")
    private String orgId;


    /**
     * 组织code
     */
    @Schema(description = "组织code")
    private String orgCode;


    /**
     * 组织名称
     */
    @Schema(description = "组织名称")
    private String orgName;


    /**
     * 0、未审核1、通过2、未通过
     */
    @Schema(description = "0、未审核1、通过2、未通过")
    @TableField("app_rover_status")
    private Integer appRoverStatus;


    /**
     * 审批意见
     */
    @Schema(description = "审批意见")
    @TableField("approval_comments")
    private String approvalComments;


    /**
     * 预留字段，暂未使用
     */
    @Schema(description = "预留字段，暂未使用")
    @TableField("remark")
    private String remark;


    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @TableField("version_number")
    private Long versionNumber;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


}
