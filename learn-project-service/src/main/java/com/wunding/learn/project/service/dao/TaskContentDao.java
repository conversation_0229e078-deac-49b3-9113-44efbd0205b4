package com.wunding.learn.project.service.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.project.service.model.TaskContent;

/**
 * <AUTHOR>
 */
public interface TaskContentDao extends IService<TaskContent> {

    /**
     * 保存项目任务内容
     *
     * @param taskContent
     */
    void saveTaskContent(TaskContent taskContent);

    /**
     * 更新项目任务内容
     *
     * @param taskContent
     */
    void updateTaskContent(TaskContent taskContent);

    /**
     * 删除项目任务内容
     *
     * @param taskContent
     */
    void delTaskContent(TaskContent taskContent);
}
