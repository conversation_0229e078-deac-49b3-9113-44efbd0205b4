<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.project.service.mapper.ClassroomMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.project.service.mapper.ClassroomMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.project.service.model.Classroom">
                <!--@Table project_classroom-->
                <id column="id" jdbcType="VARCHAR" property="id"/>
                <result column="room_name" jdbcType="VARCHAR"
                  property="roomName"/>
                <result column="address" jdbcType="VARCHAR"
                  property="address"/>
                <result column="remark" jdbcType="VARCHAR"
                  property="remark"/>
                <result column="is_del" jdbcType="TINYINT"
                  property="isDel"/>
                <result column="create_by" jdbcType="VARCHAR"
                  property="createBy"/>
                <result column="create_time" jdbcType="TIMESTAMP"
                  property="createTime"/>
                <result column="update_by" jdbcType="VARCHAR"
                  property="updateBy"/>
                <result column="update_time" jdbcType="TIMESTAMP"
                  property="updateTime"/>
                <result column="org_id" jdbcType="VARCHAR"
                  property="orgId"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, room_name, address, remark, is_del, create_by, create_time, update_by, update_time, org_id
        </sql>

        <select id="queryPage" parameterType="com.wunding.learn.project.service.admin.query.ClassroomPageQuery"
          resultType="com.wunding.learn.project.service.model.Classroom">
                select pc.*
                from project_classroom pc
                inner join sys_org g on g.id = pc.org_id
                <where>
                        <if test="params.roomName != null and params.roomName != ''">
                                and pc.room_name like concat(concat('%',#{params.roomName}), '%')
                        </if>
                        <if test="params.managerAreaPaths != null and params.managerAreaPaths.size() > 0">
                                <foreach collection="params.managerAreaPaths" item="item" open="and (" separator="or">
                                        g.level_path like concat(#{item},'%')
                                </foreach>
                                or pc.create_by=#{params.currentUserId}
                                )
                        </if>
                        and pc.is_del = 0
                </where>
                order by pc.create_time desc
        </select>

        <select id="getClassRoomPage" resultType="com.wunding.learn.project.service.admin.dto.ClassroomPageDTO">
            select
            id, room_name, address, remark description, is_del, create_by, create_time, update_by, update_time
            from project_classroom
            <where>
                is_del = 0
                <if test="params.roomName != null and params.roomName != ''">
                    and instr(room_name ,#{params.roomName})
                </if>
            </where>
        </select>
</mapper>
