package com.wunding.learn.project.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.project.service.model.ReplenishTaskLog;

/**
 * <p>
 * 补派任务日志接口
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON><PERSON><PERSON></a>
 * @date 2024/8/28 16:44
 */
public interface IReplenishTaskLogService extends IService<ReplenishTaskLog> {

    /**
     * 添加补派任务日志
     * @param info
     */
    void addLog(ReplenishTaskLog info);

}
