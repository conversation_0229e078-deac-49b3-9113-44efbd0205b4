package com.wunding.learn.project.sevice.job;

import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.project.service.LearnProjectServiceApplication;
import com.wunding.learn.project.service.service.IProjectService;
import java.util.Map;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.CollectionUtils;
import org.wildfly.common.Assert;

/**
 * 学习项目定时任务单元测试
 *
 * <AUTHOR>
 * @date 2024/08/23 13:19
 */
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(
    classes = LearnProjectServiceApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class JobTest {

    @Resource
    private IProjectService projectService;
    @Resource
    RedisTemplate redisTemplate;

    @Test
    void jobTest() {
        Map<String, String> dataSources = redisTemplate.opsForHash().entries(TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            log.error("data_source_is_empty,current_job:courseStudyStatistics()");
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        for (String dbName : dataSources.keySet()) {
            log.info("开始处理数据!");
            UserThreadContext.setTenantId(dbName.replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            String targetId = "20240820173022859d81a579460f87f418d5";
            projectService.projectUserStudyConditionOldDate(targetId);
        }
        UserThreadContext.remove();
        Assert.assertTrue(true);
    }

}