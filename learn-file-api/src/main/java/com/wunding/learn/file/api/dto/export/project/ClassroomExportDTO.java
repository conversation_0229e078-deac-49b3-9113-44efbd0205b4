package com.wunding.learn.file.api.dto.export.project;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.ExportDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * 教室列表对象
 */
@Data
@Schema(name = "ClassroomExportDTO", description = "教室列表对象")
public class ClassroomExportDTO extends ExportDTO {

    @Schema(description = "名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.roomName}")
    private String roomName;

    @Schema(description = "地点")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.address}")
    private String address;

    @Schema(description = "备注")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.description}")
    private String description;

    @Schema(description = "添加人")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.createBy}")
    private String createBy;

    @Schema(description = "添加时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.createTime}")
    private Date createTime;
}
