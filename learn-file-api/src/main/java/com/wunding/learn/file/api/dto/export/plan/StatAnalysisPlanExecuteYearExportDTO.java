package com.wunding.learn.file.api.dto.export.plan;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * </p> 培训计划执行统计
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2023-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "StatAnalysisPlanExecuteMonthExportDTO", description = "培训计划执行统计导出对象")
public class StatAnalysisPlanExecuteYearExportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织名称
     */
    @Schema(description = "组织名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.orgPath}")
    private String orgPath;

    /**
     * 月份/年份
     */
    @Schema(description = "月份/年份")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.mouthOrYear}")
    private String mouthOrYear;

    /**
     * 总人数
     */
    @Schema(description = "部门总人数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.totalNumber}")
    private Long totalNumber;

    /**
     * 参加培训计划总人数
     */
    @Schema(description = "参加培训计划总人数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.userNumber}")
    private Long userNumber;


    /**
     * 覆盖率
     */
    @Schema(description = "覆盖率")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.coverageRate}")
    private String coverageRate;


    /**
     * 人均学时
     */
    @Schema(description = "人均学时")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.avgDuration}")
    private BigDecimal avgDuration;


    /**
     * 满意度
     */
    @Schema(description = "满意度")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.avgEvaluation}")
    private BigDecimal avgEvaluation;

}
