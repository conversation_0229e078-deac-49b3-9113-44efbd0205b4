package com.wunding.learn.file.api.dto.export.analysis;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.SexConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/9
 */
@Data
@Schema(name = "LearnRecordStatAnalysisExportDTO", description = "学习档案列表导出对象")
public class LearnRecordStatAnalysisExportDTO {

    @Schema(description = "姓名")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.fullName}")
    private String fullName;

    @Schema(description = "账号")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.loginName}")
    private String loginName;

    @Schema(description = "性别 1-男 2-女")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.sex}",  converter = SexConverter.class)
    private Integer sex;

    @Schema(description = "电话")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.telephone}")
    private String telephone;

    @Schema(description = "邮箱")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.email}")
    private String email;

    @Schema(description = "生日")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.birthday}")
    private Date birthday;

    @Schema(description = "入职日期")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.joinDate}")
    private Date joinDate;

    @Schema(description = "所属部门")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.orgPath}")
    private String orgPath;

    @Schema(description = "所属岗位")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.postName}")
    private String postName;

    @Schema(description = "新学课程数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.totalCourse}")
    private Long totalCourse;

    @Schema(description = "有效学习时长")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.totalLearn}")
    private Long totalLearn;

    @Schema(description = "参加学习项目数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.projectNum}")
    private Long projectNum;

    @Schema(description = "参加面授班级数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.faceProjectNum}")
    private Long faceProjectNum;

    @Schema(description = "参加培训项目数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.trainNum}")
    private Long trainNum;

    @Schema(description = "小计")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.subtotal}")
    private Long subtotal;

    @Schema(description = "参加考试")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.examNum}")
    private Long examNum;

    @Schema(description = "浏览资讯")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.newsNum}")
    private Long newsNum;

    @Schema(description = "参加调研")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.surveyNum}")
    private Long surveyNum;

    @Schema(description = "点赞数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.totalAgree}")
    private Long totalAgree;

    @Schema(description = "评论数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.totalComment}")
    private Long totalComment;

}
