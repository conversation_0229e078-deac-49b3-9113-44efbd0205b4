package com.wunding.learn.file.api.dto.export.library;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.WhetherConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
@Schema(name = "SurveyLibExportDTO", description = "调研库列表导出对象")
public class SurveyLibExportDTO {

    @Schema(description = "调研名称")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.surveyName}")
    private String surveyName;

    @Schema(description = "添加时间")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.createTime}")
    private Date createTime;

    @Schema(description = "添加者")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.createBy}")
    private String createBy;

    @Schema(description = "调研库分类名称")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.categoryName}")
    private String categoryName;

    @Schema(description = "是否启用 0否 1是")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.available}",  converter = WhetherConverter.class)
    private Integer available;

    @Schema(description = "编辑时间")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.updateTime}")
    private Date updateTime;

    @Schema(description = "编辑者")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.updateBy}")
    private String updateBy;
}
