package com.wunding.learn.file.api.dto.export.project;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.PublishConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * 周期项目列表导出对象
 */
@Data
@Schema(name = "ProjectFixedCycleExportDTO", description = "周期项目列表导出对象")
public class ProjectFixedCycleExportDTO {

    @Schema(description = "项目编号")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.proNo}")
    private String proNo;

    @Schema(description = "项目名称")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.proName}")
    private String proName;

    @Schema(description = "创建时间")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.createTime}")
    private Date createTime;

    @Schema(description = "固定周期天数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.cycleDay}")
    private Long cycleDay;

    @Schema(description = "发布人")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.publishBy}")
    private String publishBy;

    @Schema(description = "状态 0 未发布 1 已发布")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.isPublish}",  converter = PublishConverter.class)
    private Integer isPublish;

    @Schema(description = "任务数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.taskNum}")
    private Integer taskNum;

    @Schema(description = "参与人数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.peopleNum}")
    private Integer peopleNum;

    @Schema(description = "关联项目数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.relatedProjectNum}")
    private Integer relatedProjectNum;
}
