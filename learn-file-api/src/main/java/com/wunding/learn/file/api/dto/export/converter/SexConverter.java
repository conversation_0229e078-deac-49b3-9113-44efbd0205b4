package com.wunding.learn.file.api.dto.export.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.wunding.learn.common.enums.user.SexEnum;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * 导出 性别 男/女 转换
 *
 * <AUTHOR>
 * @date 2022/10/24
 */
public class SexConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
        GlobalConfiguration globalConfiguration) throws Exception {
        Integer res = null;
        String cellText = cellData.getStringValue();
        if (StringUtils.equals(cellText, SexEnum.MAN.getName())) {
            res = SexEnum.MAN.getValue();
        } else if (StringUtils.equals(cellText, SexEnum.WOMEN.getName())) {
            res = SexEnum.WOMEN.getValue();
        } else if (StringUtils.equals(cellText, SexEnum.UNKNOW.getName())) {
            res = SexEnum.UNKNOW.getValue();
        }
        return res;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty,
        GlobalConfiguration globalConfiguration) throws Exception {
        String res = "";
        if (Objects.equals(value, SexEnum.MAN.getValue())) {
            res = SexEnum.MAN.getName();
        } else if (Objects.equals(value, SexEnum.WOMEN.getValue())) {
            res = SexEnum.WOMEN.getName();
        } else if (Objects.equals(value, SexEnum.UNKNOW.getValue())) {
            res = SexEnum.UNKNOW.getName();
        }
        return new WriteCellData<>(res);
    }
}
