package com.wunding.learn.file.api.dto.export.library;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.DifficultyConverter;
import com.wunding.learn.file.api.dto.export.converter.ExamQuestionTypeConverter;
import com.wunding.learn.file.api.dto.export.converter.QuestionCompulsoryConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/09
 */
@Data
@Schema(name = "LibQuestionExportDTO", description = "导出考题库题目对象")
public class LibQuestionExportDTO {

    /**
     * 题目标题
     */
    @Schema(description = "题目标题")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.questionName}")
    private String questionName;

    /**
     * 题目类型：1：单选 2：多选 3：填空 4：判断 5：问答
     */
    @Schema(description = "题目类型：1：单选 2：多选 3：填空 4：判断 5：问答")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.questionType}", converter = ExamQuestionTypeConverter.class)
    private Integer questionType;

    /**
     * 题目答案
     */
    @Schema(description = "题目答案")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.answer}")
    private String answer;

    /**
     * 难易度：0 低 1 中 2 高
     */
    @Schema(description = "难易度：0 低 1 中 2 高")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.difficulty}",  converter = DifficultyConverter.class)
    private Integer difficulty;

    @Schema(description = "知识点")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.knowledgePoints}")
    private String knowledgePoints;

    /**
     * 必考题： 0 否 1是
     */
    @Schema(description = "必考题： 0 否 1是")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.isCompulsory}",  converter = QuestionCompulsoryConverter.class)
    private Integer isCompulsory;

}
