package com.wunding.learn.file.api.dto.export.emigrated;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: StatisticalExportDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/11/10 15:00
 */
@Data
@Schema(name = "StatisticalExportDTO", description = "导出闯关统计列表对象")
public class StatisticalExportDTO {

    /**
     * 排名
     */
    @Schema(description = "排名")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.ranking}")
    private Integer ranking;

    /**
     * 用户姓名
     */
    @Schema(description = "姓名")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.fullName}")
    private String fullName;
    /**
     * 用户账号
     */
    @Schema(description = "账号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.loginName}")
    private String loginName;

    /**
     * 用户部门
     */
    @Schema(description = "部门")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.orgPath}")
    private String orgPath;

    /**
     * 状态(0:未开始,1:进行中,2:已完成)
     */
    @Schema(description = "状态(0:未开始,1:进行中,2:已完成)")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.status}")
    private String status;

    /**
     * 当前关卡排序序号
     */
    @Schema(description = "当前关卡序号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.checkpointSort}")
    private Integer checkpointSort;
    /**
     * 当前关卡名字
     */
    @Schema(description = "当前关卡")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.checkpointName}")
    private String checkpointName;

    /**
     * 当前任务名字
     */
    @Schema(description = "当前任务")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.taskName}")
    private String taskName;

    /**
     * 总得分
     */
    @Schema(description = "总得分")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.score}")
    private Long score;

}
