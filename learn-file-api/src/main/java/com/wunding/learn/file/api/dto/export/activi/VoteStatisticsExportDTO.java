package com.wunding.learn.file.api.dto.export.activi;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/13 14:24
 */
@Data
@Schema(name = "VoteStatisticsExportDTO", description = "导出投票统计列表对象")
public class VoteStatisticsExportDTO {

    /**
     * 投票名称
     */
    @Schema(description = "投票名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.voteName}")
    private String voteName;

    /**
     * 投票标题
     */
    @Schema(description = "投票标题")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.title}")
    private String title;

    /**
     * 是否有课件
     */
    @Schema(description = "是否有课件")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.isExistCourseWare}")
    private String isExistCourseWare;

    /**
     * 总票数
     */
    @Schema(description = "总票数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.allTicketNum}")
    private String allTicketNum;

}
