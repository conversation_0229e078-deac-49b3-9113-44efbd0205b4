package com.wunding.learn.file.api.dto.export.lecturer;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/15
 */
@Data
@Schema(name = "LecturerCourseAuthTeachExportDTO", description = "课程认证讲师-授课明细导出数据对象")
public class LecturerCourseAuthTeachExportDTO {

    @Schema(description = "讲师编号")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.lecturerCode}")
    private String lecturerCode;

    @Schema(description = "讲师名称")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.lecturerName}")
    private String lecturerName;

    @Schema(description = "讲师账号")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.employeeNo}")
    private String employeeNo;

    @Schema(description = "讲师分类")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.lecturerCategory}")
    private String lecturerCategory;

    @Schema(description = "讲师等级")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.lecturerLevel}")
    private String lecturerLevel;

    @Schema(description = "评估分")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.evalScore}")
    private BigDecimal evalScore;

    @Schema(description = "授课次数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.teachNum}")
    private Integer teachNum;

    @Schema(description = "课程开发数")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.courseNum}")
    private Integer courseNum;
}
