package com.wunding.learn.file.api.dto.export.reading;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "TaskFinishExportDTO", description = "导出图书统计列表-学员排行对象")
public class UserRankReadingExportDTO {

    @Schema(description = "排名")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.ranking}")
    private Integer ranking;

    @Schema(description = "学员")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.userName}")
    private String userName;

    @Schema(description = "账号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.userLoginName}")
    private String userLoginName;

    @Schema(description = "组织")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.orgPath}")
    private String orgPath;

    @Schema(description = "总分")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.score}")
    private Integer score;
}
