package com.wunding.learn.file.api.dto.export.exam;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.AnswerModelConverter;
import com.wunding.learn.file.api.dto.export.converter.PeopleModelConverter;
import com.wunding.learn.file.api.dto.export.converter.PublishConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 考试竞赛导出
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/9/19 13:32
 */
@Data
@Schema(name = "ExamCompetitionExportDTO", description = "导出考试竞赛列表数据对象")
public class ExamCompetitionExportDTO {


    /**
     * 名称
     */
    @Schema(description = "编码")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.code}")
    private String code;

    @Schema(description = "名称")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.name}")
    private String name;

    /**
     * 人员模式 single=单人挑战(AI对战) double=双人对决  multi=多人混战 group=团队竞技
     */
    @Schema(description = "人员模式 single=单人挑战(AI对战) double=双人对决  multi=多人混战 group=团队竞技")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.peopleModel}",  converter = PeopleModelConverter.class)
    private String peopleModel;


    /**
     * 答题模式 pk=PK答题模式  keep=一站到底 time=计时赛
     */
    @Schema(description = "答题模式 pk=PK答题模式  keep=一站到底 time=计时赛")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.answerModel}",  converter = AnswerModelConverter.class)
    private String answerModel;


    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.startTime}")
    private Date startTime;


    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.endTime}")
    private Date endTime;

    /**
     * 是否发布
     */
    @Schema(description = "是否发布")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.isPublish}",  converter = PublishConverter.class)
    private Integer isPublish;


    @Schema(description = "创建人全名")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.createFullName}")
    private String createFullName;


    @Schema(description = "创建人用户名")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.createUserName}")
    private String createUserName;

    @Schema(description = "归属部门")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.orgName}")
    private String orgName;

    @Schema(description = "发布时间")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.publishTime}")
    private Date publishTime;
}
