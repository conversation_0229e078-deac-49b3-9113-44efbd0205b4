package com.wunding.learn.forum.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 帖子置顶状态Dto
 *
 * <AUTHOR>
 * @date 2022/06/23
 */
@Data
@Accessors(chain = true)
@Schema(name = "PostTopStatusDTO", description = "帖子置顶状态参数对象")
public class PostTopStatusDTO {

    /**
     * 帖子id列表
     */
    @Schema(description = "置顶/取消置顶帖子id集合")
    @NotEmpty(message = "数据不能为空")
    private List<String> postIdList;

    /**
     * 置顶状态
     */
    @Schema(description = "是否置顶 0=不置顶 1=置顶")
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "是否置顶为非定义数字")
    @Max(value = 1, message = "是否置顶为非定义数字")
    private Integer topStatus;
}
