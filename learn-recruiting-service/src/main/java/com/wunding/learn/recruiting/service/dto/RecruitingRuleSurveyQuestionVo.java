package com.wunding.learn.recruiting.service.dto;


import com.wunding.learn.recruiting.service.model.RecruitingRuleSurveyQuestion;
import com.wunding.learn.recruiting.service.model.RecruitingRuleSurveyQuestionItem;
import com.wunding.learn.recruiting.service.model.RecruitingRuleSurveyReply;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * @tiele RecruitingRuleSurveyQuestionVo
 * @projectName devlop-learn
 * @description: TODO  调研信息表Vo
 * @Date 2022/1/7  RecruitingRuleSurveyQuestionVo
 */
@Data
public class RecruitingRuleSurveyQuestionVo extends RecruitingRuleSurveyQuestion {

    /**
     * 调研题目对应选项
     */
    private List<RecruitingRuleSurveyQuestionItem> recruitingRuleSurveyQuestionItems;

    /**
     * ; 调研题目对应被招募人提交的答案
     */
    private List<RecruitingRuleSurveyReply> recruitingRuleSurveyReplies;
}
