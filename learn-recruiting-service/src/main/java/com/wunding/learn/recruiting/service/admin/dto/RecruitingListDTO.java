package com.wunding.learn.recruiting.service.admin.dto;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "RecruitingListDTO", description = "招募列表结果对象")
public class RecruitingListDTO {


    /**
     * 报名人数
     */
    @Schema(description = "报名人数")
    private Integer signUpCount;

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;


    /**
     * 招募编码(ZM+10位数字)
     */
    @Schema(description = "招募编码(ZM+10位数字)")
    private String recruitingCode;


    /**
     * 招募类型(1:讲师,2:课程,3:其它)
     */
    @Schema(description = "招募类型(1:讲师,2:课程,3:其它)")
    private Integer type;

    /**
     * 开放日期开始时间
     */
    @Schema(description = "开放日期开始时间")
    private Date openBeginTime;


    /**
     * 开放日期-结束时间
     */
    @Schema(description = "开放日期-结束时间")
    private Date openEndTime;


    /**
     * 是否长期开放 0-否 1-是
     */
    @Schema(description = "是否长期开放 0-否 1-是")
    private Integer longTimeOpen;


    /**
     * 举办组织
     */
    @Schema(description = "举办组织")
    private String sponsorOrg;


    /**
     * 地点
     */
    @Schema(description = "地点")
    private String address;


    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;


    /**
     * 招募封面
     */
    @Schema(description = "招募封面")
    private NamePath coverImage;


    /**
     * 招募宣传图片
     */
    @Schema(description = "招募宣传图片")
    private NamePath advertiseImage;


    /**
     * 协办处理类型 1-系统随机分配审核人 2-按各自协办范围审核 3-所有人同时审核
     */
    @Schema(description = "协办处理类型 1-系统随机分配审核人 2-按各自协办范围审核 3-所有人同时审核")
    private Integer jointlyConductType;


    /**
     * 人数是否限制 0-否 1-是
     */
    @Schema(description = "人数是否限制 0-否 1-是")
    private Integer isLimit;


    /**
     * 人数限制
     */
    @Schema(description = "人数限制")
    private Integer limitNum;


    /**
     * 招募活动说明
     */
    @Schema(description = "招募活动说明")
    private String description;


    /**
     * 发布状态(1:已发布,0未发布)
     */
    @Schema(description = "发布状态(1:已发布,0未发布)")
    private Integer isPublish;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "创建、归属部门Id")
    private String orgId;

    @Schema(description = "创建、归属部门")
    private String orgName;
}
