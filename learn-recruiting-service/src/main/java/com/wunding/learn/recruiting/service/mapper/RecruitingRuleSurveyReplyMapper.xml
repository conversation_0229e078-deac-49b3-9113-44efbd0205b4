<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.recruiting.service.mapper.RecruitingRuleSurveyReplyMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.recruiting.service.mapper.RecruitingRuleSurveyReplyMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.recruiting.service.model.RecruitingRuleSurveyReply">
            <!--@Table recruiting_rule_survey_reply-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="rule_id" jdbcType="VARCHAR"
                            property="ruleId"/>
                    <result column="record_id" jdbcType="VARCHAR"
                            property="recordId"/>
                    <result column="question_id" jdbcType="VARCHAR"
                            property="questionId"/>
                    <result column="item_id" jdbcType="VARCHAR"
                            property="itemId"/>
                    <result column="user_id" jdbcType="VARCHAR"
                            property="userId"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, rule_id, record_id, question_id, item_id, user_id
        </sql>

</mapper>
