package com.wunding.learn.recruiting.service.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.recruiting.service.model.RecruitingRuleSurveyQuestionItem;


/**
 * <p> 招募规则调研题目选项表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen<PERSON><PERSON><PERSON></a>
 * @since 2022-09-01
 */
public interface IRecruitingRuleSurveyQuestionItemService extends IService<RecruitingRuleSurveyQuestionItem> {

    /**
     * 删除选项
     *
     * @param ruleId     规则id
     * @param questionId 题目id
     */
    void delByQuestionId(String ruleId, String questionId);

}
