package com.wunding.learn.apply.api.query;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/3/10
 */
@Data
public class AuthQuery {


    /**
     * 资源类型
     */
    @Parameter(required = true, description = "资源类型[COURSE_DOWNLOAD：课程下载,LECTURER:讲师]")
    private String resourcesType;


    /**
     * 资源id
     */
    @Parameter(required = true, description = "资源id")
    private String resourcesId;

    /**
     * 申请人id
     */
    @Parameter(description = "申请人id", hidden = true)
    private String applyUserId;

    /**
     * 申请组织id
     */
    @Parameter(description = "申请组织id", hidden = true)
    private String applyOrgId;
}
