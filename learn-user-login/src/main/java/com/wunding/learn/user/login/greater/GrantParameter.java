package com.wunding.learn.user.login.greater;

import lombok.Builder;
import lombok.Data;

/**
 * 授权参数
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
@Data
@Builder
public class GrantParameter {

    /**
     * 登录端类型
     */
    private LoginClientTypeEnum clientType;

    /**
     * 授权类型
     */
    private GrantTypeEnum type;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 登录验证码
     */
    private String captcha;

    /**
     * 验证码唯一id
     */
    private String captchaId;

    /**
     * 入口类型：0-企业微信 1-微信公众号 2-微信小程序 3-钉钉 4-飞书
     */
    private Integer entryType;

    /**
     * 授权Code
     */
    private String code;

    /**
     * 用户唯一标识
     */
    private String openId;

    /**
     * 自定义参数
     */
    private String state;

    /**
     * 企业微信 0-澳美点击 1-澳美领航
     */
    private String weChatType;

    /**
     * 加密令牌
     */
    private String encryptToken;

}
