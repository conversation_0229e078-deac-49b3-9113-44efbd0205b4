package com.wunding.learn.user.login.greater;

import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/2/16
 */
@Getter
public enum GrantTypeEnum {

    /**
     * 账号密码
     */
    PASSWORD("password"),

    /**
     * 短信验证码
     */
    SMS("sms"),

    /**
     * 第三方（微信公众号/微信小程序/企业微信）
     */
    OAUTH2("oauth2"),

    /**
     * CAS
     */
    CAS("cas"),

    /**
     * 外部加密信息登录
     */
    EXTERNAL("external");

    private final String type;

    GrantTypeEnum(String type) {
        this.type = type;
    }

    /**
     * 获取授权登录方式
     *
     * @param type 登录类型
     * @return {@link GrantTypeEnum}
     */
    public static GrantTypeEnum get(String type) {
        for (GrantTypeEnum value : values()) {
            if (Objects.equals(value.type, type)) {
                return value;
            }
        }
        return PASSWORD;
    }

}
