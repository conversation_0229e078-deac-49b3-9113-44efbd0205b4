package com.wunding.learn.lecturer.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: cdl @Date: 2022/7/12 10:25
 */
@Data
@Accessors(chain = true)
@Schema(name = "VirtualCostDTO", description = "虚拟课酬")
public class VirtualCostDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "虚拟课酬数量")
    private Integer virtualClassFees;

    @Schema(description = "计量单位 1-金币 2-积分")
    private Integer virtualClassFeesType;
}
