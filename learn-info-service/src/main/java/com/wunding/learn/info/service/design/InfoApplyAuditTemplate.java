package com.wunding.learn.info.service.design;

import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.flowable.api.design.template.ApplyAuditTemplate;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.flowable.api.service.IProcessInstanceResourceService;
import com.wunding.learn.flowable.api.service.IProcessInstanceTaskResourceService;
import com.wunding.learn.info.service.dao.InfoDao;
import com.wunding.learn.info.service.model.Info;
import com.wunding.learn.info.service.model.InfoCategory;
import com.wunding.learn.info.service.service.IInfoCategoryService;
import com.wunding.learn.user.api.service.UserFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <p> 应用审计模板实现（根据Component名称搜索哪里使用了，直接点击idea没那么聪明）
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-10
 */
@Component("infoApplyAuditTemplate")
@Slf4j
public class InfoApplyAuditTemplate extends ApplyAuditTemplate<Info> {


    protected final InfoDao infoDao;
    protected final IInfoCategoryService infoCategoryService;

    public InfoApplyAuditTemplate(ProcessFeign processFeign,
        IProcessInstanceResourceService processInstanceResourceService,
        IProcessInstanceTaskResourceService processInstanceTaskResourceService,
        ICategorysService categorysService,
        RedisTemplate<String, Object> redisTemplate,
        UserFeign userFeign,
        InfoDao infoDao,
        IInfoCategoryService infoCategoryService) {
        super.userFeign = userFeign;
        super.redisTemplate = redisTemplate;
        super.categorysService = categorysService;
        super.processInstanceTaskResourceService = processInstanceTaskResourceService;
        super.processInstanceResourceService = processInstanceResourceService;
        super.processFeign = processFeign;
        this.infoDao = infoDao;
        this.infoCategoryService = infoCategoryService;
    }

    @Override
    protected void updateResource(Info resource) {
        infoDao.updateInfo(resource);
    }

    @Override
    protected Info getResource(String resourceId) {
        Info info = infoDao.getById(resourceId);
        InfoCategory infoCategory = infoCategoryService.getById(info.getInfoCateId());
        // 历史设计问题，有一张中间表关联分类关系，这里做一层转换
        info.setCategoryId(infoCategory.getCategoryId());
        return info;
    }
}
