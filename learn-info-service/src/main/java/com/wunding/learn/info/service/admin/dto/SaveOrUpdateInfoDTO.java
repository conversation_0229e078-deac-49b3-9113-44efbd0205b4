package com.wunding.learn.info.service.admin.dto;

import com.wunding.learn.common.aop.valid.constraints.RichTextLength;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * 保存或更新资讯dto
 *
 * <AUTHOR>
 * @date 2022/08/05
 */
@Data
@Accessors(chain = true)
@Schema(name = "SaveOrUpdateInfoDTO", description = "保存资讯对象")
public class SaveOrUpdateInfoDTO {

    /**
     * 资讯id
     */
    @Schema(description = "更新时携带id")
    private String infoId;

    /**
     * 资讯标题
     */
    @Schema(description = "资讯标题")
    @NotBlank(message = "资讯标题不能为空")
    @Length(max = 80, message = "资讯标题长度超出80字限制")
    private String title;

    /**
     * 作者
     */
    @Schema(description = "资讯作者")
    @Length(max = 36, message = "作者id长度不能超过36")
    private String author;

    /**
     * 资讯关键词
     */
    @Schema(description = "资讯关键词")
    @Length(max = 80, message = "关键词最大输入80个字符")
    private String keyWord;

    /**
     * 资讯来源
     */
    @Schema(description = "资讯来源")
    @Length(max = 80, message = "资讯来源长度超出80字限制")
    private String source;


    /**
     * 是否允许评论
     */
    @Schema(description = "是否允许评论")
    @NotNull
    @Min(value = 0, message = "允许评论为非定义数字")
    @Max(value = 1, message = "允许评论为非定义数字")
    private Integer isComment;

    /**
     * 资讯图片
     */
    @Schema(description = "资讯图片")
    private NamePath infoImage;

    /**
     * 资讯分类ID
     */
    @Schema(description = "资讯分类ID")
    @NotBlank(message = "资讯分类不能为空")
    private String infoCateId;

    /**
     * 资讯格式 0在线编辑 1附件上传
     */
    @Schema(description = "资讯格式 0在线编辑 1附件上传")
    @NotNull
    @Min(value = 0, message = "资讯格式为非定义数字")
    @Max(value = 1, message = "资讯格式为非定义数字")
    private Integer infomationSource;

    /**
     * 资讯正文 建议存储路径，对应一个txt文件
     */
    @Schema(description = "资讯正文")
    @RichTextLength(max = 20000)
    private String infoContent;

    /**
     * 课件上传
     */
    @Schema(description = "课件上传")
    private FileUpload uploadFile;

    /**
     * 上传文件简介
     */
    @Schema(description = "上传文件简介")
    private String descriptions;

    @Schema(description = "新闻资讯来源 0-新闻资讯管理 1-学习项目 2-专题")
    private Integer isTrain;


    @Schema(description = "新闻资讯是否发布")
    private Integer isPublish;

    /**
     * pushDTO
     */
    @Schema(description = "推送通知设置")
    private PushNoticeSetDTO pushNoticeSetDTO;


    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见", required = true)
    @NotNull(message = "下发可见方式不可为空")
    private Integer viewType;

    @Schema(description = "下发范围方案id", required = true)
    @NotNull(message = "下发范围方案id不可为空")
    private Long programmeId;

    @Schema(description = "创建、归属部门Id")
    private String orgId;
}
