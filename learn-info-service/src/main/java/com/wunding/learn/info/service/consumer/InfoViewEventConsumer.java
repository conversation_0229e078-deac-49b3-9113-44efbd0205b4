package com.wunding.learn.info.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.ResourceInteractEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.info.service.event.InfoViewEvent;
import com.wunding.learn.info.service.service.IInfoService;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * <p> 资讯浏览事件消费
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-03-15
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class InfoViewEventConsumer {

    private final IInfoService infoService;

    /**
     * 资讯浏览事件消息队列
     */
    public static final String INFO_VIEW_EVENT_CONSUMER_QUEUE = "InfoViewEventConsumerQueue";

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = INFO_VIEW_EVENT_CONSUMER_QUEUE), exchange = @Exchange(value = ResourceInteractEvent.SYSTEM_RESOURCE_INTERACT_EXCHANGE, type = ResourceInteractEvent.SYSTEM_RESOURCE_INTERACT_EXCHANGE_TYPE), key = {
        ResourceInteractEventRoutingKeyConstants.INFO_VIEW_EVENT}), id = "infoViewEventConsumer")
    public void infoViewEventConsumer(@Payload InfoViewEvent infoViewEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        UserThreadContext.setTenantId(infoViewEvent.getTenantId());
        log.info("infoViewEventConsumer receive event :{} ", JsonUtil.objToJson(infoViewEvent));

        // 更新资讯互动数据-浏览数
        infoService.updateInfoInteractNum(infoViewEvent.getResourceId(),
            ResourceInteractEventRoutingKeyConstants.INFO_VIEW_EVENT);
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(infoViewEvent,channel,deliveryTag, false);
    }

}
