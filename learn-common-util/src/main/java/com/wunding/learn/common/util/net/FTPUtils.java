package com.wunding.learn.common.util.net;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

/**
 * FTP工具类，封装了FTP文件的上传、下载、删除、列出文件等常见操作。
 */
@AllArgsConstructor
@Slf4j
public class FTPUtils {

    private final FTPClient ftpClient;
    private final String server;
    private final int port;
    private final String user;
    private final String password;


    /**
     * 连接到FTP服务器。
     *
     * @throws IOException 如果连接失败抛出异常
     */
    public void connect() throws IOException {
        ftpClient.connect(server, port);
        int replyCode = ftpClient.getReplyCode();
        if (!FTPReply.isPositiveCompletion(replyCode)) {
            throw new IOException("无法连接到FTP服务器，回复码: " + replyCode);
        }
        boolean success = ftpClient.login(user, password);
        if (!success) {
            throw new IOException("FTP登录失败，请检查用户名和密码。");
        }
        ftpClient.enterLocalPassiveMode();  // 切换到被动模式
        log.info("成功连接到FTP服务器 {}，端口: {}", server, port);
    }

    /**
     * 断开与FTP服务器的连接。
     *
     * @throws IOException 如果断开失败抛出异常
     */
    public void disconnect() {
        try {
            if (ftpClient.isConnected()) {
                ftpClient.logout();
                ftpClient.disconnect();
                log.info("成功断开与FTP服务器的连接。");
            }
        } catch (Exception e) {
            log.error("断开与FTP服务器的连接失败", e);
        }
    }

    /**
     * 上传文件到FTP服务器的指定目录。
     *
     * @param localFilePath  本地文件路径
     * @param remoteFilePath 远程文件路径
     * @return 上传是否成功
     * @throws IOException 如果上传失败抛出异常
     */
    public boolean uploadFile(String localFilePath, String remoteFilePath) throws IOException {
        try (InputStream inputStream = new FileInputStream(localFilePath)) {
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            boolean done = ftpClient.storeFile(remoteFilePath, inputStream);
            if (done) {
                log.info("文件上传成功: {} -> {}", localFilePath, remoteFilePath);
                return true;
            } else {
                log.warn("文件上传失败: {} -> {}", localFilePath, remoteFilePath);
                return false;
            }
        }
    }

    /**
     * 从FTP服务器下载文件。
     *
     * @param remoteFilePath 远程文件路径
     * @param localFilePath  本地保存路径
     * @return 下载是否成功
     * @throws IOException 如果下载失败抛出异常
     */
    public boolean downloadFile(String remoteFilePath, String localFilePath) throws IOException {
        try (OutputStream outputStream = new FileOutputStream(localFilePath)) {
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            boolean done = ftpClient.retrieveFile(remoteFilePath, outputStream);
            if (done) {
                log.info("文件下载成功: {} -> {}", remoteFilePath, localFilePath);
                return true;
            } else {
                log.warn("文件下载失败: {} -> {}", remoteFilePath, localFilePath);
                return false;
            }
        }
    }

    /**
     * 从FTP服务器删除文件。
     *
     * @param remoteFilePath 远程文件路径
     * @return 删除是否成功
     * @throws IOException 如果删除失败抛出异常
     */
    public boolean deleteFile(String remoteFilePath) throws IOException {
        boolean deleted = ftpClient.deleteFile(remoteFilePath);
        if (deleted) {
            log.info("文件删除成功: {}", remoteFilePath);
            return true;
        } else {
            log.warn("文件删除失败: {}", remoteFilePath);
            return false;
        }
    }

    /**
     * 列出FTP服务器指定目录下的所有文件和目录。
     *
     * @param remoteDirPath 远程目录路径
     * @return 文件列表
     * @throws IOException 如果列出文件失败抛出异常
     */
    public FTPFile[] listFiles(String remoteDirPath) throws IOException {
        FTPFile[] files = ftpClient.listFiles(remoteDirPath);
        if (files != null && files.length > 0) {
            log.info("列出目录成功: {}", remoteDirPath);
        } else {
            log.warn("目录为空或列出目录失败: {}", remoteDirPath);
        }
        return files;
    }

    /**
     * 切换工作目录。
     *
     * @param remoteDirPath 远程目录路径
     * @return 切换是否成功
     * @throws IOException 如果切换目录失败抛出异常
     */
    public boolean changeWorkingDirectory(String remoteDirPath) throws IOException {
        boolean success = ftpClient.changeWorkingDirectory(remoteDirPath);
        if (success) {
            log.info("成功切换到目录: {}", remoteDirPath);
        } else {
            log.warn("无法切换到目录: {}", remoteDirPath);
        }
        return success;
    }

    /**
     * 在FTP服务器上创建新目录。
     *
     * @param remoteDirPath 远程目录路径
     * @return 创建是否成功
     * @throws IOException 如果创建目录失败抛出异常
     */
    public boolean makeDirectory(String remoteDirPath) throws IOException {
        boolean created = ftpClient.makeDirectory(remoteDirPath);
        if (created) {
            log.info("成功创建目录: {}", remoteDirPath);
        } else {
            log.warn("无法创建目录: {}", remoteDirPath);
        }
        return created;
    }

    /**
     * 删除FTP服务器上的目录。
     *
     * @param remoteDirPath 远程目录路径
     * @return 删除是否成功
     * @throws IOException 如果删除目录失败抛出异常
     */
    public boolean removeDirectory(String remoteDirPath) throws IOException {
        boolean removed = ftpClient.removeDirectory(remoteDirPath);
        if (removed) {
            log.info("成功删除目录: {}", remoteDirPath);
        } else {
            log.warn("无法删除目录: {}", remoteDirPath);
        }
        return removed;
    }

    /**
     * 重命名远程文件。
     *
     * @param oldRemotePath 旧的远程文件路径
     * @param newRemotePath 新的远程文件路径
     * @return 重命名是否成功
     * @throws IOException 如果重命名失败抛出异常
     */
    public boolean renameFile(String oldRemotePath, String newRemotePath) throws IOException {
        boolean renamed = ftpClient.rename(oldRemotePath, newRemotePath);
        if (renamed) {
            log.info("文件重命名成功: {} -> {}", oldRemotePath, newRemotePath);
        } else {
            log.warn("文件重命名失败: {} -> {}", oldRemotePath, newRemotePath);
        }
        return renamed;
    }

    /**
     * 检查远程文件或目录是否存在。
     *
     * @param remotePath 远程文件或目录路径
     * @return 是否存在
     * @throws IOException 如果检查失败抛出异常
     */
    public boolean exists(String remotePath) throws IOException {
        FTPFile[] files = ftpClient.listFiles(remotePath);
        return files.length > 0;
    }
}
