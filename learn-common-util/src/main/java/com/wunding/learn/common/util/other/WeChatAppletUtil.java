package com.wunding.learn.common.util.other;


import com.github.pagehelper.util.StringUtil;
import com.wunding.learn.common.constant.http.TokenConstant;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.http.HttpUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.wunding.learn.common.constant.wechat.WeChatExceptionEnum.GENERATE_TOKEN_ERROR;

/**
 * <p>
 * 微信小程序工具类
 * </p>
 * 开发文档地址 https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-access-token/getAccessToken.html
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhongchaoheng</a>
 * @date 2023/7/5 15:41
 */
@Slf4j
@Component
public class WeChatAppletUtil {

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 小程序密钥
     */
    private String appSecret;
    /**
     * 获取微信token地址
     */
    private static final String GET_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";

    private static final String ERRORCODE = "errcode";
    /**
     * 获取微信手机号地址
     */
    private static final String POST_PHONE_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=ACCESS_TOKEN";

    private static final String GET_OPENID_URL = "https://api.weixin.qq.com/sns/jscode2session";

    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;

    /**
     * 初始化微信小程序配置信息
     *
     * @param appId
     * @param appSecret
     * @return
     */
    public void initialize(String appId, String appSecret) {
        this.appId = appId;
        this.appSecret = appSecret;
    }

    /**
     * 获取微信手机号
     *
     * @param code
     * @return
     */
    public String getPhoneNumber(String code) throws Exception {
        String phone = null;
        String accessToken = getAccessToken();
        if(StringUtil.isEmpty(accessToken)){
            return phone;
        }
        String url = POST_PHONE_URL.replace("ACCESS_TOKEN", accessToken);
        Map<String, Object> jsonParams = new HashMap<>(16);
        jsonParams.put("code", code);
        String json = JsonUtil.objToJson(jsonParams);
        log.info(">>>>>>>>>getPhoneNumber url:{},param:{}", url, json);
        //发送请求调用微信服务
        String result = HttpUtil.post(url, json);
        log.info("<<<<<<<<<getPhoneNumber result:{}", result);
        Map<String, Object> resultMap = JsonUtil.jsonToObj(result, Map.class);
        if (null != resultMap.get(ERRORCODE) && Objects.equals("0", resultMap.get(ERRORCODE))) {
            log.error("<<<<<<<<<getPhoneNumber exception:{}", resultMap);
            return phone;
        }
        Map<String, Object> phoneInfo = (Map<String, Object>) resultMap.get("phone_info");
        if (null != phoneInfo) {
            phone = (String) phoneInfo.get("phoneNumber");
        }
        return phone;

    }

    /**
     * 获取微信用户的openId
     *
     * @param
     * @return
     */
    public String getOpenId(String code) {
        String openId = null;
        String url = GET_OPENID_URL;
        Map<String, String> params = new HashMap<>(16);
        params.put("appid", appId);
        params.put("secret", appSecret);
        params.put("js_code", code);
        params.put("grant_type", "authorization_code");
        log.info("getOpenId url:{},param:{}", url, params);
        //发送请求调用微信服务
        String result = HttpUtil.get(url, HttpUtil.initParams("", params));
        log.info("getOpenId result:{}", result);
        Map<String, Object> resultMap = JsonUtil.jsonToObj(result, Map.class);
        if (null != resultMap.get(ERRORCODE) && !"0".equals(resultMap.get(ERRORCODE))) {
            log.error("getOpenId exception:{}", resultMap);
            return openId;
        }
        openId = (String) resultMap.get("openid");
        return openId;
    }


    /**
     * 获取token,对外暴露的方法
     *
     * @param
     * @return
     */
    public String getAccessToken() throws Exception {
        String accessToken;
        //如果过期或者为空,就重新生成一个accessToken
        Object value = redisTemplate.opsForValue().get(TokenConstant.WECHAT_ACCESS_TOKEN);
        Long expireTime = redisTemplate.opsForValue().getOperations().getExpire(TokenConstant.WECHAT_ACCESS_TOKEN,TimeUnit.SECONDS);
        if (value == null ||   expireTime <= 0) {
            log.error("getAccessToken token过期,刷新token");
            accessToken = generateToken();
        } else {
            log.error("getAccessToken token有效,直接返回token,{}",value);
            accessToken = String.valueOf(value);
        }
        return accessToken;
    }

    /**
     * 初始化token
     *
     * @param
     * @return
     */
    private String generateToken() throws BusinessException {
        String url = GET_TOKEN_URL.replace("APPID", appId).replace("APPSECRET", appSecret);
        String tokenStr = getToken(url);
        if(StringUtils.isEmpty(tokenStr)){
            //"generateToken 失败,获取token为空"
            throw new BusinessException(GENERATE_TOKEN_ERROR);
        }
        //转换为json数据,然后从中获取对象字符串
        Map<String, Object> tokenMap = JsonUtil.parseObject(tokenStr);
        String token = String.valueOf(tokenMap.get("access_token"));
        Integer expireIn = (Integer) tokenMap.get("expires_in");
        //存储到redis
        long expireTime =  expireIn - 100;
        redisTemplate.opsForValue().set(TokenConstant.WECHAT_ACCESS_TOKEN, token, expireTime, TimeUnit.SECONDS);
        return token;
    }


    /**
     * 调用微信服务生成token
     *
     * @param url
     * @return
     */
    private String getToken(String url) {
        try {
            log.info("getToken param: {}", url);
            URL urlObj = new URL(url);
            //开链接
            URLConnection urlConnection = urlObj.openConnection();
            InputStream inputStream = urlConnection.getInputStream();

            //将输入流存进b字节数组里
            byte[] b = new byte[1024];
            int len;
            StringBuilder sb = new StringBuilder();
            while ((len = inputStream.read(b)) != -1) {
                sb.append(new String(b, 0, len));
            }
            //返回组装好的access_token
            String tokenStr = sb.toString();
            log.info("getToken result: {}", tokenStr);
            return tokenStr;
        } catch (Exception e) {
            log.error("getToken exception:", e);
        }
        return null;
    }


}
