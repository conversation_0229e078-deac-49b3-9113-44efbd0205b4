package com.wunding.learn.march.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: EmigratedRankVo
 * @projectName learn
 * @description: 对应个人排行榜
 * @date 2022/2/1814:14
 */
@Data
@Schema(name = "EmigratedRankPersonalDTO", description = "游戏排行榜个人排行表列表返回对象")
public class MarchRankUserListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "该用户排行")
    private Integer no;

    @Schema(description = "用户名字")
    private String userName;

    @Schema(description = "用户账号")
    private String loginName;

    @Schema(description = "用户所属团队名称")
    private String teamName;

    @Schema(description = "用户头像地址")
    private String headImg;

    @Schema(description = "游戏主表id")
    private String marchId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "个人总里程")
    private BigDecimal totalMileage;

}
