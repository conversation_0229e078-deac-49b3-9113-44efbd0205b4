package com.wunding.learn.march.service.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.constant.march.MarchErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.march.MarchStylesTemplateEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.exception.handler.ApiAssert;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.forum.api.service.ForumFeign;
import com.wunding.learn.march.service.admin.dto.CheckpointListDTO;
import com.wunding.learn.march.service.admin.dto.CheckpointQueryDTO;
import com.wunding.learn.march.service.admin.dto.CheckpointSaveDTO;
import com.wunding.learn.march.service.biz.IMarchCheckpointBiz;
import com.wunding.learn.march.service.client.dto.CheckpointApiListDTO;
import com.wunding.learn.march.service.client.dto.CheckpointItem;
import com.wunding.learn.march.service.client.dto.TaskApiListDTO;
import com.wunding.learn.march.service.constsnts.MarchConstants;
import com.wunding.learn.march.service.enums.TaskTypeEnum;
import com.wunding.learn.march.service.model.MarchCheckpoint;
import com.wunding.learn.march.service.model.MarchHomeworkSubmitRecord;
import com.wunding.learn.march.service.model.March;
import com.wunding.learn.march.service.model.StylesTemplate;
import com.wunding.learn.march.service.model.MarchTaskRecord;
import com.wunding.learn.march.service.service.IHomeworkSubmitRecordMarchService;
import com.wunding.learn.march.service.service.IMarchCheckpointService;
import com.wunding.learn.march.service.service.IMarchService;
import com.wunding.learn.march.service.service.IMarchStylesTemplateService;
import com.wunding.learn.march.service.service.IMarchTaskRecordService;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <p>  游戏管卡业务层实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-06-15
 */
@Slf4j
@Component("marchCheckpointBiz")
public class IMarchCheckpointBizImpl implements IMarchCheckpointBiz {

    @Resource
    private IMarchCheckpointService marchCheckpointService;
    @Resource
    private IMarchService marchService;
    @Resource
    private IMarchStylesTemplateService marchStylesTemplateService;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private IMarchTaskRecordService marchTaskRecordService;
    @Resource(name = "march2HomeworkSubmitRecordService")
    private IHomeworkSubmitRecordMarchService homeworkSubmitRecordMarchService;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private ForumFeign forumFeign;

    @Override
    public PageInfo<CheckpointListDTO> list(CheckpointQueryDTO checkpointQueryDTO) {
        PageInfo<MarchCheckpoint> selectPageInfo = PageMethod.startPage(checkpointQueryDTO.getPageNo(),
            checkpointQueryDTO.getPageSize()).doSelectPageInfo(() ->
            marchCheckpointService.list(
                new LambdaQueryWrapper<MarchCheckpoint>().eq(MarchCheckpoint::getMarchId, checkpointQueryDTO.getMarchId())
                    .orderByAsc(MarchCheckpoint::getSort)));

        List<CheckpointListDTO> checkpointListDTOList = selectPageInfo.getList().stream().map(checkpoint -> {
            CheckpointListDTO checkpointListDTO = new CheckpointListDTO();
            BeanUtils.copyProperties(checkpoint, checkpointListDTO);
            return checkpointListDTO;
        }).collect(Collectors.toList());

        PageInfo<CheckpointListDTO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(selectPageInfo, pageInfo);
        pageInfo.setList(checkpointListDTOList);
        return pageInfo;
    }

    @Override
    public CheckpointItem getCheckpointDetail(String checkpointId) {
        MarchCheckpoint checkpoint = marchCheckpointService.getById(checkpointId);
        CheckpointItem checkpointItem = new CheckpointItem();
        checkpointItem.setId(checkpoint.getId());
        checkpointItem.setName(checkpoint.getName());
        checkpointItem.setSort(checkpoint.getSort());
        checkpointItem.setMileage(checkpoint.getMileage());
        checkpointItem.setStory(checkpoint.getStory());
        checkpointItem.setTitle(checkpoint.getTitle());
        return checkpointItem;
    }

    @Override
    public String create(CheckpointSaveDTO checkpointSaveDTO) {
        commonCheck(checkpointSaveDTO);
        MarchCheckpoint checkpoint = new MarchCheckpoint();
        BeanUtils.copyProperties(checkpointSaveDTO, checkpoint);
        String checkpointId = StringUtil.newId();
        checkpoint.setId(checkpointId);
        checkpoint.setIsPublish(PublishStatusEnum.IS_PUBLISH.getValue());
        marchCheckpointService.save(checkpoint);
        return checkpointId;
    }

    @Override
    public Boolean update(CheckpointSaveDTO checkpointSaveDTO) {
        // 参数校验
        commonCheck(checkpointSaveDTO);
        MarchCheckpoint dbCheckPoint = marchCheckpointService.getById(checkpointSaveDTO.getId());
        MarchCheckpoint checkpoint = new MarchCheckpoint();
        BeanUtils.copyProperties(checkpointSaveDTO, checkpoint);
        boolean update = marchCheckpointService.updateById(checkpoint);

        // 同步更新话题版块的名称
        if (!StringUtils.equals(dbCheckPoint.getName(), checkpoint.getName())) {
            forumFeign.updateSectionNameByCorrelationId(checkpoint.getId(), dbCheckPoint.getName(),
                checkpointSaveDTO.getName());
        }

        return update;
    }

    private void commonCheck(CheckpointSaveDTO checkpointSaveDTO) {
        // 参数范围
        BigDecimal mileage = checkpointSaveDTO.getMileage();
        if (BigDecimal.ZERO.compareTo(mileage) > 0 || BigDecimal.valueOf(10000L).compareTo(mileage) <= 0) {
            throw new BusinessException(MarchErrorNoEnum.ERR_PARAMS);
        }

        if (checkpointSaveDTO.getSort() != 1 && BigDecimal.ZERO.compareTo(mileage) >= 0) {
            throw new BusinessException(MarchErrorNoEnum.ERR_MILEAGE);
        }

        long sortCount = marchCheckpointService.count(
            new LambdaQueryWrapper<MarchCheckpoint>().eq(MarchCheckpoint::getMarchId, checkpointSaveDTO.getMarchId())
                .eq(MarchCheckpoint::getSort, checkpointSaveDTO.getSort())
                .ne(StringUtils.isNotEmpty(checkpointSaveDTO.getId()), MarchCheckpoint::getId, checkpointSaveDTO.getId()));
        ApiAssert.check(sortCount > 0, MarchErrorNoEnum.ERR_SORT_REPETITION);

    }

    @Override
    public void remove(String ids) {
        String[] idList = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
        Set<String> idSet = Arrays.stream(idList).collect(Collectors.toSet());
        marchCheckpointService.remove(new LambdaQueryWrapper<MarchCheckpoint>().in(MarchCheckpoint::getId, idSet));
    }

    @Override
    public List<CheckpointListDTO> allCheckpoints(String marchId) {

        List<MarchCheckpoint> checkpointList = marchCheckpointService.list(
            new LambdaQueryWrapper<MarchCheckpoint>().eq(MarchCheckpoint::getMarchId, marchId)
                .orderByAsc(MarchCheckpoint::getSort));

        return checkpointList.stream().map(checkpoint -> {
            CheckpointListDTO checkpointListDTO = new CheckpointListDTO();
            BeanUtils.copyProperties(checkpoint, checkpointListDTO);
            return checkpointListDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CheckpointItem> getMarchItemList(String marchId, Integer isIgnoreView) {
       marchService.checkApiMarch(marchId);

        //鉴权
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
//            marchService.chargeCurrentUserRole(marchId)
        }
        //获得关卡列表
        return marchCheckpointService.getMarchItemList(marchId);
    }

    @Override
    public PageInfo<TaskApiListDTO> getMarchTask(BaseEntity baseEntity, String checkpointId) {
        //获取当前用户
        String userId = UserThreadContext.getUserId();
        //获取游戏关卡信息
        MarchCheckpoint checkpoint = marchCheckpointService.getById(checkpointId);

        //校验关卡是否存在
        ApiAssert.notNullResource(checkpoint);

        //获取游戏关卡信息
        March gameServiceById = marchService.getById(checkpoint.getMarchId());
        ApiAssert.notNullResource(gameServiceById);

        //分页查询

        PageInfo<TaskApiListDTO> selectPageInfo = PageMethod.startPage(baseEntity.getPageNo(), baseEntity.getPageSize())
            .doSelectPageInfo(() -> marchCheckpointService.getMarchTaskList(checkpointId, userId));

        //是否能访问
        int canView = gameServiceById.getEndTime().after(new Date()) ? MarchConstants.MARCH_CAN_VIEW
            : MarchConstants.MARCH_CAN_NOT_VIEW;

        selectPageInfo.getList().forEach(taskApiListDTO -> {
            //补充字段
            taskApiListDTO.setCanView(canView);
            //如果任务有前置任务
            if (StringUtils.isNotBlank(taskApiListDTO.getPreTaskId())) {
                LambdaQueryWrapper<MarchTaskRecord> preTaskQueryWrapper = new LambdaQueryWrapper<>();
                preTaskQueryWrapper.eq(MarchTaskRecord::getCheckpointId, checkpointId);
                preTaskQueryWrapper.eq(MarchTaskRecord::getUserId, userId);
                preTaskQueryWrapper.eq(MarchTaskRecord::getTaskId, taskApiListDTO.getPreTaskId());

                //获得该前置任务用户参与记录
                MarchTaskRecord preTaskRecord = marchTaskRecordService.getOne(preTaskQueryWrapper);
                //设置前置任务完成状态
                if (preTaskRecord == null || preTaskRecord.getResult() == null) {
                    taskApiListDTO.setPreTaskResult(MarchConstants.PRE_TASK_RESULT);
                } else {
                    taskApiListDTO.setPreTaskResult(preTaskRecord.getResult());
                }

                //如果任务类型为作业 设置作业相关
                if (TaskTypeEnum.HOMEWORK.getCode().equals(taskApiListDTO.getType())) {
                    //获取用户作业提交记录
                    LambdaQueryWrapper<MarchHomeworkSubmitRecord> marchHomeworkSubmitRecordWrapper = new LambdaQueryWrapper<>();
                    marchHomeworkSubmitRecordWrapper.eq(MarchHomeworkSubmitRecord::getHomeworkId,
                        taskApiListDTO.getPreTaskId());
                    marchHomeworkSubmitRecordWrapper.eq(MarchHomeworkSubmitRecord::getCreateBy, userId);
                    MarchHomeworkSubmitRecord marchHomeworkSubmitRecord = homeworkSubmitRecordMarchService.getOne(
                        marchHomeworkSubmitRecordWrapper);
                    if (marchHomeworkSubmitRecord != null) {
                        taskApiListDTO.setReviewStatus(marchHomeworkSubmitRecord.getReviewStatus() != null
                            ? MarchConstants.HOMEWORK_REVIEWED : MarchConstants.HOMEWORK_UNREVIEWED);
                        taskApiListDTO.setHomeWorkScore(marchHomeworkSubmitRecord.getScore());
                        taskApiListDTO.setPreTaskResult(1);
                    }

                }

                // 课程
                // 考试
                // 调研
            }

            //如果任务类型为作业 设置作业相关
            if (TaskTypeEnum.HOMEWORK.getCode().equals(taskApiListDTO.getType())) {
                //获取用户作业提交记录
                LambdaQueryWrapper<MarchHomeworkSubmitRecord> marchHomeworkSubmitRecordWrapper = new LambdaQueryWrapper<>();
                marchHomeworkSubmitRecordWrapper.eq(MarchHomeworkSubmitRecord::getHomeworkId, taskApiListDTO.getId());
                marchHomeworkSubmitRecordWrapper.eq(MarchHomeworkSubmitRecord::getCreateBy, userId);
                MarchHomeworkSubmitRecord marchHomeworkSubmitRecord = homeworkSubmitRecordMarchService.getOne(
                    marchHomeworkSubmitRecordWrapper);
                if (marchHomeworkSubmitRecord != null) {
                    taskApiListDTO.setReviewStatus(
                        marchHomeworkSubmitRecord.getReviewStatus() != null ? MarchConstants.HOMEWORK_REVIEWED
                            : MarchConstants.HOMEWORK_UNREVIEWED);
                    taskApiListDTO.setHomeWorkScore(marchHomeworkSubmitRecord.getScore());
                    taskApiListDTO.setResult("1");
                }

            }
        });

        return selectPageInfo;
    }

    /**
     * 获取图片地址
     *
     * @param stylesTemplate {@link StylesTemplate}
     * @return 图片地址
     */
    private String getImgUrl(StylesTemplate stylesTemplate) {
        if (Objects.equals(1, stylesTemplate.getImgUpdate())) {
            return fileFeign.getImageUrl(stylesTemplate.getId(), ImageBizType.MARCH_GAME_STYLES_TEMPLATE.name());
        } else {
            return fileFeign.getFileUrl(stylesTemplate.getImgUrl());
        }
    }

    /**
     * 构造游戏图片
     *
     * @param marchId     游戏id
     * @param marchItemVo {@link CheckpointApiListDTO}
     */
    private void buildEmigratedImg(String marchId, CheckpointApiListDTO marchItemVo) {
        LambdaQueryWrapper<StylesTemplate> templateLambdaQueryWrapper = new LambdaQueryWrapper<>();
        templateLambdaQueryWrapper.eq(StylesTemplate::getMarchId, marchId);
        List<StylesTemplate> marchStylesTemplates = marchStylesTemplateService.list(templateLambdaQueryWrapper);

        for (StylesTemplate marchStylesTemplate : marchStylesTemplates) {
            // 获取图片信息
            MarchStylesTemplateEnum.MarchStyleCodeEnum styleCodeEnum = MarchStylesTemplateEnum.MarchStyleCodeEnum.getStyleCodeEnumBy(
                marchStylesTemplate.getStyleCode());
            switch (Objects.requireNonNull(styleCodeEnum)) {
                case STYLE_CODE_1:
                    marchItemVo.setCheckpointOngoingImg(getImgUrl(marchStylesTemplate));
                    break;
                case STYLE_CODE_2:
                    marchItemVo.setCheckpointFinishImg(getImgUrl(marchStylesTemplate));
                    break;
                case STYLE_CODE_3:
                    marchItemVo.setCheckpointLockImg(getImgUrl(marchStylesTemplate));
                    break;
                case STYLE_CODE_4:
                    marchItemVo.setMobileBottomMapImg(getImgUrl(marchStylesTemplate));
                    break;
                case STYLE_CODE_5:
                    marchItemVo.setMobileMapImg(getImgUrl(marchStylesTemplate));
                    break;
                case STYLE_CODE_6:
                    marchItemVo.setMobileWithoutDataMapImg(getImgUrl(marchStylesTemplate));
                    break;
                case STYLE_CODE_7:
                    marchItemVo.setPcBottomMapImg(getImgUrl(marchStylesTemplate));
                    break;
                case STYLE_CODE_8:
                    marchItemVo.setPcMapImg(getImgUrl(marchStylesTemplate));
                    break;
                case STYLE_CODE_9:
                    marchItemVo.setPcWithoutDataMapImg(getImgUrl(marchStylesTemplate));
                    break;
                default:
            }
        }
    }

    @Override
    public void exportData(CheckpointQueryDTO checkpointQueryDTO) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IMarchCheckpointBiz, CheckpointListDTO>(
            checkpointQueryDTO) {

            @Override
            protected IMarchCheckpointBiz getBean() {
                return SpringUtil.getBean("marchCheckpointBiz", IMarchCheckpointBiz.class);
            }

            @Override
            protected PageInfo<CheckpointListDTO> getPageInfo() {
                return getBean().list((CheckpointQueryDTO) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.MarchCheckpoint;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.MarchCheckpoint.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

}
