package com.wunding.learn.march.service.admin.rest;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.march.MarchErrorNoEnum;
import com.wunding.learn.common.exception.handler.ApiAssert;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import com.wunding.learn.march.service.admin.dto.CheckpointQueryDTO;
import com.wunding.learn.march.service.admin.dto.StylesTemplateListDTO;
import com.wunding.learn.march.service.admin.dto.StylesTemplateSaveDTO;
import com.wunding.learn.march.service.biz.IMarchStylesTemplateBiz;
import com.wunding.learn.march.service.model.MarchStatistical;
import com.wunding.learn.march.service.model.StylesTemplate;
import com.wunding.learn.march.service.service.IMarchService;
import com.wunding.learn.march.service.service.IMarchStatisticalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.hibernate.validator.constraints.Length;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>  游戏样式模板表 前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">liyihui</a>
 * @since 2022-08-19
 */

@Tag(description = "游戏样式模板管理", name = "StylesTemplateRest")
@Validated
@RestController
@RequestMapping("${module.march.contentPath:/}stylesTemplate")
public class MarchStylesTemplateRest {

    @Resource
    private IMarchStylesTemplateBiz marchStylesTemplateBiz;
    @Resource(name = "marchStatisticalService")
    private IMarchStatisticalService marchStatisticalService;
    @Resource
    private IMarchService marchService;

    @GetMapping("/{marchId}/templates")
    @Operation(operationId = "list_StylesTemplateRest", summary = "获取模板列表", description = "获取模板列表")
    public Result<PageInfo<StylesTemplateListDTO>> list(
        @Parameter(description = "游戏id") @PathVariable("marchId") @Length(max = 36, message = "游戏id长度不能超过36") String marchId,
        @ParameterObject BaseEntity baseEntity) {
        CheckpointQueryDTO checkpointQueryDTO = new CheckpointQueryDTO();
        BeanUtils.copyProperties(baseEntity, checkpointQueryDTO);
        checkpointQueryDTO.setMarchId(marchId);
        marchService.checkMarchId(marchId);
        return Result.success(marchStylesTemplateBiz.list(checkpointQueryDTO));
    }

    @GetMapping("/{marchId}/haveTemplate")
    @Operation(operationId = "haveTemplate_StylesTemplateRest", summary = "检测该游戏下是否有模板", description = "检测该游戏下是否有模板")
    public Result<Boolean> haveTemplate(
        @Parameter(description = "游戏id") @PathVariable("marchId") @Length(max = 36, message = "游戏id长度不能超过36") String marchId) {
        marchService.checkMarchId(marchId);
        return Result.success(marchStylesTemplateBiz.haveTemplate(marchId));
    }

    @GetMapping("/{marchId}/styleValue")
    @Operation(operationId = "haveTemplate_StylesTemplateRest", summary = "检测该游戏下的模板样式 默认为1", description = "检测该游戏下的模板样式 默认为1")
    public Result<Integer> styleValue(
        @Parameter(description = "游戏id") @PathVariable("marchId") @Length(max = 36, message = "游戏id长度不能超过36") String marchId) {
        marchService.checkMarchId(marchId);
        int styleValue = 1;
        List<StylesTemplate> stylesTemplateList = marchStylesTemplateBiz.styleValue(marchId);
        if (!CollectionUtils.isEmpty(stylesTemplateList)) {
            styleValue = stylesTemplateList.get(0).getImgCode();
        }
        return Result.success(styleValue);
    }

    @GetMapping("/{marchId}/haveUserJoin")
    @Operation(hidden = true, operationId = "haveUserJoin_StylesTemplateRest", summary = "检测该游戏下是否有用户参加记录", description = "检测该游戏下是否有用户参加记录")
    public Result<Boolean> haveUserJoin(
        @Parameter(description = "游戏id") @PathVariable("marchId") @Length(max = 36, message = "游戏id长度不能超过36") String
            marchId) {
        marchService.checkMarchId(marchId);
        return Result.success(
            marchStatisticalService.count(
                new LambdaQueryWrapper<MarchStatistical>()
                    .eq(MarchStatistical::getMarchId, marchId)) > 0);
    }

    @PutMapping("/{marchId}/template")
    @Operation(operationId = "update_StylesTemplateRest", summary = "修改模板自定义图片", description = "修改模板自定义图片")
    public Result<Boolean> update(
        @Parameter(description = "游戏id") @PathVariable("marchId") @Length(max = 36, message = "游戏id长度不能超过36") String
            marchId,
        @Valid @RequestBody StylesTemplateSaveDTO stylesTemplateSaveDTO) {
        ApiAssert.notNullParams(stylesTemplateSaveDTO.getId(), MarchErrorNoEnum.ERR_TEMPLATE_ID_NULL);
        marchService.checkMarchId(marchId);
        return Result.success(marchStylesTemplateBiz.update(stylesTemplateSaveDTO));
    }

    @PutMapping("/{marchId}/updateStyleTemplate")
    @Operation(operationId = "update_StylesTemplateRest", summary = "整体更换模板", description = "整体更换模板")
    public Result<Void> updateStyleTemplate(
        @Parameter(description = "游戏id") @PathVariable("marchId") @Length(max = 36, message = "游戏id长度不能超过36") String
            marchId,
        @Parameter(description = "模板编码 1:样式1 2: 样式2 3:样式3") @RequestParam(value = "templateCode", required = true) int templateCode) {
        marchService.checkMarchId(marchId);
        marchStylesTemplateBiz.updateStyleTemplate(marchId, templateCode);
        return Result.success();
    }

    @PostMapping("/{marchId}/exportData")
    @Operation(operationId = "exportData_StylesTemplateRest", summary = "导出样式管理列表", description = "导出样式管理列表")
    public Result<ExportResultDTO> exportData(
        @Parameter(description = "游戏id") @PathVariable("marchId") @Length(max = 36, message = "游戏id长度不能超过36") String marchId,
        @ParameterObject BaseEntity baseEntity) {
        CheckpointQueryDTO checkpointQueryDTO = new CheckpointQueryDTO();
        BeanUtils.copyProperties(baseEntity, checkpointQueryDTO);
        checkpointQueryDTO.setMarchId(marchId);
        marchStylesTemplateBiz.exportData(checkpointQueryDTO);
        return Result.success();
    }

}
