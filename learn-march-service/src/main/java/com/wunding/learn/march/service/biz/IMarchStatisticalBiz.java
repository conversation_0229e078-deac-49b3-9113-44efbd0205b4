package com.wunding.learn.march.service.biz;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.march.service.admin.query.MarchRankTeamQuery;
import com.wunding.learn.march.service.admin.query.MarchRankUserQuery;
import com.wunding.learn.march.service.admin.query.MarchStatisticsQuery;
import com.wunding.learn.march.service.client.dto.CheckpointStatisticsDTO;
import com.wunding.learn.march.service.client.dto.MarchRankPersonalDTO;
import com.wunding.learn.march.service.client.dto.MarchRankTeamListDTO;
import com.wunding.learn.march.service.client.dto.MarchRankUserListDTO;
import com.wunding.learn.march.service.client.dto.MileageDetailStatisticsDTO;
import java.util.List;

/**
 * 游戏统计服务
 *
 * <AUTHOR>
 * @date 2022/2/18 15:05
 */
public interface IMarchStatisticalBiz {

    /**
     * 获取当前用户的排行榜总数据
     *
     * @param marchId 游戏id
     * @return Result<EmigratedRankPersonalVo>
     */
    MarchRankPersonalDTO getMarchUser(String marchId);

    /**
     * 学员端获取排行榜个人分页数据
     *
     * @param pageNo   第几页
     * @param pageSize 每页大小
     * @param marchId  游戏id
     * @return Result<PageModel < EmigratedRankUserListVo>>
     */
    PageInfo<MarchRankUserListDTO> getMarchRankItemList(int pageNo, int pageSize,
        String marchId, String teamId);

    /**
     * 管理端获取排行榜个人分页数据
     *
     * @param query   个人排行榜查询对象
     * @return Result<PageModel < EmigratedRankUserListVo>>
     */
    PageInfo<MarchRankUserListDTO> getMarchRankItemList(MarchRankUserQuery query);

    /**
     * 管理端获取排行榜团队分页数据
     *
     * @param query   团队排行榜查询对象
     * @return Result<PageModel < MarchRankTeamListDTO>>
     */
    PageInfo<MarchRankTeamListDTO> getMarchRankTeamItemList(MarchRankTeamQuery query);

    /**
     * 学员端获取排行榜团队分页数据
     *
     * @param pageNo   第几页
     * @param pageSize 每页大小
     * @param marchId  游戏id
     * @return Result<PageModel < MarchRankTeamListDTO>>
     */
    PageInfo<MarchRankTeamListDTO> getMarchRankTeamItemList(int pageNo, int pageSize,
        String marchId);

    /**
     * 关卡统计
     * @param query 统计查询对象
     * @return Result<PageModel < CheckpointStatisticsDTO>>
     */
    PageInfo<CheckpointStatisticsDTO> getCheckpointStatisticalList(MarchStatisticsQuery query);

    /**
     * 明细统计
     * @param query 统计查询对象
     * @return
     */
    PageInfo<MileageDetailStatisticsDTO> getMileageDetailStatisticsList(MarchStatisticsQuery query);

    /**
     * 个人统计
     * @param query 统计查询对象
     * @return
     */
    PageInfo<MileageDetailStatisticsDTO> getPersonalStatisticsList(MarchStatisticsQuery query);

    /**
     * 个人排行榜
     * @param marchId
     * @return
     */
    List<MileageDetailStatisticsDTO> getMarchRankPersonList(String marchId);

    /**
     * 获取用户个人排名
     * @param marchId
     * @return
     */
    Integer getMyRank(String marchId);
}
