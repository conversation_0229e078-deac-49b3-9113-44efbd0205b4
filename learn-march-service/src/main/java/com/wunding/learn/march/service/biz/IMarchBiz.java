package com.wunding.learn.march.service.biz;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.AvailableDTO;
import com.wunding.learn.march.service.admin.dto.MarchDTO;
import com.wunding.learn.march.service.admin.dto.MarchListDTO;
import com.wunding.learn.march.service.admin.dto.MarchQueryDTO;
import com.wunding.learn.march.service.admin.dto.MarchSaveDTO;
import com.wunding.learn.march.service.client.dto.CheckCompletionDTO;
import com.wunding.learn.march.service.client.dto.MarchApiListDTO;
import com.wunding.learn.march.service.client.dto.MarchApiQueryDTO;
import com.wunding.learn.march.service.client.dto.MarchDetailDTO;
import com.wunding.learn.march.service.client.dto.MarchUserInfoDTO;
import com.wunding.learn.march.service.client.query.MarchSearchQuery;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 游戏管理业务层
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-06-15
 */
public interface IMarchBiz {

    /**
     * 获取游戏列表
     *
     * @param marchQueryDTO
     * @return
     */
    PageInfo<MarchListDTO> list(MarchQueryDTO marchQueryDTO);

    /**
     * 新增游戏
     *
     * @param marchSaveDTO
     * @return
     */
    String create(MarchSaveDTO marchSaveDTO);

    /**
     * 更新游戏
     *
     * @param marchSaveDTO
     * @return
     */
    Boolean update(MarchSaveDTO marchSaveDTO);

    /**
     * 删除游戏
     *
     * @param ids
     * @return
     */
    void remove(String ids);

    /**
     * 获取游戏详情
     *
     * @param id
     * @return
     */
    MarchDTO getMarch(String id);

    /**
     * 获取游戏应用
     *
     * @param id
     * @return
     */
    String getMarchType(String id);

    /**
     * 发布/取消发布游戏
     *
     * @param availableDTO
     * @return
     */
    void available(AvailableDTO availableDTO);

    /**
     * 获取游戏列表
     *
     * @param marchApiQueryDTO
     * @return
     */
    PageInfo<MarchApiListDTO> getMarchList(MarchApiQueryDTO marchApiQueryDTO);

    /**
     * 客户端获取游戏详情
     *
     * @param id           游戏id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     * @return {@link MarchDetailDTO}
     */
    MarchDetailDTO getMarchDetail(String id, Integer isIgnoreView);

    /**
     * 判断当前人的角色
     *
     * @param marchId
     * @return 0-无权访问 1-可以访问
     */
    Integer chargeCurrentUserRole(String marchId);

    /**
     * 检查完成情况接口，返回任务完成情况，关卡完成情况，游戏完成情况
     *
     * @param id   id
     * @param type 类型
     * @return {@link CheckCompletionDTO }
     */
    CheckCompletionDTO checkCompletion(String id, int type);

    /**
     * 导出游戏管理列表的数据
     *
     * @param marchQueryDTO 查询对象
     * @return
     */
    @Async
    void export(MarchQueryDTO marchQueryDTO);


    /**
     * 搜索游戏
     *
     * @param marchSearchQuery
     * @return
     */
    PageInfo<MarchApiListDTO> searchMarch(MarchSearchQuery marchSearchQuery);

    /**
     * 获取游戏个人信息
     * @param id
     * @return
     */
    MarchUserInfoDTO getMarchUserInfo(String id);

}
