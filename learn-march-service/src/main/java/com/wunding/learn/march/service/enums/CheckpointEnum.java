package com.wunding.learn.march.service.enums;

/**
 * <AUTHOR>
 * @title: EmigratedCheckpointEnum
 * @projectName: learn
 * @description： 游戏关卡信息
 * @date 2022/2/16 13:24
 */
public enum CheckpointEnum {
    LOCKED(0),

    UNDERWAY(1),

    PASS(2);

    /**
     * 关卡状态 0-锁定;1-进行中; 2-关卡完成
     */
    private final int state;

    CheckpointEnum(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }
}
