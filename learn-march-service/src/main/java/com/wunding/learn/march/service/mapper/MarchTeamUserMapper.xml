<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.march.service.mapper.MarchTeamUserMapper">

    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.march.service.mapper.MarchTeamUserMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.march.service.model.MarchTeamUser">
        <!--@Table march_team_user-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="march_id" jdbcType="VARCHAR"
          property="marchId"/>
        <result column="team_id" jdbcType="VARCHAR"
          property="teamId"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, march_id, team_id, user_id, create_by, create_time, update_by, update_time, is_del
    </sql>

    <select id="getUserTeamNameMap" resultType="com.wunding.learn.march.service.admin.dto.TeamUserListDTO" useCache="false">
        select
            mtu.user_id as userId,
            mt.name as teamName
        from
            march_team mt
                inner join march_team_user mtu on mt.id = mtu.team_id
        where mt.march_id = #{marchId}
          and mt.is_del = 0
          and mtu.is_del = 0
          and user_id in
        <foreach collection="userIds" item="item" separator="," open="(" close=")">
                #{item}
        </foreach>
    </select>

    <select id="selectUserList" resultType="com.wunding.learn.march.service.model.MarchTeamUser" useCache="false">
        select
            *
        from
        march_team mt
        inner join march_team_user mtu on mt.id = mtu.team_id
        left join sys_user su on su.id = mtu.user_id
        where mt.id = #{teamId}
        and mt.is_del = 0
        and su.is_del = 0
        and mtu.is_del = 0
          <if test="userIdList != null and userIdList.size() != 0">
              and mtu.user_id in
              <foreach collection="userIdList" item="item" separator="," open="(" close=")">
                  #{item}
              </foreach>
          </if>
    </select>


</mapper>
