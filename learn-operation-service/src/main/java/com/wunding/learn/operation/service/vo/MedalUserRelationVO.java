package com.wunding.learn.operation.service.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/3/2 10:09
 */
@Data
@Schema(name = "MedalUserRelationVO", description = "勋章用户关系视图对象")
public class MedalUserRelationVO {

    @Schema(description = "勋章用户关系id")
    private String id;

    @Schema(description = "勋章名称")
    private String medalName;

    @Schema(description = "用户名")
    private String userId;

    @Schema(description = "用户名")
    private String fullName;

    @Schema(description = "账号")
    private String empNo;

    @Schema(description = "部门名称")
    private String orgName;

    @Schema(description = "颁奖日期")
    private Date awardTime;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;

    @Schema(description = "部门id", hidden = true)
    private String orgId;

}
