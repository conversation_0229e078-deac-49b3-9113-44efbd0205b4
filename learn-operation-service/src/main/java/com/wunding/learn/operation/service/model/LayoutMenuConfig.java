package com.wunding.learn.operation.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 板块菜单配置
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2023-03-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
        @Accessors(chain = true)
@TableName("layout_menu_config")
@Schema(name = "LayoutMenuConfig对象", description = "板块菜单配置")
public class LayoutMenuConfig implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 板块配置表layout_config主键id
     */
    @Schema(description = "板块配置表layout_config主键id")
    @TableField("config_id")
    private String configId;


    /**
     * 栏目类型[INDEX-首页,NOTICE-公告,COURSE-课程,SCHEDULE-日程,ACTIVITY-活动,CLASS-班级,PHOTO-照片墙,DATUM-资料,CASE-案例,TOPIC-话题,RANK-排名,LECTURER-讲师]
     */
    @Schema(description = "栏目类型[INDEX-首页,NOTICE-公告,COURSE-课程,SCHEDULE-日程,ACTIVITY-活动,CLASS-班级,PHOTO-照片墙,DATUM-资料,CASE-案例,TOPIC-话题,RANK-排名,LECTURER-讲师]")
    @TableField("menu_type")
    private String menuType;


    /**
     * 栏目名称
     */
    @Schema(description = "栏目名称")
    @TableField("menu_name")
    private String menuName;


    /**
     * 序号
     */
    @Schema(description = "序号")
    @TableField("sort_no")
    private Integer sortNo;

    /**
     * 是否展示会员标记(0为否,1为是,默认0)
     */
    @Schema(description = "是否展示会员标记(0为否,1为是,默认0)")
    @TableField("is_member")
    private Integer isMember;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 更新用户
     */
    @Schema(description = "更新用户")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


}
