package com.wunding.learn.sync.service.model.lnns;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 组织单元基本信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("d_hrm_tb_org_orgunit")
@Schema(name = "Orgunit对象", description = "组织单元基本信息")
public class OrgUnit implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "c_oid", type = IdType.INPUT)
    private String cOid;


    /**
     * 源系统
     */
    @Schema(description = "源系统")
    @TableField("src_stm")
    private String srcStm;


    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    @TableField("c_operate_time")
    private Date cOperateTime;


    /**
     * 操作员编号
     */
    @Schema(description = "操作员编号")
    @TableField("c_operator")
    private String cOperator;


    /**
     * 业务单元
     */
    @Schema(description = "业务单元")
    @TableField("c_setid")
    private String cSetid;


    /**
     * 机构类型
     */
    @Schema(description = "机构类型")
    @TableField("c_type")
    private String cType;


    /**
     * 编码
     */
    @Schema(description = "编码")
    @TableField("c_code")
    private String cCode;


    /**
     * 名称
     */
    @Schema(description = "名称")
    @TableField("c_name")
    private String cName;


    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    @TableField("c_est_date")
    private Date cEstDate;


    /**
     * 层级
     */
    @Schema(description = "层级")
    @TableField("c_level")
    private String cLevel;


    /**
     * 电话
     */
    @Schema(description = "电话")
    @TableField("c_contact_tel")
    private String cContactTel;


    /**
     * 传真
     */
    @Schema(description = "传真")
    @TableField("c_fax")
    private String cFax;


    /**
     * 地点
     */
    @Schema(description = "地点")
    @TableField("c_location_hid")
    private String cLocationHid;


    /**
     * 地址
     */
    @Schema(description = "地址")
    @TableField("c_address")
    private String cAddress;


    /**
     * 法人代表
     */
    @Schema(description = "法人代表")
    @TableField("c_representative")
    private String cRepresentative;


    /**
     * 批准文号
     */
    @Schema(description = "批准文号")
    @TableField("c_approval_dcmtno")
    private String cApprovalDcmtno;


    /**
     * 登记批准机构
     */
    @Schema(description = "登记批准机构")
    @TableField("c_approving_company")
    private String cApprovingCompany;


    /**
     * 投资范围
     */
    @Schema(description = "投资范围")
    @TableField("c_invest_type")
    private String cInvestType;


    /**
     * 编制性质
     */
    @Schema(description = "编制性质")
    @TableField("c_workforce_property")
    private String cWorkforceProperty;


    /**
     * 所属行业
     */
    @Schema(description = "所属行业")
    @TableField("c_industry")
    private String cIndustry;


    /**
     * 经济类型
     */
    @Schema(description = "经济类型")
    @TableField("c_econ_type")
    private String cEconType;


    /**
     * 业务区域
     */
    @Schema(description = "业务区域")
    @TableField("c_biz_area")
    private String cBizArea;


    /**
     * 状态
     */
    @Schema(description = "状态")
    @TableField("c_status")
    private String cStatus;


    /**
     * 生效日期
     */
    @Schema(description = "生效日期")
    @TableField("c_begin_date")
    private Date cBeginDate;


    /**
     * 失效日期
     */
    @Schema(description = "失效日期")
    @TableField("c_end_date")
    private Date cEndDate;


    /**
     * HID
     */
    @Schema(description = "HID")
    @TableField("c_hid")
    private String cHid;


    /**
     * 邮编
     */
    @Schema(description = "邮编")
    @TableField("c_post_code")
    private String cPostCode;


    /**
     * 网址
     */
    @Schema(description = "网址")
    @TableField("c_website")
    private String cWebsite;


    /**
     * 机构简介
     */
    @Schema(description = "机构简介")
    @TableField("c_introduction")
    private String cIntroduction;


    /**
     * 是否虚拟部门
     */
    @Schema(description = "是否虚拟部门")
    @TableField("c_virtual_dept")
    private String cVirtualDept;


    /**
     * 部门职责
     */
    @Schema(description = "部门职责")
    @TableField("c_dept_resp")
    private String cDeptResp;


    /**
     * 部门负责人
     */
    @Schema(description = "部门负责人")
    @TableField("c_pic_id")
    private String cPicId;


    /**
     * 部门负责人类型
     */
    @Schema(description = "部门负责人类型")
    @TableField("c_pic_type")
    private String cPicType;


    /**
     * 是否根节点
     */
    @Schema(description = "是否根节点")
    @TableField("c_root")
    private String cRoot;


    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    @TableField("c_biz_type")
    private String cBizType;


    /**
     * 行政区划
     */
    @Schema(description = "行政区划")
    @TableField("c_administration_area")
    private String cAdministrationArea;


    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    @TableField("c_regcapital")
    private String cRegcapital;


    /**
     * 缩写
     */
    @Schema(description = "缩写")
    @TableField("c_abbreviate")
    private String cAbbreviate;


    /**
     * 文件上传
     */
    @Schema(description = "文件上传")
    @TableField("c_filetest")
    private String cFiletest;


    /**
     * 备注
     */
    @Schema(description = "备注")
    @TableField("c_remark")
    private String cRemark;


    /**
     * 所属公司
     */
    @Schema(description = "所属公司")
    @TableField("c_affiliate_company")
    private String cAffiliateCompany;


    /**
     * 职能属性
     */
    @Schema(description = "职能属性")
    @TableField("c_org_property")
    private String cOrgProperty;


    /**
     * 全路径
     */
    @Schema(description = "全路径")
    @TableField("c_full_name")
    private String cFullName;


    /**
     * 英文全称
     */
    @Schema(description = "英文全称")
    @TableField("c_english_name")
    private String cEnglishName;


    /**
     * 异地机构
     */
    @Schema(description = "异地机构")
    @TableField("c_remode_org")
    private String cRemodeOrg;


    /**
     * 地域属性
     */
    @Schema(description = "地域属性")
    @TableField("c_region_org")
    private String cRegionOrg;


    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    @TableField("c_nature_business")
    private String cNatureBusiness;


    /**
     * 工作总结
     */
    @Schema(description = "工作总结")
    @TableField("c_work_file")
    private String cWorkFile;


    /**
     * 核心机构号
     */
    @Schema(description = "核心机构号")
    @TableField("c_core_org_number")
    private String cCoreOrgNumber;


    /**
     * 统一信用代码
     */
    @Schema(description = "统一信用代码")
    @TableField("c_code_credit")
    private String cCodeCredit;


    /**
     * 换发证日期
     */
    @Schema(description = "换发证日期")
    @TableField("c_chang_card_date")
    private Date cChangCardDate;


    /**
     * 批准成立日期
     */
    @Schema(description = "批准成立日期")
    @TableField("c_approved_date")
    private Date cApprovedDate;


    /**
     * 金融许可证机构编码
     */
    @Schema(description = "金融许可证机构编码")
    @TableField("c_license_code")
    private String cLicenseCode;


    /**
     * 监管机构
     */
    @Schema(description = "监管机构")
    @TableField("c_regulators")
    private String cRegulators;


    /**
     * 机构类别
     */
    @Schema(description = "机构类别")
    @TableField("c_org_type")
    private String cOrgType;


    /**
     * 部门类别
     */
    @Schema(description = "部门类别")
    @TableField("c_dep_type")
    private String cDepType;


    /**
     * 分支机构类型
     */
    @Schema(description = "分支机构类型")
    @TableField("c_branch_type")
    private String cBranchType;


    /**
     * 支行级别
     */
    @Schema(description = "支行级别")
    @TableField("c_dep_level")
    private String cDepLevel;


    /**
     * 前中后台（职能方向）
     */
    @Schema(description = "前中后台（职能方向）")
    @TableField("c_qzht_function")
    private String cQzhtFunction;


    /**
     * 分管领导
     */
    @Schema(description = "分管领导")
    @TableField("c_dep_change_lader")
    private String cDepChangeLader;


    /**
     * 是否机关编制部门
     */
    @Schema(description = "是否机关编制部门")
    @TableField("c_dep_establishment")
    private String cDepEstablishment;


    /**
     * 业务条线
     */
    @Schema(description = "业务条线")
    @TableField("c_business_line")
    private String cBusinessLine;


    /**
     * 原系统组织类型
     */
    @Schema(description = "原系统组织类型")
    @TableField("c_old_type")
    private String cOldType;


    /**
     * 原系统组织编码
     */
    @Schema(description = "原系统组织编码")
    @TableField("c_old_code")
    private String cOldCode;


    /**
     * ODS日期
     */
    @Schema(description = "ODS日期")
    @TableField("ods_dt")
    private String odsDt;


    /**
     * 分区日期
     */
    @Schema(description = "分区日期")
    @TableField("partition_dt")
    private Date partitionDt;


    /**
     * 数据插入日期
     */
    @Schema(description = "数据插入日期")
    @TableField(value = "insert_date")
    private Date insertDate;


}
