package com.wunding.learn.sync.service.model.lnns;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 组织信息表
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hrm_org")
@Schema(name = "HrmOrg对象", description = "组织信息表")
public class HrmOrg implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    @TableField("c_operate_time")
    private Date cOperateTime;


    /**
     * 组织名称
     */
    @Schema(description = "组织名称")
    @TableField("c_name")
    private String cName;


    /**
     * 组织编码
     */
    @Schema(description = "组织编码")
    @TableField("c_code")
    private String cCode;


    /**
     * 组织HID
     */
    @Schema(description = "组织HID")
    @TableId(value = "c_hid", type = IdType.INPUT)
    private String cHid;


    /**
     * 在职类型 0：公司，1：部门
     */
    @Schema(description = "在职类型 0：公司，1：部门")
    @TableField("c_type")
    private String cType;


    /**
     * 上级组织HID
     */
    @Schema(description = "上级组织HID")
    @TableField("c_superior_hid")
    private String cSuperiorHid;


    /**
     * 组织全路径（从根节点开始逐层HID拼接）
     */
    @Schema(description = "组织全路径（从根节点开始逐层HID拼接）")
    @TableField("c_path_code")
    private String cPathCode;


    /**
     * 有效状态 0：无效，1：有效
     */
    @Schema(description = "有效状态 0：无效，1：有效")
    @TableField("c_status")
    private Integer cStatus;


    /**
     * 数据插入日期
     */
    @Schema(description = "数据插入日期")
    @TableField(value = "insert_date")
    private Date insertDate;


}
