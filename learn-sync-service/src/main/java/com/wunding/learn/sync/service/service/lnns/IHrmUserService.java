package com.wunding.learn.sync.service.service.lnns;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.sync.service.model.lnns.HrmUser;
import com.wunding.learn.user.api.dto.sync.SyncUserDTO;
import java.util.List;

/**
 * <p> 员工基本信息 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-05-16
 */
public interface IEmpService extends IService<HrmUser> {


    /**
     * 查询同步用户dto
     *
     * @return {@link List }<{@link SyncUserDTO }>
     */
    List<SyncUserDTO> querySyncUserDTO();


}
