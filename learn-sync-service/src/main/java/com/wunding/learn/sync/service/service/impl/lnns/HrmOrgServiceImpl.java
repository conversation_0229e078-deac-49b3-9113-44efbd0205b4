package com.wunding.learn.sync.service.service.impl.lnns;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.sync.service.mapper.lnns.HrmOrgMapper;
import com.wunding.learn.sync.service.model.lnns.HrmOrg;
import com.wunding.learn.sync.service.service.lnns.IHrmOrgService;
import com.wunding.learn.user.api.dto.sync.SyncOrgDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 组织单元基本信息 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-05-16
 */
@Slf4j
@Service("hrmOrgService")
public class HrmOrgServiceImpl extends ServiceImpl<HrmOrgMapper, HrmOrg> implements IHrmOrgService {

    @Override
    public List<SyncOrgDTO> querySyncOrgDTO() {
        return baseMapper.querySyncOrgDTO();
    }

    @Override
    public SyncOrgDTO queryRootSyncOrgDTO() {
        return baseMapper.queryRootSyncOrgDTO();
    }
}
