package com.wunding.learn.sync.service.mapper.lnns;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.user.api.dto.sync.SyncOrgDTO;
import com.wunding.learn.user.service.model.HrmOrg;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 组织单元基本信息 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-05-16
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface OrgUnitMapper extends BaseMapper<HrmOrg> {

    /**
     * 查询组织数据
     *
     * @return {@link List }<{@link SyncOrgDTO }>
     */
    List<SyncOrgDTO> querySyncOrgDTO();

    /**
     * 查询根组织组织数据
     *
     * @return {@link List }<{@link SyncOrgDTO }>
     */
    SyncOrgDTO queryRootSyncOrgDTO();

}
