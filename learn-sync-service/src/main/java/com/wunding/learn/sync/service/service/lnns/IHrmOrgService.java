package com.wunding.learn.sync.service.service.lnns;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.sync.service.model.lnns.HrmOrg;
import com.wunding.learn.user.api.dto.sync.SyncOrgDTO;
import java.util.List;

/**
 * <p> 组织单元基本信息 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-05-16
 */
public interface IOrgUnitService extends IService<HrmOrg> {


    /**
     * 查询组织数据
     *
     * @return {@link List }<{@link SyncOrgDTO }>
     */
    List<SyncOrgDTO> querySyncOrgDTO();

    /**
     * 查询根组织组织数据
     *
     * @return {@link List }<{@link SyncOrgDTO }>
     */
    SyncOrgDTO queryRootSyncOrgDTO();
}
