package com.wunding.learn.sync.service.model.lnns;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> HRM员工信息表
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hrm_user")
@Schema(name = "HrmUser对象", description = "HRM员工信息表")
public class HrmUser implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 工号/用户名
     */
    @Schema(description = "工号/用户名")
    @TableId(value = "c_employee_code", type = IdType.INPUT)
    private String cEmployeeCode;


    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    @TableField("c_operate_time")
    private Date cOperateTime;


    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @TableField("c_employee_name")
    private String cEmployeeName;


    /**
     * 所属单位HID
     */
    @Schema(description = "所属单位HID")
    @TableField("c_company_hid")
    private String cCompanyHid;


    /**
     * 所属单位
     */
    @Schema(description = "所属单位")
    @TableField("c_company_name")
    private String cCompanyName;


    /**
     * 所属部门HID
     */
    @Schema(description = "所属部门HID")
    @TableField("c_dept_hid")
    private String cDeptHid;


    /**
     * 所属部门
     */
    @Schema(description = "所属部门")
    @TableField("c_dept_name")
    private String cDeptName;


    /**
     * 岗位
     */
    @Schema(description = "岗位")
    @TableField("c_position_name")
    private String cPositionName;


    /**
     * 职务
     */
    @Schema(description = "职务")
    @TableField("c_job_name")
    private String cJobName;


    /**
     * 员工状态,1:未入职,2:在职,3:离职,4:退休
     */
    @Schema(description = "员工状态,1:未入职,2:在职,3:离职,4:退休")
    @TableField("c_employee_status")
    private Integer cEmployeeStatus;


    /**
     * 在职状态,1:临聘在职,101:返聘,11:长病假,12:长伤假,13:长事假,14:脱产学习,15:息工,17:离岗,18:借调结束,19:外派结束,2:在岗,20:兼职,21:其它,25:退出管理岗,3:待岗,4:内退,5:借出,6:借入,7:派出,8:派入
     */
    @Schema(description = "在职状态,1:临聘在职,101:返聘,11:长病假,12:长伤假,13:长事假,14:脱产学习,15:息工,17:离岗,18:借调结束,19:外派结束,2:在岗,20:兼职,21:其它,25:退出管理岗,3:待岗,4:内退,5:借出,6:借入,7:派出,8:派入")
    @TableField("c_active_status")
    private String cActiveStatus;


    /**
     * 编制类型,1:在编在岗,2:在编内退,3:在编其他,4:非编
     */
    @Schema(description = "编制类型,1:在编在岗,2:在编内退,3:在编其他,4:非编")
    @TableField("c_headcount_type")
    private Integer cHeadcountType;


    /**
     * 电话
     */
    @Schema(description = "电话")
    @TableField("c_mobile_tel")
    private String cMobileTel;


    /**
     * 工作邮箱
     */
    @Schema(description = "工作邮箱")
    @TableField("c_business_email")
    private String cBusinessEmail;


    /**
     * 个人邮箱
     */
    @Schema(description = "个人邮箱")
    @TableField("c_per_email")
    private String cPerEmail;


    /**
     * 性别,1:男,2:女,9:未说明
     */
    @Schema(description = "性别,1:男,2:女,9:未说明")
    @TableField("c_gender")
    private Integer cGender;


    /**
     * 出生日期
     */
    @Schema(description = "出生日期")
    @TableField("c_birth_date")
    private Date cBirthDate;


    /**
     * 办公电话
     */
    @Schema(description = "办公电话")
    @TableField("c_office_tel")
    private String cOfficeTel;


    /**
     * 入职时间
     */
    @Schema(description = "入职时间")
    @TableField("c_labor_date")
    private Date cLaborDate;


    /**
     * 用工形式,1:正式工,2:派遣员工,3:短期合同工,4:外系统人员
     */
    @Schema(description = "用工形式,1:正式工,2:派遣员工,3:短期合同工,4:外系统人员")
    @TableField("c_labor_type")
    private Integer cLaborType;


    /**
     * 身份证
     */
    @Schema(description = "身份证")
    @TableField("c_personal_id")
    private String cPersonalId;


    /**
     * 数据插入日期
     */
    @Schema(description = "数据插入日期")
    private Date insertDate;


}
