<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.sync.service.mapper.DataSourceMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.sync.service.mapper.DataSourceMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.sync.service.model.DataSource">
            <!--@Table sync_data_source-->
                    <id column="id" jdbcType="BIGINT" property="id"/>
                    <result column="type" jdbcType="TINYINT"
                            property="type"/>
                    <result column="name" jdbcType="VARCHAR"
                            property="name"/>
                    <result column="description" jdbcType="VARCHAR"
                            property="description"/>
                    <result column="data_type" jdbcType="VARCHAR"
                            property="dataType"/>
                    <result column="resp" jdbcType="LONGVARCHAR"
                            property="resp"/>
                    <result column="path" jdbcType="VARCHAR"
                            property="path"/>
                    <result column="is_del" jdbcType="INTEGER"
                            property="isDel"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, type, name, description, data_type, resp, path, is_del, create_by, create_time, update_by, update_time
        </sql>

</mapper>
