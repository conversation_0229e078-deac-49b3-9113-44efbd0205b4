package com.wunding.learn.sync.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "FTP配置项")
public class FtpConfigDTO {

    @Schema(description = "FTP服务器地址")
    private String host;

    @Schema(description = "FTP服务器端口")
    private Integer port;

    @Schema(description = "FTP用户名")
    private String username;

    @Schema(description = "FTP密码")
    private String password;

    @Schema(description = "FTP文件路径")
    private String filePath;

}
