package com.wunding.learn.sync.service.service.impl.lnns;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.sync.service.mapper.lnns.SyncPostRecordMapper;
import com.wunding.learn.sync.service.model.lnns.SyncPostRecord;
import com.wunding.learn.sync.service.service.lnns.ISyncPostRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 岗位全量表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-25
 */
@Slf4j
@Service("syncPostRecordService")
public class SyncPostRecordServiceImpl extends ServiceImpl<SyncPostRecordMapper, SyncPostRecord> implements
    ISyncPostRecordService {

}
