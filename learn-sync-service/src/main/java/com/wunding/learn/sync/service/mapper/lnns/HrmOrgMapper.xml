<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.sync.service.mapper.lnns.HrmOrgMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        c_operate_time, c_name, c_code, c_hid, c_type, c_superior_hid, c_path_code, c_status, insert_date
        </sql>

    <select id="querySyncOrgDTO" resultType="com.wunding.learn.user.api.dto.sync.SyncOrgDTO" useCache="false">
        SELECT DISTINCT org.c_code AS orgCode,
                        org.c_name AS orgName,
                        parent.c_code AS parentCode,
                        parent.c_name AS parentName,
                        org.c_path_code AS levelPathCode,
                        org.c_name AS levelPathName,
                        CASE
                            WHEN org.c_path_code IS NOT NULL THEN
                                CHAR_LENGTH(org.c_path_code) - CHAR_LENGTH(REPLACE(org.c_path_code, '-', '')) + 1
                            ELSE 1
                            END AS orgLevel,
                        0 AS sortNo,
                        CASE
                            WHEN parent.c_hid IS NULL THEN 1
                            ELSE 0
                            END AS sysDefined,
                        CASE
                            WHEN org.c_status = 1 THEN 1
                            ELSE 0
                            END AS isAvailable,
                        0 AS isDel,
                        CASE
                            WHEN org.c_type = '0' THEN 2
                            WHEN org.c_type = '1' THEN 1
                            ELSE 0
                            END AS dimension,
                        0 AS multiChannelType
        FROM hrm_org org
        LEFT JOIN hrm_org parent ON org.c_superior_hid = parent.c_hid AND parent.c_status = 1 AND parent.insert_date = DATE_FORMAT(curdate(),'%Y-%m-%d')
        WHERE org.c_status = 1
          AND org.insert_date = DATE_FORMAT(curdate(),'%Y-%m-%d')
    </select>

    <select id="queryRootSyncOrgDTO" resultType="com.wunding.learn.user.api.dto.sync.SyncOrgDTO" useCache="false">
        SELECT DISTINCT org.c_code AS orgCode,
                        org.c_name AS orgName,
                        parent.c_code AS parentCode,
                        parent.c_name AS parentName,
                        org.c_path_code AS levelPathCode,
                        org.c_name AS levelPathName,
                        CASE
                            WHEN org.c_path_code IS NOT NULL THEN
                                CHAR_LENGTH(org.c_path_code) - CHAR_LENGTH(REPLACE(org.c_path_code, '/', '')) + 1
                            ELSE 1
                            END AS orgLevel,
                        0 AS sortNo,
                        CASE
                            WHEN parent.c_hid IS NULL THEN 1
                            ELSE 0
                            END AS sysDefined,
                        CASE
                            WHEN org.c_status = 1 THEN 1
                            ELSE 0
                            END AS isAvailable,
                        0 AS isDel,
                        CASE
                            WHEN org.c_type = '0' THEN 2
                            WHEN org.c_type = '1' THEN 1
                            ELSE 0
                            END AS dimension,
                        0 AS multiChannelType
        FROM hrm_org org
                 LEFT JOIN hrm_org parent ON org.c_superior_hid = parent.c_hid
            AND parent.c_status = 1
            AND parent.insert_date = DATE_FORMAT(curdate(),'%Y-%m-%d')
        WHERE org.c_status = 1
          AND org.insert_date = DATE_FORMAT(curdate(),'%Y-%m-%d')
          AND (parent.c_code IS NULL or parent.c_code = '' or parent.c_code = '-1')
    </select>
</mapper>
