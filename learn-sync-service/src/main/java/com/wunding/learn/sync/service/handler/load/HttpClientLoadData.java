package com.wunding.learn.sync.service.handler.load;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wunding.learn.common.constant.sync.SyncErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.http.JdkHttpUtils;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.sync.service.admin.dto.ScriptDTO;
import com.wunding.learn.sync.service.enums.AuthTypeEnum;
import com.wunding.learn.sync.service.enums.DataSourceTypeEnum;
import com.wunding.learn.sync.service.enums.ParamTypeEnum;
import com.wunding.learn.sync.service.enums.SyncDataTargetTypeEnum;
import com.wunding.learn.sync.service.groovy.GroovyScriptExecutor;
import com.wunding.learn.sync.service.handler.ConvertData;
import com.wunding.learn.sync.service.handler.LoadData;
import com.wunding.learn.sync.service.model.DataSource;
import com.wunding.learn.sync.service.model.Mapping;
import com.wunding.learn.sync.service.model.Script;
import com.wunding.learn.sync.service.model.SourceRecord;
import com.wunding.learn.sync.service.model.Target;
import com.wunding.learn.sync.service.service.IDataSourceService;
import com.wunding.learn.sync.service.service.IMappingService;
import com.wunding.learn.sync.service.service.IParamsService;
import com.wunding.learn.sync.service.service.IScriptService;
import com.wunding.learn.sync.service.service.ISourceRecordService;
import com.wunding.learn.sync.service.service.ITargetService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("httpClientLoadData")
@AllArgsConstructor
@Slf4j
public class HttpClientLoadData implements LoadData {

    private final IDataSourceService dataSourceService;
    private final IParamsService paramsService;
    private final IMappingService mappingService;
    private final ITargetService targetService;
    private final ConvertData convertData;
    private final ISourceRecordService sourceRecordService;
    private final GroovyScriptExecutor groovyScriptExecutor;
    private final IScriptService scriptService;


    public String getData(Long dataSourceId){
        Long dataSourceScriptId = null;
        try {
            dataSourceScriptId = Long.parseLong(paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.SCRIPT_ID.getType()));
        } catch (NumberFormatException e) {
            dataSourceScriptId = 0L;
        }
        if(dataSourceScriptId != null && dataSourceScriptId != 0L){
            String url = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_URL.getType());
            String method = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_METHOD.getType());
            String authType = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_AUTH_TYPE.getType());
            String param = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_PARAM.getType());
            String body = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_BODY_PARAMS.getType());
            Map<String,String> headers = paramsService.getListByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_HEADER.getType());
            String username = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_USERNAME.getType());
            String password = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_PASSWORD.getType());
            String token = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_TOKEN.getType());
            String ak = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_ACCESS_KEY.getType());
            String sk = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_SECRET_KEY.getType());
            String extraParam = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_EXTRA_PARAMS.getType());
            Map<String,Object> params = new HashMap<>();
            params.put("url", url);
            params.put("method", method);
            params.put("authType", authType);
            params.put("param", param);
            params.put("body", body);
            params.put("headers", headers);
            params.put("username", username);
            params.put("password", password);
            params.put("token", token);
            params.put("ak", ak);
            params.put("sk", sk);
            params.put("extraParam", extraParam);
            Script script = scriptService.getById(dataSourceScriptId);
            return (String)groovyScriptExecutor.execute(script.getContent(), params);
        }
        //读取数据源数据和参数
        DataSource dataSource = dataSourceService.getById(dataSourceId);
        if(!Objects.equals(dataSource.getType(), DataSourceTypeEnum.HTTP_CLIENT.getId())){
            throw new BusinessException(SyncErrorNoEnum.DATA_SOURCE_TYPE_ERROR);
        }
        String url = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_URL.getType());
        String method = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_METHOD.getType());
        String authType = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_AUTH_TYPE.getType());
        String param = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_PARAM.getType());
        String body = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_BODY_PARAMS.getType());
        Map<String,String> headers = paramsService.getListByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_HEADER.getType());


        if(Objects.equals(authType, AuthTypeEnum.BASIC.getId())){
            String username = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_USERNAME.getType());
            String password = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_PASSWORD.getType());
            headers.put("Authorization", "Basic " + Arrays.toString(
                Base64.getEncoder().encode((username + ":" + password).getBytes())));
        }else if(Objects.equals(authType, AuthTypeEnum.TOKEN.getId())){
            String token = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_TOKEN.getType());
            if(StringUtils.isBlank(token)){
                token = "";
            }
            headers.put("Authorization", "Bearer " + token);
        }else if(Objects.equals(authType, AuthTypeEnum.API_KEY.getId())){
            String ak = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_ACCESS_KEY.getType());
            String sk = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_SECRET_KEY.getType());
            Long scriptId = Long.parseLong(paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_KEY_SCRIPT_ID.getType()));
            String apiSingPosition = paramsService.getByDataSourceIdAndType(dataSourceId, ParamTypeEnum.REQUEST_KEY_POSITION.getType());
            ScriptDTO script = scriptService.getScriptById(scriptId);
            Map<String, Object> scriptParams = new HashMap<>();
            scriptParams.put("ak", ak);
            scriptParams.put("sk", sk);
            String kv = (String)groovyScriptExecutor.execute(script.getContent(), scriptParams);
            String[] kvArray = kv.split(":");
            String sginKey = kvArray[0];
            String sgin = kvArray[1];
            if(Objects.equals(apiSingPosition, "header")){
                headers.put(sginKey, sgin);
            }else if(Objects.equals(apiSingPosition, "param")){
                if(url.contains("?")){
                    url += "&" + sginKey + "=" + sgin;
                }else{
                    url += "?" + sginKey + "=" + sgin;
                }
            }else if(Objects.equals(apiSingPosition, "body")){
                //追加到body里
                body = JsonUtil.objToJson(JsonUtil.parseObject(body).put(sginKey, sgin)) ;
            }
        }
        //组装http请求参数
        Map<String, String> par = JsonUtil.parseObject(param, Map.class);
        if(par == null){
            par = new HashMap<>();
        }
        try {
            // 发起http请求
            String result = JdkHttpUtils.sendRequest(method, url, headers, par, body);
            return result;

        } catch (Exception e) {
            log.error("HttpClient getData 获取数据出错",e);
            throw new BusinessException(SyncErrorNoEnum.LOAD_DATA_ERROR);
        }
    }

    public void loadData(Long taskId,Long dataSourceId,Long mappingId) {

        String result="";
        SourceRecord sourceRecord = sourceRecordService.initSave(taskId);

        try{
            result = getData(dataSourceId);
            sourceRecord.setStatus("success");
            sourceRecord.setContent(result);
        }catch (Exception e){
            log.error("HttpClient loadData 获取数据出错",e);
            sourceRecord.setStatus("error");
            sourceRecord.setErrorMsg(e.getMessage());
            sourceRecord.setContent("");
            throw new BusinessException(SyncErrorNoEnum.LOAD_DATA_ERROR);
        }finally {
            sourceRecordService.updateById(sourceRecord);
        }

        receiveJsonData(taskId, dataSourceId, mappingId, result);

    }

    public void receiveJsonData(Long taskId, Long dataSourceId, Long mappingId, String result) {
        List<Map<String, Object>> data = new ArrayList<>();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = null;
        try {
            rootNode = mapper.readTree(result);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        DataSource dataSource = dataSourceService.getById(dataSourceId);
        String path = dataSource.getPath();
        if(!path.startsWith("/")){
            path = "/"+path;
        }
        if(path.contains(".")){
            path = StringUtils.replace(path, ".", "/");
        }
        // 使用JsonPointer查找值
        JsonNode dataNode = rootNode.at(path);
        if (dataNode.isArray()) {
            data = JsonUtil.parseListToMapList(dataNode);
        }else if(dataNode.isObject()){
            data.add(JsonUtil.parseObjectToMap(dataNode));
        }

        // 获取返回数据类型
        Mapping mapping = mappingService.getById(mappingId);
        Target target = targetService.getById(mapping.getTargetId());
        String type = target.getName();

        // 按类型接收数据
        if(Objects.equals(type, SyncDataTargetTypeEnum.ORG.getName())){
            receiveOrgData(data, taskId, mappingId);
        }else if(Objects.equals(type,SyncDataTargetTypeEnum.POST.getName())){
            receivePostData(data, taskId, mappingId);
        }else if(Objects.equals(type,SyncDataTargetTypeEnum.USER.getName())){
            receiveUserData(data, taskId, mappingId);
        }else{
            throw new RuntimeException("未知类型");
        }
    }

    /**
     * 接收组织数据
     * @param data 接收的数据
     */
    public void receiveOrgData(List<Map<String, Object>> data,Long taskId,Long mappingId) {
        convertData.convertToOrg(data, taskId,mappingId);
    }

    /**
     * 接收岗位数据
     * @param data 接收的数据
     */
    public void receivePostData(List<Map<String, Object>> data,Long taskId,Long mappingId) {
        convertData.convertToPost(data, taskId, mappingId);
    }

    /**
     * 接收用户数据
     * @param data 接收的数据
     */
    public void receiveUserData(List<Map<String, Object>> data,Long taskId,Long mappingId) {
        convertData.convertToUser(data, taskId, mappingId);
    }

}
