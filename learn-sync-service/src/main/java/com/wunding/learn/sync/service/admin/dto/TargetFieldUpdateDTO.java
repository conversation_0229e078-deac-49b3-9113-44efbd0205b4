package com.wunding.learn.sync.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 目标字段更新DTO
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@Schema(description = "目标字段更新DTO")
public class TargetFieldUpdateDTO {

    @Schema(description = "字段ID")
    @NotNull(message = "字段ID不能为空")
    private Long id;

    @Schema(description = "目标ID")
    private Long targetId;

    @Schema(description = "字段名")
    private String fieldName;

    @Schema(description = "字段类型")
    private String fieldType;

    @Schema(description = "字段长度")
    private Integer fieldLength;

    @Schema(description = "是否主键")
    private Boolean isPrimaryKey;

    @Schema(description = "是否可为空")
    private Boolean isNullable;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "字段描述")
    private String description;

    @Schema(description = "排序")
    private Integer sort;
} 