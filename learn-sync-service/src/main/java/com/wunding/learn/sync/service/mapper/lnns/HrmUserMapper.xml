<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.sync.service.mapper.lnns.HrmUserMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            c_oid, src_stm, c_operate_time, c_operator, c_code, c_name, c_gender, c_birth_place, c_age, c_birth_date, c_personal_id, c_passport_no, c_other_name_used, c_firstname, c_middlename, c_lastname, c_hukou_location, c_nation, c_nativeplace, c_nationality, c_marital_status, c_political_status, c_photo, c_first_work_date, c_remark, c_quanpin, c_jianpin, c_first_quanpin, c_ctf_type, c_thumbnail_photo, c_order, c_oid_applypos, c_resume_id, c_ccp_time, c_file_time, c_archivesage, c_system_time, c_accounttype, c_teller_number, c_expertise, c_credit_number, c_note_1, c_social_job, c_finance_begin_time, c_economy_begin_time, c_finance_begin_time_year, c_economy_begin_time_year, c_core_code, c_note_2, c_report, c_finance_before_time, c_economy_before_time, c_old_status, c_old_employeestatus, c_young_cadre, c_reserve_cadre, ods_dt, partition_dt, c_khdj, insert_date
        </sql>

    <select id="querySyncUserDTO" resultType="com.wunding.learn.user.api.dto.sync.SyncUserDTO" useCache="false">
        SELECT distinct emp.c_code             AS userId,
                        emp.c_code            AS employeeNo,
                        emp.c_code            AS loginName,
                        emp.c_name            AS fullName,
                        emp.c_name            AS nikeName,
                        NULL                  AS PASSWORD,
                        job.c_code            AS postCode,
                        dept.c_code           AS orgCode,
                        dept.c_biz_area       AS workingArea,
                        emp.c_firstname       AS firstName,
                        emp.c_lastname        AS lastName,
                        emp.c_quanpin         AS pinyin,
                        CASE
                            WHEN emp.c_gender = '1' THEN
                                1
                            WHEN emp.c_gender = '2' THEN
                                2
                            END               AS sex,
                        dept.c_contact_tel    AS workPhone,
                        NULL                  AS telephone,
                        NULL                  AS shortMobile,
                        NULL                  AS email,
                        0                     AS isLock,
                        CASE

                            WHEN emporg.c_employee_status = '1' THEN
                                0
                            ELSE 1
                            END               AS isAvailable,
                        0                     AS isDel,
                        0                     AS isSuper,
                        job.c_grade_id        AS userLevelId,
                        CASE
                            WHEN emporg.c_key_man = '1' THEN
                                1
                            ELSE 0
                            END               AS isExpert,
                        0                     AS lsDepartAdmin,
                        emp.c_birth_date      AS birthday,
                        emp.c_first_work_date AS joinDate,
                        emp.c_order           AS `order`,
                        FALSE                 AS isUpdateLoginName,
                        0                     AS multiChannelType,
                        ''                    AS identityJobLevelId,
                        ''                    AS identitySuperiorId
        FROM d_hrm_tb_sta_emp emp
                 LEFT JOIN d_hrm_tb_sta_emp_org emporg ON emp.c_oid = emporg.c_employee_id
            AND CURDATE() >= emporg.c_begin_date
            AND CURDATE() <![CDATA[<=]]> emporg.c_end_date
                 LEFT JOIN d_hrm_tb_org_orgunit dept
                           ON emporg.c_dept_hid = dept.c_hid AND CURDATE() >= dept.c_begin_date
                               AND CURDATE() <![CDATA[<=]]> dept.c_end_date
                 LEFT JOIN d_hrm_tb_org_job job
                           ON emporg.c_job_hid = job.c_hid AND CURDATE() >= job.c_effective_date_begin
                               AND CURDATE() <![CDATA[<=]]> job.c_effective_date_end
                 LEFT JOIN d_hrm_tb_org_unitrelation_new ur
                           ON ur.c_org_hid = emporg.c_dept_hid AND ur.c_dim_hid = '65ca64ab44274d789f8e958abbddc406' AND
                              CURDATE() >= ur.c_begin_date
                               AND CURDATE() <![CDATA[<=]]> ur.c_end_date AND ur.c_status = '1'
        WHERE emporg.c_dept_type = '1'
          AND emporg.c_employee_status <![CDATA[<>]]> '1'
          AND CURDATE() >= emporg.c_begin_date
          AND CURDATE() <![CDATA[<]]> emporg.c_end_date
          and emp.insert_date = curdate()
    </select>

    <select id="queryPostData" resultType="com.wunding.learn.user.api.dto.sync.SyncPostDTO" useCache="false">
        SELECT j.c_code             AS CODE,
               j.c_name             AS NAME,
               NULL                 AS MONTH,
               j.c_content          AS description,
               CASE

                   WHEN j.c_status = '1' THEN
                       1
                   ELSE 0
                   END              AS isAvailable,
               0                    AS isDel,
               j.c_eval_total_score AS sortNo,
               0                    AS multiChannelType
        FROM d_hrm_tb_org_job j
        WHERE CURDATE() >= j.c_effective_date_begin
          AND j.c_effective_date_end >= CURDATE()
          AND j.c_status = '1'
          and insert_date = date_format(CURDATE(), '%Y-%m-%d')
    </select>

</mapper>
