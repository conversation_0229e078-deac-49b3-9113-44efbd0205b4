<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.sync.service.mapper.lnns.HrmUserMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            c_oid, src_stm, c_operate_time, c_operator, c_code, c_name, c_gender, c_birth_place, c_age, c_birth_date, c_personal_id, c_passport_no, c_other_name_used, c_firstname, c_middlename, c_lastname, c_hukou_location, c_nation, c_nativeplace, c_nationality, c_marital_status, c_political_status, c_photo, c_first_work_date, c_remark, c_quanpin, c_jianpin, c_first_quanpin, c_ctf_type, c_thumbnail_photo, c_order, c_oid_applypos, c_resume_id, c_ccp_time, c_file_time, c_archivesage, c_system_time, c_accounttype, c_teller_number, c_expertise, c_credit_number, c_note_1, c_social_job, c_finance_begin_time, c_economy_begin_time, c_finance_begin_time_year, c_economy_begin_time_year, c_core_code, c_note_2, c_report, c_finance_before_time, c_economy_before_time, c_old_status, c_old_employeestatus, c_young_cadre, c_reserve_cadre, ods_dt, partition_dt, c_khdj, insert_date
        </sql>

    <select id="querySyncUserDTO" resultType="com.wunding.learn.user.api.dto.sync.SyncUserDTO" useCache="false">
        SELECT DISTINCT user.c_employee_code    AS userId,
                        user.c_employee_code    AS employeeNo,
                        user.c_employee_code    AS loginName,
                        user.c_employee_name    AS fullName,
                        user.c_employee_name    AS nikeName,
                        user.c_personal_id      AS idNumber,
                        NULL                    AS password,
                        org.c_code              AS orgCode,
                        CASE
                            WHEN user.c_gender = 1 THEN 1
                            WHEN user.c_gender = 2 THEN 2
                            ELSE 0
                            END                 AS sex,
                        user.c_office_tel       AS workPhone,
                        user.c_mobile_tel       AS telephone,
                        NULL                    AS shortMobile,
                        COALESCE(user.c_business_email, user.c_per_email) AS email,
                        0                       AS isLock,
                        CASE
                            WHEN user.c_employee_status = 2 THEN 1
                            ELSE 0
                            END                 		AS isAvailable,
                        0                       AS isDel,
                        0                       AS isSuper,
                        NULL                    AS userLevelId,
                        0                       AS isExpert,
                        0                       AS lsDepartAdmin,
                        user.c_birth_date       AS birthday,
                        user.c_labor_date       AS joinDate,
                        NULL                    AS `order`,
                        FALSE                   AS isUpdateLoginName,
                        0                       AS multiChannelType,
                        user.c_job_name         AS identityJobLevelId,
                        ''                      AS identitySuperiorId
        FROM hrm_user user
                 LEFT JOIN hrm_org org ON user.c_dept_hid = org.c_hid
            AND org.c_status = 1
            AND org.insert_date = DATE_FORMAT(curdate(),'%Y-%m-%d')
        WHERE user.insert_date = DATE_FORMAT(curdate(),'%Y-%m-%d')
    </select>

    <select id="queryPostData" resultType="com.wunding.learn.user.api.dto.sync.SyncPostDTO" useCache="false">
        SELECT DISTINCT user.c_position_name AS name,
                        NULL                 AS month,
                        user.c_position_name AS description,
                        1                    AS isAvailable,
                        0                    AS isDel,
                        0                    AS sortNo,
                        0                    AS multiChannelType
        FROM hrm_user user
        WHERE user.c_position_name IS NOT NULL
          AND user.insert_date = DATE_FORMAT(curdate(),'%Y-%m-%d')
    </select>

    <select id="queryJobLevelUserDTO" resultType="java.lang.String">
        SELECT user.c_job_name    AS NAME
        FROM hrm_user user
        WHERE user.c_position_name IS NOT NULL
          AND user.insert_date = DATE_FORMAT(curdate(),'%Y-%m-%d')
    </select>
</mapper>
