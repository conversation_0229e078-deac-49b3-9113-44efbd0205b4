package com.wunding.learn.sync.service.service.impl.lnns;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.sync.service.mapper.lnns.HrmUserMapper;
import com.wunding.learn.sync.service.model.lnns.Emp;
import com.wunding.learn.sync.service.service.lnns.IEmpService;
import com.wunding.learn.user.api.dto.sync.SyncUserDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 员工基本信息 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-05-16
 */
@Slf4j
@Service("empService")
public class EmpServiceImpl extends ServiceImpl<HrmUserMapper, Emp> implements IEmpService {

    @Override
    public List<SyncUserDTO> querySyncUserDTO() {
        return baseMapper.querySyncUserDTO();
    }
}
