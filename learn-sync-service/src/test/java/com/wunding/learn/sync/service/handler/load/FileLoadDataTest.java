package com.wunding.learn.sync.service.handler.load;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.user.UserConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.config.GlobalLocaleResolver;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.sync.service.handler.SaveData;
import com.wunding.learn.sync.service.handler.conver.GroovyConvertData;
import com.wunding.learn.sync.service.model.DataSource;
import com.wunding.learn.sync.service.model.Task;
import com.wunding.learn.sync.service.service.IDataSourceService;
import com.wunding.learn.sync.service.service.ISourceRecordService;
import com.wunding.learn.sync.service.service.impl.TaskServiceImpl;
import com.wunding.learn.sync.service.service.impl.lnns.EmpServiceImpl;
import com.wunding.learn.sync.service.service.impl.lnns.OrgJobServiceImpl;
import com.wunding.learn.sync.service.service.lnns.IOrgUnitService;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.sync.SyncPostDTO;
import com.wunding.learn.user.api.enums.IdentityCategoryEnum;
import com.wunding.learn.user.api.service.OrgFeign;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

@SpringBootTest(webEnvironment = WebEnvironment.NONE)
class FileLoadDataTest {

    private IDataSourceService dataSourceService;
    private OrgFeign orgFeign;
    private FileFeign fileFeign;
    private Task mockTask;
    private OrgDTO mockOrg;
    private List<SyncPostDTO> mockPostList;
    @InjectMocks
    private FileLoadData fileLoadData;
    @MockBean
    private GlobalLocaleResolver globalLocaleResolver;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @SpyBean
    private TaskServiceImpl taskService;
    @MockBean
    private SaveData saveData;
    @SpyBean
    private GroovyConvertData groovyConvertData;
    @SpyBean
    private IOrgUnitService orgUnitService;
    @SpyBean
    private OrgJobServiceImpl orgJobService;
    @SpyBean
    private EmpServiceImpl empService;

    private static final String TEST_FILE_PATH = "testCase/人力数据.zip";
    private static final String HRM_USER = "temp_expdata_hrm_renyuan";
    private static final String HRM_ORG = "temp_expdata_hrm_zhuzhi";


    @BeforeEach
    void setUp() {
        UserThreadContext.setTenantId("default");
        dataSourceService = mock(IDataSourceService.class);
        orgFeign = mock(OrgFeign.class);
        fileFeign = mock(FileFeign.class);
        fileLoadData = new FileLoadData(
            dataSourceService,
            Mockito.mock(ISourceRecordService.class),
            saveData,
            taskService,
            groovyConvertData,
            jdbcTemplate,
            orgUnitService,
            orgJobService,
            empService,
            orgFeign,
            fileFeign
        );
        ReflectionTestUtils.setField(fileLoadData, "orgFeign", orgFeign);
        ReflectionTestUtils.setField(fileLoadData, "fileFeign", fileFeign);
        mockTask = new Task();
        mockTask.setId(1L);
        mockTask.setOrgId(UserConstant.ROOT_ORG_CODE);

        mockOrg = new OrgDTO().setLevelPath("/0/").setLevelPathName("/总部/").setOrgCode(UserConstant.ROOT_ORG_CODE)
            .setOrgName("总部");

        // 初始化一个包含两个 SyncPostDTO 的列表
        mockPostList = new ArrayList<>();
        SyncPostDTO post1 = new SyncPostDTO();
        post1.setName("工程师");
        post1.setCode("ENG001");

        SyncPostDTO post2 = new SyncPostDTO();
        post2.setName("经理");
        post2.setCode("MNG001");

        mockPostList.add(post1);
        mockPostList.add(post2);
    }


    @Test
    void testParseHeadersFromDDL_WithSchema_ShouldReturnTableNameAndFields() {
        // Arrange
        String ddl = "CREATE TABLE hr.employees (\n" +
            "\"id\" INTEGER PRIMARY KEY,\n" +
            "\"name\" VARCHAR(100),\n" +
            "\"salary\" DECIMAL(10,2)\n" +
            ");";

        // Act
        List<String> result = FileLoadData.parseHeadersFromDDL(ddl);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(4, result.size());
        Assertions.assertEquals("employees", result.getFirst());
        Assertions.assertTrue(result.contains("id"));
        Assertions.assertTrue(result.contains("name"));
        Assertions.assertTrue(result.contains("salary"));
    }

    @Test
    void testParseHeadersFromDDL_OnlyCreateTableStatement_ShouldReturnTableNameOnly() {
        // Arrange
        String ddl = "CREATE TABLE test_table (";

        // Act
        List<String> result = FileLoadData.parseHeadersFromDDL(ddl);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals("test_table", result.getFirst());
    }

    @Test
    void testParseHeadersFromDDL_EmptyInput_ShouldReturnEmptyList() {
        // Arrange
        String ddl = "";

        // Act
        List<String> result = FileLoadData.parseHeadersFromDDL(ddl);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(0, result.size());
    }

    @Test
    void testParseHeadersFromDDL_WithSchemaLNNS_ShouldReturnEmptyList() {
        // Arrange
        String ddl = """
                        CREATE TABLE dwh.D_HRM_TB_ORG_ORGUNIT
                        (
            //                "src_stm"    character varying(30)	NOT NULL,
            //                "c_oid"    character varying(36),
            //                "c_operate_time"    timestamp(6) without time zone,
            //                "c_operator"    character varying(36),
            //                "c_setid"    character varying(36),
            //                "c_type"    character varying(256),
            //                "c_code"    character varying(256),
            //                "c_name"    character varying(256),
            //                "c_est_date"    timestamp without time zone,
            //                "c_level"    character varying(256),
            //                "c_contact_tel"    character varying(512),
            //                "c_fax"    character varying(512),
            //                "c_location_hid"    character varying(36),
            //                "c_address"    character varying(4000),
            //                "c_representative"    character varying(256),
            //                "c_approval_dcmtno"    character varying(256),
            //                "c_approving_company"    character varying(256),
            //                "c_invest_type"    character varying(256),
            //                "c_workforce_property"    character varying(256),
            //                "c_industry"    character varying(256),
            //                "c_econ_type"    character varying(256),
            //                "c_biz_area"    character varying(32),
            //                "c_status"    character varying(256),
            //                "c_begin_date"    timestamp without time zone,
            //                "c_end_date"    timestamp without time zone,
            //                "c_hid"    character varying(36),
            //                "c_post_code"    character varying(512),
            //                "c_website"    character varying(512),
            //                "c_introduction"    character varying(4000),
            //                "c_virtual_dept"    character varying(256),
            //                "c_dept_resp"    character varying(4000),
            //                "c_pic_id"    character varying(36),
            //                "c_pic_type"    character varying(256),
            //                "c_root"    character varying(32),
            //                "c_biz_type"    character varying(32),
            //                "c_administration_area"    character varying(32),
            //                "c_regcapital"    character varying(512),
            //                "c_abbreviate"    character varying(4000),
            //                "c_filetest"    character varying(32),
            //                "c_remark"    character varying(4000),
            //                "c_affiliate_company"    character varying(128),
            //                "c_org_property"    character varying(256),
            //                "c_full_name"    character varying(4000),
            //                "c_english_name"    character varying(256),
            //                "c_remode_org"    character varying(32),
            //                "c_region_org"    character varying(32),
            //                "c_nature_business"    character varying(1024),
            //                "c_work_file"    character varying(32),
            //                "c_core_org_number"    character varying(256),
            //                "c_code_credit"    character varying(256),
            //                "c_chang_card_date"    timestamp without time zone,
            //                "c_approved_date"    timestamp without time zone,
            //                "c_license_code"    character varying(256),
            //                "c_regulators"    character varying(1024),
            //                "c_org_type"    character varying(32),
            //                "c_dep_type"    character varying(32),
            //                "c_branch_type"    character varying(32),
            //                "c_dep_level"    character varying(32),
            //                "c_qzht_function"    character varying(32),
            //                "c_dep_change_lader"    character varying(1024),
            //                "c_dep_establishment"    character varying(32),
            //                "c_business_line"    character varying(4000),
            //                "c_old_type"    character varying(1024),
            //                "c_old_code"    character varying(64),
            //                "ods_dt"    character varying(8),
            //                "partition_dt"    date
                        )
                        with (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8)
                        PARTITION BY range (partition_dt)(
                            PARTITION pm202006 START ('2020-06-01'::date) END ('2020-07-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202007 START ('2020-07-01'::date) END ('2020-08-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202008 START ('2020-08-01'::date) END ('2020-09-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202009 START ('2020-09-01'::date) END ('2020-10-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202010 START ('2020-10-01'::date) END ('2020-11-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202011 START ('2020-11-01'::date) END ('2020-12-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202012 START ('2020-12-01'::date) END ('2021-01-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202101 START ('2021-01-01'::date) END ('2021-02-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202102 START ('2021-02-01'::date) END ('2021-03-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202103 START ('2021-03-01'::date) END ('2021-04-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202104 START ('2021-04-01'::date) END ('2021-05-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202105 START ('2021-05-01'::date) END ('2021-06-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202106 START ('2021-06-01'::date) END ('2021-07-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202107 START ('2021-07-01'::date) END ('2021-08-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202108 START ('2021-08-01'::date) END ('2021-09-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202109 START ('2021-09-01'::date) END ('2021-10-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202110 START ('2021-10-01'::date) END ('2021-11-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202111 START ('2021-11-01'::date) END ('2021-12-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202112 START ('2021-12-01'::date) END ('2022-01-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202201 START ('2022-01-01'::date) END ('2022-02-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202202 START ('2022-02-01'::date) END ('2022-03-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202203 START ('2022-03-01'::date) END ('2022-04-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202204 START ('2022-04-01'::date) END ('2022-05-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202205 START ('2022-05-01'::date) END ('2022-06-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202206 START ('2022-06-01'::date) END ('2022-07-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202207 START ('2022-07-01'::date) END ('2022-08-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202208 START ('2022-08-01'::date) END ('2022-09-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202209 START ('2022-09-01'::date) END ('2022-10-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202210 START ('2022-10-01'::date) END ('2022-11-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202211 START ('2022-11-01'::date) END ('2022-12-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202212 START ('2022-12-01'::date) END ('2023-01-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202301 START ('2023-01-01'::date) END ('2023-02-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202302 START ('2023-02-01'::date) END ('2023-03-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202303 START ('2023-03-01'::date) END ('2023-04-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202304 START ('2023-04-01'::date) END ('2023-05-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202305 START ('2023-05-01'::date) END ('2023-06-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202306 START ('2023-06-01'::date) END ('2023-07-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202307 START ('2023-07-01'::date) END ('2023-08-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202308 START ('2023-08-01'::date) END ('2023-09-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202309 START ('2023-09-01'::date) END ('2023-10-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202310 START ('2023-10-01'::date) END ('2023-11-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202311 START ('2023-11-01'::date) END ('2023-12-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202312 START ('2023-12-01'::date) END ('2024-01-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202401 START ('2024-01-01'::date) END ('2024-02-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202402 START ('2024-02-01'::date) END ('2024-03-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202403 START ('2024-03-01'::date) END ('2024-04-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202404 START ('2024-04-01'::date) END ('2024-05-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202405 START ('2024-05-01'::date) END ('2024-06-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202406 START ('2024-06-01'::date) END ('2024-07-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202407 START ('2024-07-01'::date) END ('2024-08-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202408 START ('2024-08-01'::date) END ('2024-09-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202409 START ('2024-09-01'::date) END ('2024-10-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202410 START ('2024-10-01'::date) END ('2024-11-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202411 START ('2024-11-01'::date) END ('2024-12-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202412 START ('2024-12-01'::date) END ('2025-01-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202501 START ('2025-01-01'::date) END ('2025-02-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202502 START ('2025-02-01'::date) END ('2025-03-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202503 START ('2025-03-01'::date) END ('2025-04-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202504 START ('2025-04-01'::date) END ('2025-05-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8),
                            PARTITION pm202505 START ('2025-05-01'::date) END ('2025-06-01'::date) WITH (appendonly=true, orientation=orc, compresstype=lz4, dicthreshold=0.8)
                        );
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_oid IS'主键';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_operate_time IS'操作时间';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_operator IS'操作员编号';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_setid IS'业务单元';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_type IS'机构类型';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_code IS'编码';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_name IS'名称';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_est_date IS'成立日期';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_level IS'层级';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_contact_tel IS'电话';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_fax IS'传真';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_location_hid IS'地点';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_address IS'地址';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_representative IS'法人代表';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_approval_dcmtno IS'批准文号';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_approving_company IS'登记批准机构';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_invest_type IS'投资范围';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_workforce_property IS'编制性质';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_industry IS'所属行业';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_econ_type IS'经济类型';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_biz_area IS'业务区域';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_status IS'状态';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_begin_date IS'生效日期';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_end_date IS'失效日期';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_hid IS'HID';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_post_code IS'邮编';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_website IS'网址';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_introduction IS'机构简介';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_virtual_dept IS'是否虚拟部门';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_dept_resp IS'部门职责';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_pic_id IS'部门负责人';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_pic_type IS'部门负责人类型';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_root IS'是否根节点';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_biz_type IS'业务类型';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_administration_area IS'行政区划';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_regcapital IS'注册资本';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_abbreviate IS'缩写';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_filetest IS'文件上传';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_remark IS'备注';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_affiliate_company IS'所属公司';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_org_property IS'职能属性';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_full_name IS'全路径';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_english_name IS'英文全称';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_remode_org IS'异地机构';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_region_org IS'地域属性';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_nature_business IS'经营范围';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_work_file IS'工作总结';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_core_org_number IS'核心机构号';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_code_credit IS'统一信用代码';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_chang_card_date IS'换发证日期';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_approved_date IS'批准成立日期';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_license_code IS'金融许可证机构编码';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_regulators IS'监管机构';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_org_type IS'机构类别';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_dep_type IS'部门类别';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_branch_type IS'分支机构类型';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_dep_level IS'支行级别';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_qzht_function IS'前中后台（职能方向）';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_dep_change_lader IS'分管领导';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_dep_establishment IS'是否机关编制部门';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_business_line IS'业务条线';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_old_type IS'原系统组织类型';
                        COMMENT ON COLUMN dwh.d_hrm_tb_org_orgunit.c_old_code IS'原系统组织编码';
                        COMMENT ON TABLE  dwh.d_hrm_tb_org_orgunit IS '组织单元基本信息';
            """;

        // Act
        List<String> result = FileLoadData.parseHeadersFromDDL(ddl);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(67, result.size());
        Assertions.assertEquals("D_HRM_TB_ORG_ORGUNIT", result.getFirst());
    }


    @Test
    void testGetDataSourceData_WhenDataSourceIsNull_ShouldThrowBusinessException() {
        // Arrange
        Long dataSourceId = 1L;
        when(dataSourceService.getById(dataSourceId)).thenReturn(null);

        // Act & Assert
        Assertions.assertThrows(BusinessException.class, () -> fileLoadData.getData(dataSourceId, null));
    }

    @Test
    void testGetDataSourceData_WhenDataSourcePathIsNull_ShouldThrowBusinessException() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(null);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act & Assert
        Assertions.assertThrows(BusinessException.class, () -> fileLoadData.getData(dataSourceId, null));
    }

    @Test
    void testGetDataSourceData_WhenFileNotExists_ShouldThrowBusinessException() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath("not_exist_file.gz");
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act & Assert
        Assertions.assertThrows(BusinessException.class, () -> fileLoadData.getData(dataSourceId, null));
    }

    @Test
    void testGetDataSourceData_WhenIOExceptionOccurs_ShouldThrowBusinessException() throws Exception {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(TEST_FILE_PATH);

        File mockFile = mock(File.class);
        when(mockFile.exists()).thenReturn(true);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        FileInputStream fis = mock(FileInputStream.class);
        when(fis.read()).thenThrow(new IOException());

        GzipCompressorInputStream gzipIn = mock(GzipCompressorInputStream.class);
        TarArchiveInputStream tarIn = mock(TarArchiveInputStream.class);

        TarArchiveEntry entry = new TarArchiveEntry("D_HRM_TB_ORG_JOB.dat");
        when(tarIn.getNextEntry()).thenReturn(entry, (TarArchiveEntry) null);

        doAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            ((OutputStream) args[1]).write("test content".getBytes());
            return null;
        }).when(gzipIn).readAllBytes();

        // 使用 PowerMock 或 Mockito-inline 来 mock IOUtils.toString 方法
        // 这里假设我们使用 Mockito-inline 的 mockStatic
        try (MockedStatic<IOUtils> utilities = Mockito.mockStatic(IOUtils.class)) {
            utilities.when(() -> IOUtils.toString(any(InputStream.class), eq(StandardCharsets.UTF_8)))
                .thenThrow(new IOException());

            // Act & Assert
            Assertions.assertThrows(BusinessException.class, () -> fileLoadData.getData(dataSourceId, null));
        }
    }

    @Test
    void testGetDataSourceData_WhenSuccessfullyReadAndParseFile_ShouldReturnEmptyList() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(TEST_FILE_PATH);

        File mockFile = mock(File.class);
        when(mockFile.exists()).thenReturn(true);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act
        // Assert
        Assertions.assertDoesNotThrow(() -> fileLoadData.getData(dataSourceId, null));
        // ACT
        List<Map<String, Object>> queryResult = fileLoadData.query(
            "select * from " + HRM_USER
                + " where c_employee_code = faa1d7c5d959413cb54e1cff0465e04b and c_employee_code = J00000035");

        System.out.println("queryResult:" + JsonUtil.objToJson(queryResult));
        Assertions.assertNotNull(queryResult);
        Assertions.assertFalse(queryResult.isEmpty());
        // ACT
        List<Map<String, Object>> queryResult2 = fileLoadData.query(
            "select * from " + HRM_USER
                + " where c_oid = faa1d7c5d959413cb54e1cff0465e04b and c_code = J000000377");

        System.out.println("queryResult2:" + JsonUtil.objToJson(queryResult2));
        Assertions.assertNotNull(queryResult2);
        Assertions.assertTrue(queryResult2.isEmpty());
    }

    @Test
    void testGetDataSourceData_WhenSuccessfullyReadAndParseFileToDb_ShouldReturnEmptyList() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(TEST_FILE_PATH);

        File mockFile = mock(File.class);
        when(mockFile.exists()).thenReturn(true);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act
        Assertions.assertDoesNotThrow(() -> fileLoadData.getData(dataSourceId, null));

        Integer count1 = Assertions.assertDoesNotThrow(
            () -> jdbcTemplate.queryForObject("select count(*) from " + ORG_JOB_TABLE + " where insert_date = ?",
                Integer.class,
                DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD)));
        Assertions.assertTrue(count1 >= 1);

        Integer count2 = Assertions.assertDoesNotThrow(
            () -> jdbcTemplate.queryForObject("select count(*) from " + ORG_UNIT_TABLE + " where insert_date = ?",
                Integer.class,
                DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD)));
        Assertions.assertTrue(count2 >= 1);

        Integer count3 = Assertions.assertDoesNotThrow(
            () -> jdbcTemplate.queryForObject(
                "select count(*) from " + ORG_UNITRELATION_NEW_TABLE + " where insert_date = ?", Integer.class,
                DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD)));
        Assertions.assertTrue(count3 >= 1);

        Integer count5 = Assertions.assertDoesNotThrow(
            () -> jdbcTemplate.queryForObject("select count(*) from " + STA_EMP_ORG_TABLE + " where insert_date = ?",
                Integer.class,
                DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD)));
        Assertions.assertTrue(count5 >= 1);

        Integer count6 = Assertions.assertDoesNotThrow(
            () -> jdbcTemplate.queryForObject("select count(*) from " + STA_EMP_TABLE + " where insert_date = ?",
                Integer.class,
                DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD)));
        Assertions.assertTrue(count6 >= 1);
    }

    /**
     * TC01: taskId = null → taskService.getById 返回 null → 日志记录错误
     */
    @Test
    void testReceiveOrgData_TaskIsNull_ShouldLogError() {
        // Arrange
        doNothing().when(saveData).saveSyncOrg(anyLong(), anyList());

        // Act
        fileLoadData.receiveOrgData(Collections.emptyList(), null, null);

        // Assert
        verify(taskService, never()).getById(anyLong());
        verify(orgFeign, never()).getById(anyString());
        verify(groovyConvertData, never()).findChildren(anyList(), any(Queue.class), any(OrgDTO.class));
        verify(saveData, never()).saveSyncOrg(anyLong(), anyList());
    }

    /**
     * TC02: taskId存在但对应task为null → 日志记录错误
     */
    @Test
    void testReceiveOrgData_TaskNotExist_ShouldLogError() {
        // Arrange
        when(taskService.getById(1L)).thenReturn(null);

        // Act
        fileLoadData.receiveOrgData(Collections.emptyList(), 1L, null);

        // Assert
        verify(taskService, atLeast(1)).getById(1L);
        verify(orgFeign, never()).getById(anyString());
        verify(groovyConvertData, never()).findChildren(anyList(), any(Queue.class), any(OrgDTO.class));
        verify(saveData, never()).saveSyncOrg(anyLong(), anyList());
    }

    /**
     * TC03: convertOrgData返回空列表 → 不调用后续方法
     */
    @Test
    void testReceiveOrgData_EmptySyncOrgList_ShouldNotProcessFurther() {
        // Arrange
        doReturn(mockTask).when(taskService).getById(1L);
        when(orgFeign.getById(UserConstant.ROOT_ORG_CODE)).thenReturn(mockOrg);
        when(orgUnitService.querySyncOrgDTO()).thenReturn(Collections.emptyList());
        ReflectionTestUtils.setField(groovyConvertData, "orgFeign", orgFeign);
        // Act
        fileLoadData.receiveOrgData(Collections.emptyList(), 1L, null);

        // Assert
        verify(taskService, atLeast(1)).getById(1L);
        verify(orgFeign, atLeast(1)).getById(UserConstant.ROOT_ORG_CODE);
        verify(groovyConvertData, times(1)).findChildren(anyList(), any(Queue.class), any(OrgDTO.class));
        verify(saveData, times(1)).saveSyncOrg(anyLong(), anyList());
    }

    /**
     * TC04: 所有步骤正常 → 成功调用findChildren和saveSyncOrg
     */
    @Test
    void testReceiveOrgData_NormalFlow_ShouldCallSaveSyncOrg() {
        when(taskService.getById(1L)).thenReturn(mockTask);
        when(orgFeign.getById(UserConstant.ROOT_ORG_CODE)).thenReturn(mockOrg);
        doNothing().when(saveData).saveSyncOrg(eq(1L), any());
        ReflectionTestUtils.setField(groovyConvertData, "orgFeign", orgFeign);
        // Act
        fileLoadData.receiveOrgData(Collections.emptyList(), 1L, null);

        // Assert
        verify(taskService, atLeast(1)).getById(1L);
        verify(orgFeign, atLeast(1)).getById(UserConstant.ROOT_ORG_CODE);
        verify(groovyConvertData).findChildren(any(), any(), any());
        verify(saveData).saveSyncOrg(eq(1L), any());
    }

    /**
     * TC01: 正常流程测试 - queryPostData 返回非空列表 - saveSyncPost 应被调用一次
     */
    @Test
    void testReceivePostData_WithValidData_ShouldCallSaveOnce() {
        // Arrange
        Long taskId = 1L;
        Long mappingId = 100L;

        when(taskService.getById(1L)).thenReturn(mockTask);
        when(orgJobService.queryPostData()).thenReturn(mockPostList);

        // Act
        fileLoadData.receivePostData(Collections.emptyList(), taskId, mappingId);

        // Assert
        verify(saveData, times(1)).saveSyncPost(eq(taskId), anyList());
    }

    /**
     * TC02: 空数据测试 - queryPostData 返回空列表 - saveSyncPost 不应被调用
     */
    @Test
    void testReceivePostData_WithEmptyList_ShouldNotCallSave() {
        // Arrange
        Long taskId = 1L;
        Long mappingId = 100L;

        when(taskService.getById(1L)).thenReturn(mockTask);
        when(orgJobService.queryPostData()).thenReturn(Collections.emptyList());

        // Act
        fileLoadData.receivePostData(Collections.emptyList(), taskId, mappingId);

        // Assert
        verify(saveData, never()).saveSyncPost(anyLong(), anyList());
    }

    /**
     * TC03: 异常处理测试 - queryPostData 抛出运行时异常 - 方法应抛出异常
     */
    @Test
    void testReceivePostData_WhenQueryThrowsException_ShouldThrow() {
        // Arrange
        Long taskId = 1L;
        Long mappingId = 100L;

        when(taskService.getById(1L)).thenReturn(mockTask);
        when(orgJobService.queryPostData()).thenThrow(new BusinessException(ErrorNoEnum.ERR_SERVER));

        // Act & Assert
        Assertions.assertThrows(BusinessException.class, () ->
            fileLoadData.receivePostData(Collections.emptyList(), taskId, mappingId));
    }

    /**
     * TC04: 数据转换验证 - 检查 categoryId 和 directoryId 是否正确设置
     */
    @Test
    void testReceivePostData_DataConversion_ShouldSetCorrectFields() {
        // Arrange
        Long taskId = 1L;
        Long mappingId = 100L;

        when(taskService.getById(1L)).thenReturn(mockTask);
        when(orgJobService.queryPostData()).thenReturn(mockPostList);

        // Act
        fileLoadData.receivePostData(Collections.emptyList(), taskId, mappingId);

        // Assert
        verify(saveData).saveSyncPost(eq(taskId), argThat(list -> {
            for (SyncPostDTO dto : list) {
                Assertions.assertEquals(IdentityCategoryEnum.POST.getCategoryId(), dto.getCategoryId());
                Assertions.assertEquals("7", dto.getDirectoryId());
            }
            return true;
        }));
    }
}
