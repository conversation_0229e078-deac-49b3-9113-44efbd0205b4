package com.wunding.learn.common.elastic.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/4/7
 */
@Configuration
@ConditionalOnProperty(prefix = "spring.elasticsearch", name = "uris")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EsConfig {

    private final ElasticsearchProperties elasticsearchProperties;

    @Bean
    public RestClient restClient() {

        List<String> uriList = elasticsearchProperties.getUris();

        HttpHost[] hostList = new HttpHost[uriList.size()];
        for (int i = 0, uriListLength = uriList.size(); i < uriListLength; i++) {
            hostList[i] = HttpHost.create(uriList.get(i));
        }
        RestClientBuilder builder = RestClient.builder(hostList);


        if(elasticsearchProperties.getUsername() != null && elasticsearchProperties.getPassword()!=null){
            final CredentialsProvider credentialsProvider =
                new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(elasticsearchProperties.getUsername(), elasticsearchProperties.getPassword()));

            builder.setHttpClientConfigCallback(
                (httpClientBuilder)-> httpClientBuilder
                    .setDefaultCredentialsProvider(credentialsProvider)
                    // 设置keepAlive 5分钟
                    .setKeepAliveStrategy((response, context) -> 300000)
            );
        }
        return builder.build();
    }

    @Bean
    public ElasticsearchTransport elasticsearchTransport() {
        return new RestClientTransport(restClient(), new JacksonJsonpMapper());
    }

    @Bean
    public ElasticsearchClient elasticsearchClient() {
        return new ElasticsearchClient(elasticsearchTransport());
    }
}
