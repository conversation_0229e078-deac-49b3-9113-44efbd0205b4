package com.wunding.learn.common.elastic.aspect;

import co.elastic.clients.elasticsearch.core.SearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class DslLogAspect {

    @Around("execution(* co.elastic.clients.elasticsearch.ElasticsearchClient.search(..)) ")
    public Object intercept(ProceedingJoinPoint point) throws Throwable {
        for (Object arg : point.getArgs()) {
            if(arg instanceof SearchRequest){
                log.info(arg.toString());
            }
        }
        return point.proceed();
    }

}
