[submodule "common"]
	path = common
	url = https://100.wunding.com:1443/server/mlearn/common.git
	branch = audit-lnns-dev
[submodule "comment"]
	path = comment
	url = https://100.wunding.com:1443/server/mlearn/comment.git
	branch = audit-lnns-dev
[submodule "file"]
	path = file
	url = https://100.wunding.com:1443/server/mlearn/file.git
	branch = audit-lnns-dev
[submodule "user"]
	path = user
	url = https://100.wunding.com:1443/server/mlearn/user.git
	branch = audit-lnns-dev
[submodule "exam"]
	path = exam
	url = https://100.wunding.com:1443/server/mlearn/exam.git
	branch = audit-lnns-dev
[submodule "course"]
	path = course
	url = https://100.wunding.com:1443/server/mlearn/course.git
	branch = audit-lnns-dev
[submodule "lecturer"]
	path = lecturer
	url = https://100.wunding.com:1443/server/mlearn/lecturer.git
	branch = audit-lnns-dev
[submodule "survey"]
	path = survey
	url = https://100.wunding.com:1443/server/mlearn/survey.git
	branch = audit-lnns-dev
[submodule "forum"]
	path = forum
	url = https://100.wunding.com:1443/server/mlearn/forum.git
	branch = audit-lnns-dev
[submodule "live"]
	path = live
	url = https://100.wunding.com:1443/server/mlearn/live.git
	branch = audit-lnns-dev
[submodule "websocket"]
	path = websocket
	url = https://100.wunding.com:1443/server/mlearn/websocket.git
	branch = audit-lnns-dev
[submodule "info"]
	path = info
	url = https://100.wunding.com:1443/server/mlearn/info.git
	branch = audit-lnns-dev
[submodule "project"]
	path = project
	url = https://100.wunding.com:1443/server/mlearn/project.git
	branch = audit-lnns-dev
[submodule "appraise"]
	path = appraise
	url = https://100.wunding.com:1443/server/mlearn/appraise.git
	branch = audit-lnns-dev
[submodule "special"]
	path = special
	url = https://100.wunding.com:1443/server/mlearn/special.git
	branch = audit-lnns-dev
[submodule "recruiting"]
	path = recruiting
	url = https://100.wunding.com:1443/server/mlearn/recruiting.git
	branch = audit-lnns-dev
[submodule "excitation"]
	path = excitation
	url = https://100.wunding.com:1443/server/mlearn/excitation.git
	branch = audit-lnns-dev
[submodule "promoted-game"]
	path = promoted-game
	url = https://100.wunding.com:1443/server/mlearn/promoted-game.git
	branch = audit-lnns-dev
[submodule "reading"]
	path = reading
	url = https://100.wunding.com:1443/server/mlearn/reading.git
	branch = audit-lnns-dev
[submodule "example"]
	path = example
	url = https://100.wunding.com:1443/server/mlearn/example.git
	branch = audit-lnns-dev
[submodule "business-view"]
	path = business-view
	url = https://100.wunding.com:1443/server/mlearn/business-view.git
	branch = audit-lnns-dev
[submodule "certification"]
	path = certification
	url = https://100.wunding.com:1443/server/mlearn/certification.git
	branch = audit-lnns-dev
[submodule "push"]
	path = push
	url = https://100.wunding.com:1443/server/mlearn/push.git
	branch = audit-lnns-dev
[submodule "market"]
	path = market
	url = https://100.wunding.com:1443/server/mlearn/market.git
	branch = audit-lnns-dev
[submodule "evaluation"]
	path = evaluation
	url = https://100.wunding.com:1443/server/mlearn/evaluation.git
	branch = audit-lnns-dev
[submodule "single"]
	path = single
	url = https://100.wunding.com:1443/server/mlearn/single.git
	branch = audit-lnns-dev
[submodule "apply"]
	path = apply
	url = https://100.wunding.com:1443/server/mlearn/apply.git
	branch = audit-lnns-dev
[submodule "train"]
	path = train
	url = https://100.wunding.com:1443/server/mlearn/train.git
	branch = audit-lnns-dev
[submodule "operation"]
	path = operation
	url = https://100.wunding.com:1443/server/mlearn/operation.git
	branch = audit-lnns-dev
[submodule "plan"]
	path = plan
	url = https://100.wunding.com:1443/server/mlearn/plan.git
	branch = audit-lnns-dev
[submodule "tenant"]
	path = tenant
	url = https://100.wunding.com:1443/server/mlearn/tenant.git
	branch = audit-lnns-dev
[submodule "trans"]
	path = trans
	url = https://100.wunding.com:1443/server/mlearn/trans.git
	branch = audit-lnns-dev
[submodule "march"]
	path = march
	url = https://100.wunding.com:1443/server/mlearn/march.git
	branch = audit-lnns-dev
[submodule "payment"]
	path = payment
	url = https://100.wunding.com:1443/server/mlearn/payment.git
	branch = audit-lnns-dev
[submodule "sync"]
	path = sync
	url = https://100.wunding.com:1443/server/mlearn/sync.git
	branch = audit-lnns-dev
