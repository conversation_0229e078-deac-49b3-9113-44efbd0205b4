package com.wunding.learn.live.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * </p> 直播供应商配置保存对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2024-03-06
 */
@Data
@Accessors(chain = true)
@Schema(name = "SaveLiveSupplierConfigDTO", description = "直播供应商配置保存对象")
public class SaveLiveSupplierConfigDTO implements Serializable {


    /**
     * 类型：2-保利威,3-小鱼易连
     */
    @Schema(description = "类型：2-保利威,3-小鱼易连")
    @NotNull(message = "类型不能为空")
    private Integer type;


    /**
     * 企业id
     */
    @Schema(description = "企业id")
    @NotBlank(message = "企业id不能为空")
    private String corpId;


    /**
     * 应用id
     */
    @Schema(description = "应用id")
    @NotBlank(message = "应用id不能为空")
    private String appId;


    /**
     * 应用密钥
     */
    @Schema(description = "应用密钥")
    @NotBlank(message = "应用密钥不能为空")
    private String secret;

}
