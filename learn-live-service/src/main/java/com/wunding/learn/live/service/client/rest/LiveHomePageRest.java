package com.wunding.learn.live.service.client.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.live.service.client.dto.LiveClientDTO;
import com.wunding.learn.live.service.client.query.LiveHomePageQuery;
import com.wunding.learn.live.service.client.query.TrainLiveHomePageQuery;
import com.wunding.learn.live.service.service.ILiveClientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 首页资讯管理
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/11/8 14:43
 */
@RestController
@RequestMapping("${module.live.contentPath:/}homePage")

@Tag(description = "首页直播管理", name = "LiveHomePageRest")
public class LiveHomePageRest {

    @Resource
    private ILiveClientService liveClientService;

    @GetMapping("/findLiveHomePageList")
    @Operation(operationId = "findLiveHomePageList", summary = "获取用户首页直播", description = "获取用户首页直播")
    public Result<PageInfo<LiveClientDTO>> findLiveHomePageList(
        @Valid @ParameterObject LiveHomePageQuery liveHomePageQuery) {
        return Result.success(liveClientService.findLiveHomePageList(liveHomePageQuery));
    }

    @GetMapping("/findTrainHomePageList")
    @Operation(operationId = "findTrainHomePageList", summary = "获取引用的直播", description = "获取引用的直播")
    public Result<PageInfo<LiveClientDTO>> findTrainHomePageList(
        @Valid @ParameterObject TrainLiveHomePageQuery query) {
        return Result.success(liveClientService.findTrainHomePageList(query));
    }
}
