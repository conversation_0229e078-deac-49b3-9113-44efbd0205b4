package com.wunding.learn.live.service.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.live.LiveErrorEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.ResourceMemberDTO;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.common.viewlimit.constant.LimitType;
import com.wunding.learn.common.viewlimit.model.BaseViewLimit;
import com.wunding.learn.live.api.dto.LiveDTO;
import com.wunding.learn.live.api.dto.LiveTimeDTO;
import com.wunding.learn.live.api.query.UserIdpStatisticQuery;
import com.wunding.learn.live.api.service.LiveFeign;
import com.wunding.learn.live.service.admin.dto.LiveVodListDTO;
import com.wunding.learn.live.service.admin.query.LiveVodQuery;
import com.wunding.learn.live.service.compant.LiveViewLimitComponent;
import com.wunding.learn.live.service.mapper.LiveMapper;
import com.wunding.learn.live.service.mapper.LiveStaticMapper;
import com.wunding.learn.live.service.model.Live;
import com.wunding.learn.live.service.model.LiveUserRecord;
import com.wunding.learn.live.service.model.LiveVodRecord;
import com.wunding.learn.live.service.service.ILiveService;
import com.wunding.learn.live.service.service.ILiveUserRecordService;
import com.wunding.learn.live.service.service.ILiveVodRecordService;
import com.wunding.learn.user.api.dto.MemberCardFeignDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import com.wunding.learn.user.api.service.MemberCardFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: mlearn
 * @description: <p></p>
 * @author: 赖卓成
 * @create: 2022-07-22 18:10
 **/
@RestController
@RequestMapping("${module.live.contentPath:/}")
public class LiveFeignImpl implements LiveFeign {

    @Resource
    ILiveService liveService;

    @Resource
    ILiveVodRecordService liveVodRecordService;

    @Resource
    LiveViewLimitComponent liveViewLimitComponent;
    @Resource
    private LiveMapper liveMapper;

    @Resource
    private MemberCardFeign memberCardFeign;

    @Resource
    private LiveStaticMapper liveStaticMapper;

    @Resource
    private ParaFeign paraFeign;

    @Resource
    private ILiveUserRecordService liveUserRecordService;

    @Override
    public LiveDTO getById(String id) {
        Live live = liveService.getById(id);
        if (live == null) {
            return null;
        }
        LiveDTO liveDTO = new LiveDTO();
        BeanUtils.copyProperties(live, liveDTO);
        ViewLimitBaseInfoDTO viewLimitBaseInfo = liveViewLimitComponent.getViewLimitBaseInfo(id);
        liveDTO.setProgrammeId(viewLimitBaseInfo.getProgrammeId());
        return liveDTO;
    }

    @Override
    public List<String> getEffectiveLiveIds() {
        LambdaQueryWrapper<Live> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Live::getId).eq(Live::getIsDel, 0).gt(Live::getEndTime, new Date());
        List<Live> liveList = liveService.getBaseMapper().selectList(queryWrapper);
        return liveList.stream().map(Live::getId).collect(Collectors.toList());
    }

    @Override
    public int getLivePubState(String id) {
        Live live = liveService.getById(id);
        if (live == null) {
            return 0;
        }
        return live.getIsPublish();
    }

    @Override
    public void publishLive(String id, Integer isPublish) throws IOException {
        PublishDTO publishDTO = new PublishDTO();
        publishDTO.setIds(List.of(id));
        publishDTO.setIsPublish(isPublish);
        liveService.publishLive(publishDTO);
    }

    @Override
    public void removeLive(String id) {
        liveService.deleteLive(id);
    }

    @Override
    public Integer getPlayBack(String taskContent) {
        LiveVodQuery liveVodQuery = new LiveVodQuery();
        liveVodQuery.setIdOrChannelId(taskContent);
        PageInfo<LiveVodListDTO> list = liveVodRecordService.getList(liveVodQuery);
        if (!list.getList().isEmpty()) {
            return 1;
        }
        return 0;
    }

    @Override
    public Map<String, Integer> getPlayBackByList(Collection<String> liveIdList) {
        return Collections.emptyMap();
    }

    @Override
    public LiveDTO getOneLive(String id) {
        // getById 带上了isDel 而这个就是需要isDel状态
        Live live = liveService.getOne(new QueryWrapper<Live>().lambda().eq(Live::getId, id));
        if (live == null) {
            return null;
        }
        LiveDTO liveDTO = new LiveDTO();
        BeanUtils.copyProperties(live, liveDTO);
        return liveDTO;
    }

    @Override
    public Map<String, String> getNameBatchIds(Collection<String> batchIds) {
        return liveMapper.selectBatchIds(batchIds).stream()
            .collect(Collectors.toMap(Live::getId, Live::getLiveName, (key1, key2) -> key1));
    }

    @Override
    public Map<String, ResourceMemberDTO> getResourceMemberBatchIds(Collection<String> batchIds) {
        Map<String, ResourceMemberDTO> result = new HashMap<>();
        if (org.springframework.util.CollectionUtils.isEmpty(batchIds)) {
            return result;
        }
        Map<String, String> map = liveMapper.selectBatchIds(batchIds).stream()
            .collect(Collectors.toMap(Live::getId, Live::getLiveName, (key1, key2) -> key1));

        // 取资源对应会员卡的信息
        Map<String, Set<MemberCardFeignDTO>> resourceCardMap = memberCardFeign.getMemberCardMapByResourceIds(
            batchIds);
        result = map.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry -> {
            ResourceMemberDTO resourceMemberDTO = new ResourceMemberDTO();
            resourceMemberDTO.setName(entry.getValue());
            if(null != resourceCardMap){
                Set<MemberCardFeignDTO> cardMap = resourceCardMap.get(entry.getKey());
                if(!CollectionUtils.isEmpty(cardMap)){
                    List<String> memberIconUrls = cardMap.stream().filter(Objects::nonNull).distinct()
                        .sorted((dto1, dto2) -> dto1.getSort().compareTo(dto2.getSort()))
                        .map(MemberCardFeignDTO::getCoinImgUrl)
                        .collect(Collectors.toList());

                    resourceMemberDTO.setMemberIconUrls(memberIconUrls);
                }
            }
            return resourceMemberDTO;
        }));
        return result;
    }


    @Override
    public void saveSyncLiveViewLimit(String resourceId, Long programmeId) {
        liveViewLimitComponent.handleNewViewLimit(programmeId, resourceId);
    }

    @Override
    public List<LearningCalendarTaskDTO> findLearningCalenderLiveTaskList(
        LearningCalendarTaskQuery learningCalendarTaskQuery) {
        return liveService.findLearningCalenderLiveTaskList(learningCalendarTaskQuery);
    }

    @Override
    public List<String> findLiveStatusByIdList(Collection<String> liveIdList) {
        if (CollectionUtils.isEmpty(liveIdList)) {
            return new ArrayList<>();
        }
        List<Live> liveList = liveService.lambdaQuery().eq(Live::getIsDel, 0).eq(Live::getIsPublish, 1).list();
        return liveList.stream().map(Live::getId).collect(Collectors.toList());
    }

    @Override
    public LiveDTO getOneLiveIncludeDel(String id) {
        Live oneLiveIncludeDel = liveMapper.getOneLiveIncludeDel(id);
        if (oneLiveIncludeDel == null) {
            return null;
        }
        LiveDTO liveDTO = new LiveDTO();
        BeanUtils.copyProperties(oneLiveIncludeDel, liveDTO);
        return liveDTO;
    }

    @Override
    public int getIdpStatistic(String year, String type) {
        UserIdpStatisticQuery userIdpStatisticQuery = new UserIdpStatisticQuery();
        userIdpStatisticQuery.setUserId(UserThreadContext.getUserId());
        userIdpStatisticQuery.setType(type);
        userIdpStatisticQuery.setYear(year);
        return liveStaticMapper.getIdpStatistic(userIdpStatisticQuery);
    }

    @Override
    public LiveTimeDTO getLiveTime(String id) {
        return liveMapper.getLiveTime(id);
    }

    @Override
    public Map<String, Integer> getLiveCount(Collection<String> liveIdList) {
        Map<String, Integer> liveCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(liveIdList)) {
            return liveCountMap;
        }
        for (String liveId : liveIdList) {
            LambdaQueryWrapper<Live> liveQueryWrapper = new LambdaQueryWrapper<>();
            liveQueryWrapper.eq(Live::getId, liveId);
            Live live = liveService.getOne(liveQueryWrapper);
            Optional.ofNullable(live).ifPresent(l -> {
                LambdaQueryWrapper<LiveVodRecord> liveVodRecordQueryWrapper = new LambdaQueryWrapper<>();
                liveVodRecordQueryWrapper.eq(LiveVodRecord::getChannelId, live.getChannelId());
                Long count = liveVodRecordService.count(liveVodRecordQueryWrapper);
                liveCountMap.put(liveId, count.intValue());
            });
        }
        return liveCountMap;
    }

    @Override
    public List<String> getInvalidLiveId(Collection<String> liveIdList) {
        if (CollectionUtils.isEmpty(liveIdList)) {
            return new ArrayList<>();
        }
        String supplier = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_306.getCode());
        return liveMapper.getInvalidLiveId(liveIdList, supplier);
    }

    @Override
    public Integer getLiveRecordCount(String userId, String taskContent) {
        return liveMapper.getLiveRecordCount(userId, taskContent);
    }

    @Override
    public int getResourceIsNotDeleteAndIsPublish(String resourceId) {
        LambdaQueryWrapper<Live> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Live::getId);
        queryWrapper.select(Live::getIsPublish);
        queryWrapper.eq(Live::getId, resourceId);
        Live live = liveService.getOne(queryWrapper);
        return live == null || live.getIsPublish().equals(PublishStatusEnum.IS_NO_PUBLISH.getValue()) ? 1 : 0;
    }

    @Override
    public Map<String, LiveDTO> getLiveByIdList(Collection<String> liveIdList) {
        if (CollectionUtils.isEmpty(liveIdList)) {
            return new HashMap<>();
        }
        List<LiveDTO> liveList = liveService.getLiveList(liveIdList);
        return liveList.stream().collect(Collectors.toMap(LiveDTO::getId, l -> l));
    }

    @Override
    public void publishOrUnPublishLive(String id, Integer publishType) {
        PublishDTO publishDTO = new PublishDTO();
        List<String> ids = new ArrayList<>();
        ids.add(id);
        publishDTO.setIds(ids);
        publishDTO.setIsPublish(publishType);
        try {
            liveService.publishLive(publishDTO);
        } catch (Exception e) {
            throw new BusinessException(LiveErrorEnum.ERR_LIVE_PUBLISH_FAIL, null, e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getLiveFinish(ResourceUserQuery resourceUserQuery) {
        Map<String, Integer> map = new HashMap<>();
        if (StringUtils.isBlank(resourceUserQuery.getResourceId()) || CollectionUtils.isEmpty(
            resourceUserQuery.getUserIdList())) {
            return map;
        }
        LambdaQueryWrapper<LiveUserRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LiveUserRecord::getLiveId, resourceUserQuery.getResourceId());
        queryWrapper.in(LiveUserRecord::getUserId, resourceUserQuery.getUserIdList());
        liveUserRecordService.list(queryWrapper)
            .forEach(dto -> map.put(dto.getUserId(), GeneralJudgeEnum.CONFIRM.getValue()));
        return map;
    }

    @Override
    public Map<String, ResourceBaseDTO> getLiveBaseInfo(ResourceBaseQuery resourceBaseQuery) {
        if (CollectionUtils.isEmpty(resourceBaseQuery.getResourceIdList())) {
            return new HashMap<>();
        }
        List<ResourceBaseDTO> resourceBaseList = liveService.getLiveBaseList(resourceBaseQuery);
        if (!CollectionUtils.isEmpty(resourceBaseQuery.getManagerAreaOrgIds())) {
            resourceBaseList.forEach(resource -> {
                Integer inManageArea = GeneralJudgeEnum.NEGATIVE.getValue();
                for (String managerOrgId : resourceBaseQuery.getManagerAreaOrgIds()) {
                    if (resource.getLevelPath().startsWith(managerOrgId)) {
                        inManageArea = GeneralJudgeEnum.CONFIRM.getValue();
                        break;
                    }
                }
                resource.setInManageArea(inManageArea);
            });
        }
        return resourceBaseList.stream()
            .collect(Collectors.toMap(ResourceBaseDTO::getId, Function.identity(), (key1, key2) -> key1));
    }

    @Override
    public Integer checkLiveManagePermissions(Collection<String> userManageAreaOrgId, String id) {
        return liveMapper.checkLiveManagePermissions(userManageAreaOrgId, id);
    }

    @Override
    public ResourceDeleteInfoDTO getLiveIsDelById(String resourceId) {
        return liveMapper.getLiveIsDelById(resourceId);
    }

    @Override
    public Integer getResourceIsDel(String resourceId) {
        ResourceDeleteInfoDTO resourceDeleteInfoDTO = liveMapper.getLiveIsDelById(resourceId);
        if (resourceDeleteInfoDTO == null) {
            return 0;
        }
        return resourceDeleteInfoDTO.getIsDel();
    }
}

