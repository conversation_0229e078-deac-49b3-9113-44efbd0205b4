package com.wunding.learn.live.service.client.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 课程首页查询
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/11/7 15:14
 */
@Data
public class LiveHomePageQuery extends BaseEntity implements Serializable {


    @Parameter(description = "内容规则")
    private String contentRule;

    @Parameter(hidden = true)
    private String userId;

    /**
     * 展示规则方式
     */
    @Parameter(hidden = true)
    private String showType;

    /**
     * 直播供应商类型
     */
    @Parameter(hidden = true)
    private Integer liveSupplier;

    /**
     * 组织id
     */
    @Parameter(description = "组织id")
    private String orgId;

    /**
     * 内容范围,1-全部范围的内容，2-归属部门为DIY对应组织的内容
     */
    @Parameter(description = "内容范围,1-全部范围的内容，2-归属部门为DIY对应组织的内容")
    private Integer contentRange;

}
