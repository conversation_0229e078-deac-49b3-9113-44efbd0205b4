package com.wunding.learn.trans.service.util;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.CRC32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/11/05
 */
@Slf4j
public class ZipCompressorUtil {

    static final int BUFFER = 8192;

    private final File zipFile;

    public ZipCompressorUtil(String pathName) {
        zipFile = new File(pathName);
    }

    private int suffixCode = 1;

    private List<String> fileNameList = new ArrayList<>();


    public void compress(String... pathName) {
        try (FileOutputStream fileOutputStream = new FileOutputStream(zipFile);
            CheckedOutputStream cos = new CheckedOutputStream(fileOutputStream, new CRC32());
            ZipOutputStream out = new ZipOutputStream(cos)) {
            String basedir = "";
            for (String s : pathName) {
                compress(new File(s), out, basedir);
            }
        } catch (Exception e) {
            log.error("compress error",e);
        }
    }

    private void compress(File file, ZipOutputStream out, String basedir) {
        // 判断是目录还是文件
        if (file.isDirectory()) {
            this.compressDirectory(file, out, basedir);
        } else {
            this.compressFile(file, out, basedir);
        }
    }

    /**
     * 压缩一个目录
     */
    private void compressDirectory(File dir, ZipOutputStream out, String basedir) {
        if (!dir.exists()) {
            return;
        }

        File[] files = dir.listFiles();
        // 递归
        for (File file : files) {
            compress(file, out, basedir + dir.getName() + "/");
        }
    }

    /**
     * 压缩一个文件
     */
    private void compressFile(File file, ZipOutputStream out, String basedir) {
        if (!file.exists()) {
            return;
        }

        try (FileInputStream fileInputStream = new FileInputStream(file);
            BufferedInputStream bis = new BufferedInputStream(fileInputStream)) {
            ZipEntry entry = null;
            String fileName = file.getName();
            // 打包下载时，处理打包项中存在同名的文件情况
            if (fileNameList.contains(fileName)) {
                log.info("this file name {} repeat", fileName);
                suffixCode++;
                entry = new ZipEntry(basedir + fileName + suffixCode);
                fileNameList.add(fileName + suffixCode);
            } else {
                entry = new ZipEntry(basedir + fileName);
                fileNameList.add(fileName);
            }

            out.putNextEntry(entry);
            int count;
            byte[] data = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                out.write(data, 0, count);
            }
        } catch (Exception e) {
            log.error("compressFile error",e);
        }
    }

}


