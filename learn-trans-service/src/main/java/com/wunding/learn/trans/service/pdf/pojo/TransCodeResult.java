package com.wunding.learn.trans.service.pdf.pojo;

import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import lombok.Data;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/26  14:32
 */
@Data
public class TransCodeResult {

    /**
     * 转码结果
     */
    private TranscodeStatusEnum status;

    /**
     * 转码后文件格式
     */
    private String mime;

    /**
     * 转码前文件格式
     */
    private String oldMime;

    /**
     * 转码后的文件路径
     */
    private String outFilePath;

}
