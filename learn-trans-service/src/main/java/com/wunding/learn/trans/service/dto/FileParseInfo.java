package com.wunding.learn.trans.service.dto;

import lombok.Data;

/**
 * 视频、音频文件信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @date 2020/10/9
 */
@Data
public class FileParseInfo {

    /**
     * 时长（秒）
     */
    private Long duration;

    /**
     * 格式化时长：10'30"
     */
    private String formatDuration;

    /**
     * 开始时间
     */
    private String start;

    /**
     * 比特率
     */
    private String bitrate;


    /**
     * 分辨率  eg:580x180
     */
    private String resolutionRatio;

    /**
     * 视频宽
     */
    private int width;

    /**
     * 视频高
     */
    private int height;

    /**
     * 帧率
     */
    private double fps;

}
