FROM hengyunabc/arthas:3.7.2-no-jdk


FROM openjdk:11 as builder
WORKDIR /app/
COPY ./learn-evaluation-service/target/learn-evaluation-service-*-fat.jar /app/evaluation.jar
RUN java -Djarmode=layertools -jar evaluation.jar extract

FROM openjdk:11

COPY --from=builder app/dependencies/ /app
COPY --from=builder app/snapshot-dependencies/ /app
COPY --from=builder app/spring-boot-loader/ /app
COPY --from=0 /opt/arthas /app/arthas
# 保证几乎只更新代码层
COPY --from=builder app/application/ /app

WORKDIR /app/

# 仅arthas运行需要，相关jdk命令运行需要有实际的用户存在
RUN groupadd -r myuser -g 1000 && useradd -d /home/<USER>/bin/bash -g myuser myuser

ENTRYPOINT ["java","-Duser.timezone=GMT+08","org.springframework.boot.loader.launch.JarLauncher"]

EXPOSE 28029
