package com.wunding.learn.evaluation.service.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wunding.learn.common.viewlimit.model.BaseViewLimit;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 评估库下发范围表
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("templet_lib_view_limit")
@Schema(name = "TempletLibViewLimit对象", description = "评估库下发范围表")
public class TempletLibViewLimit extends BaseViewLimit implements Serializable {

    private static final long serialVersionUID = 1L;

}
