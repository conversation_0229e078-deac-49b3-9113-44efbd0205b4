package com.wunding.learn.evaluation.service.client.dto;

import com.wunding.learn.common.dto.ExcitationBizBaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: mlearn
 * @description: <p></p>
 * @author: 赖卓成
 * @create: 2022-08-11 16:12
 **/
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "EvaluationReplyListDTO", description = "提交评估对象")
public class EvaluationReplyListDTO extends ExcitationBizBaseDTO {

    /**
     * 提交评估对象列表
     */
    @Schema(description = "提交评估对象列表")
    List<EvaluationReplyDTO> evaluationReplyDTOList;
}
