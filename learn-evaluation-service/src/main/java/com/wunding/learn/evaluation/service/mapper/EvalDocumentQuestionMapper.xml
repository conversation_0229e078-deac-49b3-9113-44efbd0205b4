<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.evaluation.service.mapper.EvalDocumentQuestionMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.evaluation.service.mapper.EvalDocumentQuestionMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.evaluation.service.model.DocumentQuestion">
        <!--@Table document_question-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="document_id" jdbcType="VARCHAR"
          property="documentId"/>
        <result column="question_no" jdbcType="INTEGER"
          property="questionNo"/>
        <result column="question_name" jdbcType="VARCHAR"
          property="questionName"/>
        <result column="question_type" jdbcType="INTEGER"
          property="questionType"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="question_category" jdbcType="VARCHAR"
          property="questionCategory"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, document_id, question_no, question_name, question_type, sort_no, question_category, is_available, is_del, create_by, create_time, update_by, update_time
    </sql>
</mapper>
