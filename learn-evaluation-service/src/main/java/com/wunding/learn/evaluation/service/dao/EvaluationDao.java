package com.wunding.learn.evaluation.service.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.evaluation.service.model.Evaluation;

/**
 * <AUTHOR>
 */
public interface EvaluationDao extends IService<Evaluation> {

    /**
     * 保存评估
     *
     * @param
     */
    void saveEvaluation(Evaluation evaluation);

    /**
     * 更新评估
     *
     * @param evaluation
     */
    void updateEvaluation(Evaluation evaluation);

    /**
     * 删除评估
     * @param evaluation
     */
    void delEvaluation(Evaluation evaluation);

    /**
     * 发布
     * @param evaluation
     */
    void publishEvaluation(Evaluation evaluation);

    /**
     * 取消发布
     * @param evaluation
     */
    void unPublishEvaluation(Evaluation evaluation);
}
