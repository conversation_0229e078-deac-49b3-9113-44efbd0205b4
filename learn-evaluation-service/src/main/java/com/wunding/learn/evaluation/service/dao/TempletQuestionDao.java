package com.wunding.learn.evaluation.service.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.evaluation.service.model.TempletQuestion;

/**
 * <AUTHOR>
 */
public interface TempletQuestionDao extends IService<TempletQuestion> {

    /**
     * 保存模版题目
     *
     * @param templetQuestion
     */
    void saveTempletQuestion(TempletQuestion templetQuestion);

    /**
     * 更新模版题目
     *
     * @param templetQuestion
     */
    void updateTempletQuestion(TempletQuestion templetQuestion);

    /**
     * 删除模版题目
     * @param templetQuestion
     */
    void delTempletQuestion(TempletQuestion templetQuestion);
}
