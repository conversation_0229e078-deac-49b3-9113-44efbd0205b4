package com.wunding.learn.evaluation.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/7/12 11:34
 */
@Data
@Schema(name = "EvaluationAnalysisDTO", description = "评估分析对象")
public class EvaluationAnalysisDTO extends EvaluationAnalysisBaseDTO implements Serializable {

    /**
     * 问题和答案集合
     */
    @Schema(description = "问题和答案集合")
    private List<EvalQuestionDTO> evalQuestionList;

}
