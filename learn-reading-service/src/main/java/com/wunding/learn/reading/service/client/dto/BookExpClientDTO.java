package com.wunding.learn.reading.service.client.dto;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客户端图书心得列表
 *
 * <AUTHOR>
 * @date 2022/09/06
 */
@Data
@Accessors(chain = true)
@Schema(name = "BookExpClientDTO", description = "客户端图书心得列表对象")
public class BookExpClientDTO {

    /**
     * 主键
     */
    @Schema(description = "图书心得id")
    private String id;

    /**
     * 心得内容
     */
    @Schema(description = "心得内容")
    private String content;

    @Schema(description = "评论人id", hidden = true)
    private String createBy;

    /**
     * 作者
     */
    @Schema(description = "心得评论人名称")
    private String author;

    /**
     * org
     */
    @Schema(description = "心得评论人组织")
    private String orgName;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String headUrl;

    /**
     * 日期
     */
    @Schema(description = "心得提交时间")
    private Date createTime;

    /**
     * 是否是本人
     */
    @Schema(description = "是否是本人")
    private Integer isHimself;

    /**
     * 是否点赞过
     */
    @Schema(description = "是否点赞过")
    private Integer isLiked;

    /**
     * 评论数
     */
    @Schema(description = "评论数")
    private Integer commentNum;

    /**
     * 点赞数
     */
    @Schema(description = "点赞数")
    private Integer likeNum;

    /**
     * 图书名字
     */
    @Schema(description = "图书名字")
    private String bookName;

    /**
     * 图书作者
     */
    @Schema(description = "图书作者")
    private String bookAuthor;

    /**
     * 共读图书id
     */
    @Schema(description = "共读图书id")
    private String bookId;

    /**
     * 图书封面图片地址
     */
    @Schema(description = "图书封面图片地址")
    private NamePath bookUrl;

    @Schema(description = "录音文件地址")
    private String recordingUrl;

    /**
     * 图书心得图片
     */
    @Schema(description = "图书心得图片列表")
    private List<NamePath> imgPathList;

}
