package com.wunding.learn.reading.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.reading.service.admin.dto.BookExperienceReportDTO;
import com.wunding.learn.reading.service.admin.query.BookExperienceReportQuery;
import com.wunding.learn.reading.service.model.UserReport;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 举报记录表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-09-05
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface UserReportMapper extends BaseMapper<UserReport> {


    /**
     * 分页获取心得举报列表
     *
     * @param bookExperienceReportQuery 心得举报查询对象
     * @return {@link List}<{@link BookExperienceReportDTO}>
     */
    List<BookExperienceReportDTO> selectBookReportDataByPage(
        @Param("params") BookExperienceReportQuery bookExperienceReportQuery);
}
