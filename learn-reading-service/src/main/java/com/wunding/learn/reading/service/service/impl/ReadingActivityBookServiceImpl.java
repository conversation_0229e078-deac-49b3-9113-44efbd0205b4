package com.wunding.learn.reading.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.aop.log.annotation.Log;
import com.wunding.learn.common.aop.log.annotation.Log.Type;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.reading.ReadingErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.file.ImageTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.reading.service.admin.dto.BookDTO;
import com.wunding.learn.reading.service.admin.dto.BookDetailDTO;
import com.wunding.learn.reading.service.admin.dto.BookManageDTO;
import com.wunding.learn.reading.service.admin.dto.BookSaveDTO;
import com.wunding.learn.reading.service.admin.query.BookManageQuery;
import com.wunding.learn.reading.service.client.dto.BookDetailClientDTO;
import com.wunding.learn.reading.service.client.dto.ReadingBookDTO;
import com.wunding.learn.reading.service.client.query.ReadingBookQuery;
import com.wunding.learn.reading.service.mapper.ReadingActivityBookMapper;
import com.wunding.learn.reading.service.model.BookLearningRecord;
import com.wunding.learn.reading.service.model.BookReadingRecord;
import com.wunding.learn.reading.service.model.ReadingActivityBook;
import com.wunding.learn.reading.service.service.IBookLearningRecordService;
import com.wunding.learn.reading.service.service.IBookReadingRecordService;
import com.wunding.learn.reading.service.service.IReadingActivityBookService;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p> 共读图书表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-09-05
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("readingActivityBookService")
public class ReadingActivityBookServiceImpl extends
    BaseServiceImpl<ReadingActivityBookMapper, ReadingActivityBook> implements IReadingActivityBookService {

    private final CourseFeign courseFeign;

    private final FileFeign fileFeign;

    @Resource
    private IBookReadingRecordService bookReadingRecordService;

    @Resource
    private IBookLearningRecordService bookLearningRecordService;

    @Resource
    private ExportComponent exportComponent;

    @Override
    public PageInfo<BookManageDTO> findBookByPage(BookManageQuery bookManageQuery) {
        PageInfo<BookManageDTO> pageInfo = PageMethod.startPage(bookManageQuery.getPageNo(),
            bookManageQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.selectBookByPage(bookManageQuery));

        List<BookManageDTO> bookList = pageInfo.getList();
        if (bookList.isEmpty()) {
            return pageInfo;
        }
        Set<String> courseIds = bookList.stream().map(BookManageDTO::getNo).collect(Collectors.toSet());
        Map<String, CourseInfoDTO> courseMap = courseFeign.getCourseBatchIds(courseIds);
        bookList.forEach(book -> {
            CourseInfoDTO courseInfo = courseMap.get(book.getNo());
            Optional.ofNullable(courseInfo).ifPresent(info -> {
                book.setCourseName(info.getCourseName());
                book.setCourseCode(info.getCourseNo());
            });
        });
        return pageInfo;
    }

    @Override
    public void deleteBook(String ids) {
        String[] bookIds = ids.split(",");
        List<String> bookIdList = Arrays.asList(bookIds);
        if (bookIdList.isEmpty()) {
            throw new BusinessException(ReadingErrorNoEnum.ERR_BOOK_NULL);
        }
        List<ReadingActivityBook> bookList = listByIds(bookIdList);
        removeBatchByIds2(bookIdList);
        IReadingActivityBookService readingActivityBookService = SpringUtil.getBean("readingActivityBookService",
            IReadingActivityBookService.class);
        bookList.forEach(readingActivityBookService::delLog);
    }

    @Override
    @Log(type = Type.DELETE, targetId = "#readingActivityBook.id", targetName = "#readingActivityBook.name", targetType = Log.TargetType.BOOK)
    public void delLog(ReadingActivityBook readingActivityBook) {
        //打删除sql的日志
    }

    @Override
    public void createOrUpdate(BookSaveDTO bookSaveOrUpdateDTO) {
        log.info("book_create_or_update_arg:[{}]", bookSaveOrUpdateDTO);
        if (StringUtils.isNotEmpty(bookSaveOrUpdateDTO.getId())) {
            log.debug("into_update_book");
            updateBook(bookSaveOrUpdateDTO);
        } else {
            log.debug("into_save_book");
            saveBook(bookSaveOrUpdateDTO);
        }
    }

    /**
     * 保存
     *
     * @param saveBook 书保存或更新dto
     */
    @Log(type = Log.Type.CREATE, targetId = "#saveBook.id", targetName = "#saveBook.name", targetType = Log.TargetType.BOOK)
    @Override
    public void saveBook(BookSaveDTO saveBook) {
        ReadingActivityBook activityBook = new ReadingActivityBook();
        String id = newId();
        saveBook.setId(id);
        BeanUtils.copyProperties(saveBook, activityBook);
        NamePath picUrl = saveBook.getPicUrl();
        if (StringUtils.isNotEmpty(picUrl.getPath())) {
            fileFeign.saveImage(id, ImageTypeEnum.bookImageIcon.name(), picUrl.getName(), picUrl.getPath());
        }
        save(activityBook);
    }

    /**
     * 更新本书
     *
     * @param updateBook 预订、保存或更新dto
     */
    @Log(type = Log.Type.UPDATE, targetId = "#updateBook.id", targetName = "#updateBook.name", targetType = Log.TargetType.BOOK)
    @Override
    public void updateBook(BookSaveDTO updateBook) {
        ReadingActivityBook activityBook = new ReadingActivityBook();
        BeanUtils.copyProperties(updateBook, activityBook);
        chooseUpdateImages(updateBook.getPicUrl(), updateBook.getId(), ImageTypeEnum.bookImageIcon);
        updateById(activityBook);
    }

    @Override
    public List<BookDTO> bookList(String id) {
        LambdaQueryWrapper<ReadingActivityBook> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReadingActivityBook::getActivityId, id);
        List<ReadingActivityBook> list = list(queryWrapper);
        return BeanListUtils.copyList(list, BookDTO.class);
    }

    @Override
    public BookDetailDTO detail(String id) {
        ReadingActivityBook activityBook = getById(id);
        BookDetailDTO book = new BookDetailDTO();
        BeanUtils.copyProperties(activityBook, book);
        NamePath namePath = fileFeign.getImageFileNamePath(id, ImageTypeEnum.bookImageIcon.name());
        book.setPicUrl(namePath);
        CourseInfoDTO courseInfoDTO = courseFeign.getById(book.getNo());
        // 如果课程是被删除的则不给课程id和courseName
        Optional.ofNullable(courseInfoDTO).map(CourseInfoDTO::getCourseName)
            .ifPresentOrElse(book::setCourseName, () -> book.setNo(null));
        return book;
    }

    @Override
    public void startReadingBook(String bookId) {
        ReadingActivityBook readingActivityBook = getById(bookId);
        Optional.ofNullable(readingActivityBook).ifPresent(r -> {
            String userId = UserThreadContext.getUserId();
            String lockName = "startReadingBook:" + userId + ":" + readingActivityBook.getNo();
            try {
                RedisLockUtil.acquire(lockName, 30L);
                LambdaQueryWrapper<BookReadingRecord> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BookReadingRecord::getBookNo, readingActivityBook.getNo());
                queryWrapper.eq(BookReadingRecord::getUserId, userId);
                BookReadingRecord bookReadingRecord = bookReadingRecordService.getOne(queryWrapper);
                if (Optional.ofNullable(bookReadingRecord).isEmpty()) {
                    BookReadingRecord newBean = new BookReadingRecord();
                    newBean.setId(StringUtil.newId());
                    newBean.setBookNo(readingActivityBook.getNo());
                    newBean.setUserId(UserThreadContext.getUserId());
                    newBean.setOrgId(UserThreadContext.getOrgId());
                    newBean.setBookType(readingActivityBook.getType());
                    newBean.setStatus(GeneralJudgeEnum.CONFIRM.getValue());
                    newBean.setBookName(readingActivityBook.getName());
                    bookReadingRecordService.save(newBean);
                }
            } finally {
                RedisLockUtil.release(lockName);
            }
        });
    }

    @Override
    public BookDetailClientDTO getBookDetail(String bookId) {
        BookDetailClientDTO bookDetail = new BookDetailClientDTO();
        ReadingActivityBook readingActivityBook = getById(bookId);
        Optional.ofNullable(readingActivityBook).ifPresent(r -> {
            bookDetail.setAuthor(r.getAuthor());
            bookDetail.setBookName(r.getName());
            bookDetail.setDescription(r.getDescription());
            bookDetail.setIsUpload(r.getIsUpload());
            bookDetail.setBookUrl(fileFeign.getImageUrl(r.getId(), ImageTypeEnum.bookImageIcon.name()));
            bookDetail.setLearnNum(getLearningNum(r.getId()));
        });
        return bookDetail;
    }

    @Override
    public List<ReadingBookDTO> getBookList(ReadingBookQuery readingBookQuery) {
        LambdaQueryWrapper<ReadingActivityBook> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReadingActivityBook::getActivityId, readingBookQuery.getActivityId());
        queryWrapper.orderByAsc(ReadingActivityBook::getSortNo).orderByDesc(ReadingActivityBook::getCreateTime);
        List<ReadingActivityBook> list = list(queryWrapper);
        List<ReadingBookDTO> bookList = BeanListUtils.copyList(list, ReadingBookDTO.class);
        for (ReadingBookDTO dto : bookList) {
            dto.setBookUrl(fileFeign.getImageFileNamePath(dto.getId(), ImageTypeEnum.bookImageIcon.name()));
            dto.setStatus(getStatus(UserThreadContext.getUserId(), dto.getNo(), dto.getType()));
        }
        return bookList;
    }

    private Integer getStatus(String userId, String no, Integer type) {
        LambdaQueryWrapper<BookReadingRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BookReadingRecord::getUserId, userId);
        queryWrapper.eq(BookReadingRecord::getBookNo, no);
        queryWrapper.eq(BookReadingRecord::getBookType, type);
        BookReadingRecord bookReadingRecord = bookReadingRecordService.getOne(queryWrapper);
        if (Optional.ofNullable(bookReadingRecord).isPresent()) {
            return bookReadingRecord.getStatus();
        }
        return CommonConstants.USER_BOOK_STATUS_NO;
    }

    private Long getLearningNum(String bookId) {
        LambdaQueryWrapper<BookLearningRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BookLearningRecord::getBookId, bookId);
        return bookLearningRecordService.count(queryWrapper);
    }

    /**
     * 判断是否更新图片(仅适用单张图片)
     *
     * @param newNamePath 图片路径
     * @param bizId       业务id
     * @param bizType     图像类型
     */
    public void chooseUpdateImages(NamePath newNamePath, String bizId, ImageTypeEnum bizType) {
        // 甚至可以去掉这次调用，只判断是不是含有temp路径
        NamePath image = fileFeign.getImageFileNamePath(bizId, bizType.name());
        String oldPath = image.getPath();
        String newPath = newNamePath.getPath();
        // 图片地址变化处理
        if (!StringUtils.equals(oldPath, newPath)) {
            // 删除原来的
            fileFeign.deleteImageByImageId(image.getId());
            // 添加新的图片
            fileFeign.saveImage(bizId, bizType.name(), newNamePath.getName(), newPath);
        }
    }

    @Override
    public void exportData(BookManageQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IReadingActivityBookService, BookManageDTO>(query) {

            @Override
            protected IReadingActivityBookService getBean() {
                return SpringUtil.getBean("readingActivityBookService", IReadingActivityBookService.class);
            }

            @Override
            protected PageInfo<BookManageDTO> getPageInfo() {
                return getBean().findBookByPage((BookManageQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ReadingBooksManage;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ReadingBooksManage.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }
}
