package com.wunding.learn.reading.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.reading.service.admin.dto.BookExperienceCommentDTO;
import com.wunding.learn.reading.service.admin.dto.BookExperienceDTO;
import com.wunding.learn.reading.service.admin.dto.BookExperienceDetailDTO;
import com.wunding.learn.reading.service.admin.dto.BookExperienceReportDTO;
import com.wunding.learn.reading.service.admin.dto.BookExperienceStarDTO;
import com.wunding.learn.reading.service.admin.dto.ReportDetailDTO;
import com.wunding.learn.reading.service.admin.query.BookExpBaseQuery;
import com.wunding.learn.reading.service.admin.query.BookExpCommentQuery;
import com.wunding.learn.reading.service.admin.query.BookExperienceQuery;
import com.wunding.learn.reading.service.admin.query.BookExperienceReportQuery;
import com.wunding.learn.reading.service.model.UserReport;
import com.wunding.learn.reading.service.service.IBookExperienceService;
import com.wunding.learn.reading.service.service.IUserReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Date;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 书经验休息
 *
 * <AUTHOR>
 * @date 2022/09/05
 */

@RestController
@RequestMapping("${module.reading.contentPath:/}bookExperience")
@Validated
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Tag(description = "图书心得管理", name = "BookExperienceRest")
public class BookExperienceRest {

    private final IBookExperienceService bookExperienceService;
    private final IUserReportService userReportService;

    @GetMapping("/list")
    @Operation(operationId = "list", summary = "图书心得列表", description = "图书心得列表")
    public Result<PageInfo<BookExperienceDTO>> list(@Valid @ParameterObject BookExperienceQuery bookExperience) {
        PageInfo<BookExperienceDTO> data = bookExperienceService.findBookExperienceByPage(bookExperience);
        return Result.success(data);
    }

    @DeleteMapping("/deleteBookExperience")
    @Operation(operationId = "deleteBookExperience", summary = "删除图书心得", description = "删除图书心得")
    public Result<Void> deleteBookExperience(
        @Parameter(description = "心得ids(用,分割)") @RequestParam @NotBlank(message = "心得ids不可为空") String ids) {
        bookExperienceService.deleteBookExperience(ids);
        return Result.success();
    }

    @GetMapping("/reportsData")
    @Operation(operationId = "reportsData", summary = "图书心得举报列表", description = "图书心得举报列表")
    public Result<PageInfo<BookExperienceReportDTO>> reportsData(
        @Valid @ParameterObject BookExperienceReportQuery bookExperienceReportQuery) {
        PageInfo<BookExperienceReportDTO> data = bookExperienceService.findBookReportDataByPage(
            bookExperienceReportQuery);
        return Result.success(data);
    }

    @PutMapping("/report/deal/{id}")
    @Operation(operationId = "deal", summary = "举报处理", description = "举报处理")
    public Result<Void> deal(@Parameter(description = "举报记录id") @PathVariable String id) {
        userReportService.lambdaUpdate().set(UserReport::getStatus, GeneralJudgeEnum.CONFIRM.getValue())
            .set(UserReport::getUpdateTime, new Date()).eq(UserReport::getId, id).update();
        return Result.success();
    }

    @GetMapping("/report/detail/{id}")
    @Operation(operationId = "reportDetail", summary = "图书心得举报详情", description = "图书心得举报详情")
    public Result<ReportDetailDTO> reportDetail(@Parameter(description = "举报记录id") @PathVariable String id) {
        ReportDetailDTO data = bookExperienceService.getReportDetailById(id);
        return Result.success(data);
    }


    @GetMapping("/detail/{id}")
    @Operation(operationId = "detail", summary = "图书心得内容", description = "图书心得内容")
    public Result<BookExperienceDetailDTO> detail(@Parameter(description = "心得id") @PathVariable String id) {
        BookExperienceDetailDTO data = bookExperienceService.detail(id);
        return Result.success(data);
    }

    @GetMapping("/star")
    @Operation(operationId = "star", summary = "图书心得点赞列表", description = "图书心得点赞列表")
    public Result<PageInfo<BookExperienceStarDTO>> star(@Valid @ParameterObject BookExpBaseQuery bookExpBaseQuery) {
        PageInfo<BookExperienceStarDTO> data = bookExperienceService.star(bookExpBaseQuery);
        return Result.success(data);
    }

    @GetMapping("/comment")
    @Operation(operationId = "comment", summary = "图书心得评论列表", description = "图书心得评论列表")
    public Result<PageInfo<BookExperienceCommentDTO>> comment(
        @Valid @ParameterObject BookExpCommentQuery bookExpBaseQuery) {
        PageInfo<BookExperienceCommentDTO> data = bookExperienceService.comment(bookExpBaseQuery);
        return Result.success(data);
    }

    @DeleteMapping("/comment/delete")
    @Operation(operationId = "commentDel", summary = "删除图书心得评论", description = "删除图书心得评论")
    public Result<Void> commentDel(
        @Parameter(description = "评论ids(用,分割)") @RequestParam @NotBlank(message = "评论ids不可为空") String ids) {
        bookExperienceService.commentDel(ids);
        return Result.success();
    }

    @Operation(operationId = "exportData", summary = "导出图书心得列表", description = "导出图书心得列表")
    @GetMapping("/exportData")
    public Result<Void> exportData(@Valid @ParameterObject BookExperienceQuery bookExperience) {
        bookExperienceService.exportData(bookExperience);
        return Result.success();
    }

    @Operation(operationId = "exportCommentData", summary = "导出心得评论管理列表", description = "导出心得评论管理列表")
    @GetMapping("/exportCommentData")
    public Result<Void> exportCommentData(@Valid @ParameterObject BookExpCommentQuery bookExpBaseQuery) {
        bookExperienceService.exportCommentData(bookExpBaseQuery);
        return Result.success();
    }

    @Operation(operationId = "exportStarData", summary = "导出心得点赞管理列表", description = "导出心得点赞管理列表")
    @GetMapping("/exportStarData")
    public Result<Void> exportStarData(@Valid @ParameterObject BookExpBaseQuery bookExpBaseQuery) {
        bookExperienceService.exportStarData(bookExpBaseQuery);
        return Result.success();
    }

    @Operation(operationId = "exportReportsData", summary = "导出图书心得举报详情", description = "导出图书心得举报详情")
    @GetMapping("/exportReportsData")
    public Result<Void> exportReportsData(@Valid @ParameterObject BookExperienceReportQuery bookExperienceReportQuery) {
        bookExperienceService.exportReportsData(bookExperienceReportQuery);
        return Result.success();
    }

}
