<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.reading.service.mapper.BookExperienceMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.reading.service.mapper.BookExperienceMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.reading.service.model.BookExperience">
        <!--@Table book_experience-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="activity_id" jdbcType="VARCHAR"
          property="activityId"/>
        <result column="book_id" jdbcType="VARCHAR"
          property="bookId"/>
        <result column="content" jdbcType="VARCHAR"
          property="content"/>
        <result column="comment_num" jdbcType="BIGINT"
          property="commentNum"/>
        <result column="like_num" jdbcType="BIGINT"
          property="likeNum"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , activity_id, book_id, content, comment_num, like_num, is_del, update_by, update_time, create_by, create_time
    </sql>

    <select id="selectBookExperienceByPage"
      resultType="com.wunding.learn.reading.service.admin.dto.BookExperienceDTO" useCache="false">
        select
        a.id, a.comment_num, a.like_num, a.create_time, b.name bookName,a.create_by, a.content
        from book_experience a
        inner join reading_activity_book b on a.book_id = b.id and b.is_del = 0
        where a.activity_id = #{params.activityId}
        and a.is_del = 0
        <if test="params.orgIdList !=null and params.orgIdList.size() > 0">
            and a.org_id in
            <foreach collection="params.orgIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.userIdList !=null and params.userIdList.size() > 0">
            and a.create_by in
            <foreach collection="params.userIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.content != null and params.content != ''">
            and instr(a.content,#{params.content}) > 0
        </if>
        <if test="params.bookId != null and params.bookId != ''">
            and a.book_id = #{params.bookId}
        </if>
        <if test="params.startTime != null">
            and date_format(a.create_time,'%Y-%m-%d') >= date_format(#{params.startTime},'%Y-%m-%d')
        </if>
        <if test="params.endTime != null">
            and date_format(#{params.endTime},'%Y-%m-%d') >= date_format(a.create_time,'%Y-%m-%d')
        </if>
    </select>

    <select id="selectBookExpStar"
      resultType="com.wunding.learn.reading.service.admin.dto.BookExperienceStarDTO" useCache="false">
        select a.id,
               a.create_by,
               a.create_by,
               a.create_time
        from exp_like_record a
                 inner join book_experience tbe on a.exp_id = tbe.id and tbe.is_del = 0
                 inner join reading_activity_book b on tbe.book_id = b.id and b.is_del = 0
        where a.exp_id = #{params.expId}
        order by a.create_time desc
    </select>

    <select id="selectBookExpComment"
      resultType="com.wunding.learn.reading.service.admin.dto.BookExperienceCommentDTO" useCache="false">
        select
        a.id, a.create_by, a.create_time, a.content
        from exp_comment a
        inner join sys_org g on g.id = a.org_id
        inner join book_experience tbe on a.exp_id = tbe.id and tbe.is_del = 0
        inner join reading_activity_book b on tbe.book_id = b.id and b.is_del = 0
        where a.exp_id = #{params.expId}
        and a.is_del = 0
        <if test="params.orgId != null and params.orgId != ''">
            and g.level_path like concat('%/',#{params.orgId},'/%')
        </if>
        <if test="params.userIdList !=null and params.userIdList.size() > 0">
            and a.create_by in
            <foreach collection="params.userIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.content != null and params.content != ''">
            and instr(a.content,#{params.content}) > 0
        </if>
        <if test="params.startTime != null">
            and a.create_time >= #{params.startTime}
        </if>
        <if test="params.endTime != null">
            and #{params.endTime} >= a.create_time
        </if>
        order by a.create_time desc
    </select>

    <select id="experience" parameterType="com.wunding.learn.reading.service.client.query.BookExpClientQuery"
      resultType="com.wunding.learn.reading.service.client.dto.BookExpClientDTO" useCache="false">
        select tbe.id,
        tbe.book_id,
        tbe.content,
        tbe.comment_num,
        tbe.like_num,
        tbe.create_by,
        tbe.create_time,
        tab.name bookName,
        tab.author bookAuthor
        from book_experience tbe
        inner join reading_activity_book tab on tab.id = tbe.book_id and tab.is_del = 0
        <where>
            <if test="null != params.activityId and ''!= params.activityId">
                tbe.activity_id = #{params.activityId}
            </if>
            <if test="null != params.searchKey and ''!= params.searchKey">
                and instr(tbe.content,#{params.searchKey}) > 0
            </if>
            <if test="null != params.bookId and ''!= params.bookId">
                and tbe.book_id = #{params.bookId}
            </if>
            and tbe.is_del = 0
        </where>
        order by tbe.create_time desc
    </select>

    <update id="updateBookExperienceCommentNum" parameterType="string">
        update book_experience
        set comment_num = (select count(*)
                           from exp_comment ec
                           where ec.is_del = 0
                             and ec.exp_id = #{id})
        where id = #{id}
    </update>

    <update id="updateBookExperienceLikeNum" parameterType="string">
        update book_experience
        set like_num = (select count(*)
                        from exp_like_record elr
                        where elr.exp_id = #{id})
        where id = #{id}
    </update>

    <select id="getActivityExpireCommentNum" resultType="integer">
        SELECT
            count(1)
        FROM
            book_experience a
        INNER JOIN exp_comment b ON a.id = b.exp_id and b.is_del = 0
        where a.is_del = 0
          and a.activity_id = #{activityId}
          and b.create_by = #{userId}
    </select>
</mapper>
