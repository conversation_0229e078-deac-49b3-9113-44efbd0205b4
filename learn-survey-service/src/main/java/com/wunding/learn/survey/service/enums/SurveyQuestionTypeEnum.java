package com.wunding.learn.survey.service.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <h4>mlearn</h4>
 * <p>调研题目类型枚举</p>
 *
 * <AUTHOR> 赖卓成
 * @date : 2022-05-20 16:44
 **/
@Getter
@AllArgsConstructor
public enum SurveyQuestionTypeEnum {

    /**
     * 默认
     */
    DEFAULT(0, "未知类型"),

    /**
     * 单选
     */
    QUESTION_TYPE_RADIO(1, "单选"),

    /**
     * 多选
     */
    QUESTION_TYPE_MULTI_SELECT(2, "多选"),

    /**
     * 判断
     */
    QUESTION_TYPE_JUDGEMENT(3, "判断"),

    /**
     * 填空
     */
    QUESTION_TYPE_CLOZE(4, "填空"),

    /**
     * 问答
     */
    QUESTION_TYPE_QA(5, "问答"),

    /**
     * 打分
     */
    QUESTION_TYPE_MARK(6, "打分"),

    /**
     * 材料
     */
    QUESTION_TYPE_TITLE(7, "材料"),
    ;

    @EnumValue
    private final Integer code;

    private final String desc;

    /**
     * 根据中文题目类型获取int类型
     *
     * @param desc desc 题目类型-中文
     * @return {@link Integer}
     */
    public static Integer getCodeByDesc(String desc) {

        if (QUESTION_TYPE_RADIO.getDesc().equals(desc)) {
            return QUESTION_TYPE_RADIO.getCode();
        } else if (QUESTION_TYPE_MULTI_SELECT.getDesc().equals(desc)) {
            return QUESTION_TYPE_MULTI_SELECT.getCode();
        } else if (QUESTION_TYPE_CLOZE.getDesc().equals(desc)) {
            return QUESTION_TYPE_CLOZE.getCode();
        } else if (QUESTION_TYPE_JUDGEMENT.getDesc().equals(desc)) {
            return QUESTION_TYPE_JUDGEMENT.getCode();
        } else if (QUESTION_TYPE_QA.getDesc().equals(desc)) {
            return QUESTION_TYPE_QA.getCode();
        } else if (QUESTION_TYPE_MARK.getDesc().equals(desc)) {
            return QUESTION_TYPE_MARK.getCode();
        } else if (QUESTION_TYPE_TITLE.getDesc().equals(desc)) {
            return QUESTION_TYPE_TITLE.getCode();
        } else {
            return DEFAULT.getCode();
        }
    }

    /**
     * 根据调研题目类型枚举的code值获取相应的枚举类
     *
     * @param value
     * @return
     */
    public static String getEventEnumByValue(Integer value) {
        for (SurveyQuestionTypeEnum eventTypeEnum : values()) {
            if (Objects.equals(eventTypeEnum.getCode(), value)) {
                return eventTypeEnum.getDesc();
            }
        }
        return SurveyQuestionTypeEnum.DEFAULT.getDesc();
    }

    /**
     * 判断题目是否有选项
     *
     * @param questionType [1,2...]
     * @return
     */
    public static boolean hasOption(Integer questionType) {
        return !Objects.equals(questionType, QUESTION_TYPE_CLOZE.getCode()) && !Objects.equals(questionType,
            QUESTION_TYPE_QA.getCode());
    }

    /**
     * 判断题目是否为主观题
     *
     * @return
     */
    public static boolean isSubjectiveType(int questionType) {
        if (Objects.equals(questionType, QUESTION_TYPE_CLOZE.getCode()) || Objects
            .equals(questionType, QUESTION_TYPE_QA.getCode())) {
            return true;
        }
        return false;
    }
}
