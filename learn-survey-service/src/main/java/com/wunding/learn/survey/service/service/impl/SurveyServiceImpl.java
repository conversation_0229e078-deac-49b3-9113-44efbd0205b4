package com.wunding.learn.survey.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.aop.log.annotation.Log;
import com.wunding.learn.common.aop.log.annotation.Log.Type;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.constant.excel.ExcelTitleBaseCheckUtil;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.constant.survey.SurveyErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.constant.train.TrainErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.RemindListDTO;
import com.wunding.learn.common.dto.RemindQueryDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceRecordSyncDTO;
import com.wunding.learn.common.dto.ResourceSyncDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.exam.IsTrainEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.market.FirstInfoContentEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.ResourceTypeCodeEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.library.record.enums.HandleTypeEnum;
import com.wunding.learn.common.library.record.enums.LibraryTypeEnum;
import com.wunding.learn.common.library.record.service.BaseLibraryRecordService;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.ResourceChangeEvent;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.ResourceRecordSyncEvent;
import com.wunding.learn.common.mq.event.ResourceSyncEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.market.FirstInfoViewLimitChangeEvent;
import com.wunding.learn.common.mq.event.project.ActivityStatusChangeEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mq.service.impl.RabbitMqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.query.RemindQuery;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.util.bean.CopyNotNullObjectUtil;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.dto.export.survey.SurveyAnalysisQuestionExportDTO;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.push.api.component.PushComponent;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.enums.RemindPushEnum;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.survey.api.dto.SurveyDTO;
import com.wunding.learn.survey.service.admin.dto.ParseQuestionExcelDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyAnalysisDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyAnalysisQuestionDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyAnalysisQuestionOptionDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyInfoDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyLibInfoDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyLibraryListDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyLibrarySaveDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyLibraryToUpdateInfoDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyLibraryUpdateDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyListDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyPreviewDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyPreviewQuestionDTO;
import com.wunding.learn.survey.service.admin.dto.SurveySaveDTO;
import com.wunding.learn.survey.service.admin.dto.SurveyUpdateDTO;
import com.wunding.learn.survey.service.admin.dto.base.SurveyRecordDetailDTO;
import com.wunding.learn.survey.service.admin.query.SurveyDetailListQuery;
import com.wunding.learn.survey.service.admin.query.SurveyLibraryListQuery;
import com.wunding.learn.survey.service.admin.query.SurveyListQuery;
import com.wunding.learn.survey.service.client.dto.SurveyInfoApiDTO;
import com.wunding.learn.survey.service.client.dto.SurveyListApiDTO;
import com.wunding.learn.survey.service.client.dto.SurveyQuestionResultDTO;
import com.wunding.learn.survey.service.client.dto.SurveyQuestionSubmitDTO;
import com.wunding.learn.survey.service.client.dto.SurveyResultDTO;
import com.wunding.learn.survey.service.client.dto.SurveySubmitDTO;
import com.wunding.learn.survey.service.client.query.SurveyListApiQuery;
import com.wunding.learn.survey.service.client.query.SurveySearchQuery;
import com.wunding.learn.survey.service.component.SurveyViewLimitComponent;
import com.wunding.learn.survey.service.constant.SurveyConstant;
import com.wunding.learn.survey.service.enums.SurveyQuestionTypeEnum;
import com.wunding.learn.survey.service.enums.SurveySourceTypeEnum;
import com.wunding.learn.survey.service.mapper.SurveyMapper;
import com.wunding.learn.survey.service.model.Survey;
import com.wunding.learn.survey.service.model.SurveyQuestion;
import com.wunding.learn.survey.service.model.SurveyQuestionOption;
import com.wunding.learn.survey.service.model.SurveyRecord;
import com.wunding.learn.survey.service.model.SurveyRecordDetail;
import com.wunding.learn.survey.service.mq.event.SurveyFinishEvent;
import com.wunding.learn.survey.service.service.ISurveyCategoryService;
import com.wunding.learn.survey.service.service.ISurveyQuestionOptionService;
import com.wunding.learn.survey.service.service.ISurveyQuestionService;
import com.wunding.learn.survey.service.service.ISurveyRecordDetailService;
import com.wunding.learn.survey.service.service.ISurveyRecordService;
import com.wunding.learn.survey.service.service.ISurveyService;
import com.wunding.learn.survey.service.template.SurveyExcelTemplate;
import com.wunding.learn.survey.service.utils.SurveyUtils;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.query.ProgrammedIdQuery;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <p> 在线调研表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-05-20
 */
@Slf4j
@Service("surveyService")
public class SurveyServiceImpl extends BaseServiceImpl<SurveyMapper, Survey> implements ISurveyService {

    private static final String SURVEY_SERVICE_BEAN_NAME = "surveyService";

    @Resource
    UserFeign userFeign;
    @Resource
    OrgFeign orgFeign;
    @Resource
    @Lazy
    SurveyUtils surveyUtils;
    @Resource
    ImportDataFeign importDataFeign;
    @Resource
    ISurveyRecordService surveyRecordService;
    @Resource
    @Lazy
    ISurveyQuestionService surveyQuestionService;
    @Resource
    ISurveyCategoryService surveyCategoryService;
    @Resource
    BaseLibraryRecordService baseLibraryRecordService;
    @Resource
    SurveyViewLimitComponent surveyViewLimitComponent;
    @Resource
    ISurveyQuestionOptionService surveyQuestionOptionService;
    @Resource
    ISurveyRecordDetailService surveyRecordDetailService;
    @Resource
    RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RabbitMqProducer rabbitMqProducer;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private PushComponent pushComponent;
    @Resource
    private PushFeign pushFeign;
    @Resource
    @Lazy
    private IResourceViewLimitService resourceViewLimitService;

    @Override
    @Log(type = Log.Type.CREATE, targetId = "#surveySaveDTO.id", targetName = "#surveySaveDTO.surveyName", targetType = Log.TargetType.SURVEY)
    @Transactional(rollbackFor = Exception.class)
    public String saveSurvey(SurveySaveDTO surveySaveDTO) {
        // 当下发范围为仅创建人可见,不用鉴权
        checkSurveyVisitArea(surveySaveDTO);

        // 参数
        String surveyName = surveySaveDTO.getSurveyName();
        Integer sourceType = surveySaveDTO.getSourceType();
        String surveyExcelPath = surveySaveDTO.getSurveyExcelPath();
        String description = surveySaveDTO.getDescription();
        Date startTime = surveySaveDTO.getStartTime();
        Date endTime = surveySaveDTO.getEndTime();
        Integer addToLib = surveySaveDTO.getAddToLib();
        Integer viewResult = surveySaveDTO.getViewResult();
        Integer sortNo = surveySaveDTO.getSortNo();
        String surveyLibId = surveySaveDTO.getSurveyLibId();
        String orgId = UserThreadContext.getOrgId();
        String id = StringUtil.newId();
        surveySaveDTO.setId(id);

        // 1 判断添加类型
        Survey survey = new Survey().setId(id).setSurveyNo(surveyUtils.generatorSurveyNo()).setSurveyName(surveyName)
            .setDescription(description).setTrain(surveySaveDTO.getIsTrain()).setSourceType(sourceType)
            .setStartTime(startTime).setEndTime(endTime).setOrgId(orgId).setPublished(SurveyConstant.PUBLISH_NO)
            .setViewResult(viewResult).setSortNo(sortNo).setViewType(surveySaveDTO.getViewType());
        if (sourceType.equals(SurveySourceTypeEnum.IMPORT.getCode())) {
            // 上传导入 先处理Excel文件
            ImportDataDTO importData = importDataFeign.getImportData(surveyExcelPath);
            String[][] excel = importData.getExcel();
            ParseQuestionExcelDTO parseQuestionExcelDTO = parseQuestionExcel(excel, id);
            List<SurveyQuestion> questions = parseQuestionExcelDTO.getQuestions();
            List<SurveyQuestionOption> options = parseQuestionExcelDTO.getOptions();
            surveyQuestionService.saveBatch(questions);
            surveyQuestionOptionService.saveBatch(options);
            survey.setQuestionCount((long) questions.size());

            // 同时保存到调研库
            if (addToLib.equals(SurveyConstant.ADD_TO_LIB_YES)) {
                String libId = StringUtil.newId();
                Survey libSurvey = new Survey().setId(libId).setSurveyName(surveyName).setDescription(description)
                    .setOrgId(orgId).setAvailable(SurveyConstant.AVAILABLE_YES)
                    .setSourceType(SurveySourceTypeEnum.LIBRARY.getCode()).setViewResult(viewResult);
                ParseQuestionExcelDTO libParseQuestionExcelDTO = parseQuestionExcel(excel, libId);
                List<SurveyQuestion> libQuestions = libParseQuestionExcelDTO.getQuestions();
                List<SurveyQuestionOption> libOptions = libParseQuestionExcelDTO.getOptions();
                libSurvey.setQuestionCount((long) libQuestions.size());
                baseMapper.insert(libSurvey);
                surveyQuestionService.saveBatch(libQuestions);
                surveyQuestionOptionService.saveBatch(libOptions);
                // 记录资源库操作记录
                baseLibraryRecordService.saveOperationRecord(survey.getSurveyName(), 1, HandleTypeEnum.TP_ADD_RECORD,
                    LibraryTypeEnum.LIB_SURVEY);
            }

        } else if (sourceType.equals(SurveySourceTypeEnum.LIBRARY_QUOTE.getCode())) {
            // 调研库引入
            Survey libSurvey = this.getById(surveyLibId);
            if (libSurvey == null || !libSurvey.getSourceType().equals(SurveySourceTypeEnum.LIBRARY.getCode())
                || libSurvey.getAvailable().equals(SurveyConstant.AVAILABLE_NO)) {
                throw new BusinessException(SurveyErrorNoEnum.ILLEGAL_TO_SUBMIT);
            }
            survey.setQuoteLibId(surveyLibId);
            survey.setQuestionCount(libSurvey.getQuestionCount());
        }

        // 2 处理可见范围
        surveyViewLimitComponent.handleNewViewLimit(surveySaveDTO.getProgrammeId(), id);

        // 默认发布时
        Integer isPublish = surveySaveDTO.getIsPublish();
        if (isPublish != null && isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
            survey.setPublished(isPublish);
            survey.setPublishBy(UserThreadContext.getUserId());
            survey.setPublishTime(new Date());
        }

        baseMapper.insert(survey);
        // 初始化调研资源激励配置规则
        mqProducer.sendMsg(new ExcitationInitMqEvent(new ResourceConfigInitDTO().setResourceId(survey.getId())
            .setResourceType(ExcitationEventCategoryEnum.SURVEY.getCode())));

        // 启用才给资源配置推送
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(surveySaveDTO.getIsPublish()) && Optional.ofNullable(
            surveySaveDTO.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeignSave(surveySaveDTO);
        }
        // 发送资源同步事件消息
        if (Objects.equals(survey.getTrain(), IsTrainEnum.ITSELF.getValue())) {
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.SURVEY.name(), survey.getId(),
                    survey.getSurveyName(), survey.getStartTime(), survey.getEndTime(), 1, survey.getPublished(),
                    survey.getIsDel(), survey.getCreateBy(), survey.getCreateTime(), survey.getUpdateBy(),
                    survey.getUpdateTime())));
        }
        return survey.getId();
    }

    private void checkSurveyVisitArea(SurveySaveDTO surveySaveDTO) {
        if (surveySaveDTO.getViewType() != 1 && surveySaveDTO.getIsTrain() == 0) {
            //对下发范围进行鉴权
            ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
            programmedIdQuery.setNewProgrammeId(surveySaveDTO.getProgrammeId());
            boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
            if (!checkViewLimit) {
                throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
            }
        }
    }

    @Override
    @Log(type = Type.UPDATE, targetId = "#surveyUpdateDTO.id", targetName = "#surveyUpdateDTO.surveyName", targetType = Log.TargetType.SURVEY)
    @Transactional(rollbackFor = Exception.class)
    public void updateSurvey(String id, SurveyUpdateDTO surveyUpdateDTO) {

        // 处理鉴权 —— 提取方法，减少代码复杂度
        checkViewLimit(surveyUpdateDTO);

        // 参数
        String surveyName = surveyUpdateDTO.getSurveyName();
        String surveyExcelPath = surveyUpdateDTO.getSurveyExcelPath();
        String description = surveyUpdateDTO.getDescription();
        Date startTime = surveyUpdateDTO.getStartTime();
        Date endTime = surveyUpdateDTO.getEndTime();
        Integer viewResult = surveyUpdateDTO.getViewResult();
        Integer sortNo = surveyUpdateDTO.getSortNo();
        String surveyLibId = surveyUpdateDTO.getSurveyLibId();
        surveyUpdateDTO.setId(id);
        Survey survey = baseMapper.selectById(id);
        Integer sourceType = survey.getSourceType();
        if (sourceType.equals(SurveySourceTypeEnum.IMPORT.getCode())) {
            // 上传的调研
            if (StringUtils.isNotBlank(surveyExcelPath)) {
                // 删除原来的题目和选项
                surveyQuestionService.removeBySurveyId(id);
                surveyQuestionOptionService.removeBySurveyId(id);
                ImportDataDTO importData = importDataFeign.getImportData(surveyExcelPath);
                String[][] excel = importData.getExcel();
                ParseQuestionExcelDTO parseQuestionExcelDTO = parseQuestionExcel(excel, id);
                List<SurveyQuestion> questions = parseQuestionExcelDTO.getQuestions();
                List<SurveyQuestionOption> options = parseQuestionExcelDTO.getOptions();
                surveyQuestionService.saveBatch(questions);
                surveyQuestionOptionService.saveBatch(options);
            }
        } else if (sourceType.equals(SurveySourceTypeEnum.LIBRARY_QUOTE.getCode())) {
            // 引入的调研
            survey.setQuoteLibId(surveyLibId);
        }

        Survey newSurvey = survey
            .setSurveyName(surveyName)
            .setDescription(description)
            .setStartTime(startTime)
            .setEndTime(endTime)
            .setViewResult(viewResult)
            .setSortNo(sortNo)
            .setViewType(surveyUpdateDTO.getViewType());

        // 默认发布时
        Integer isPublish = surveyUpdateDTO.getIsPublish();
        if (isPublish != null && isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
            survey.setPublished(isPublish);
            survey.setPublishBy(UserThreadContext.getUserId());
            survey.setPublishTime(new Date());
        } else {
            survey.setPublished(0);
            survey.setPublishBy(null);
            survey.setPublishTime(null);
        }
        if (StringUtils.isNotBlank(surveyUpdateDTO.getOrgId())) {
            newSurvey.setOrgId(surveyUpdateDTO.getOrgId());
        }
        baseMapper.updateById(newSurvey);

        // 处理可见范围
        surveyViewLimitComponent.handleNewViewLimit(surveyUpdateDTO.getProgrammeId(), id);
        //更新头条下发范围
        mqProducer.sendMsg(new FirstInfoViewLimitChangeEvent(id, FirstInfoContentEnum.survey.name(),
            surveyUpdateDTO.getProgrammeId()));

        // 删除缓存
        redisTemplate.delete(SurveyConstant.SURVEY_QUESTION_CACHE + ":" + UserThreadContext.getTenantId() + ":" + id);
        mqProducer.sendMsg(new ResourceOperateEvent(
            GeneralJudgeEnum.CONFIRM.getValue().equals(surveyUpdateDTO.getIsPublish()) ? OperationEnum.PUBLISH
                : OperationEnum.PUBLISH_CANCEL, PushType.SURVEY.getKey(), newSurvey.getId()));
        mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.UPDATE, PushType.SURVEY.getKey(), newSurvey.getId()));
        // 与添加逻辑类似
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(surveyUpdateDTO.getIsPublish()) && Optional.ofNullable(
            surveyUpdateDTO.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeignUpdate(surveyUpdateDTO);
        }
        // 发送资源同步事件消息
        if (Objects.equals(survey.getTrain(), IsTrainEnum.ITSELF.getValue())) {
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.SURVEY.name(), survey.getId(),
                    survey.getSurveyName(), survey.getStartTime(), survey.getEndTime(), 1, survey.getPublished(),
                    survey.getIsDel(), survey.getCreateBy(), survey.getCreateTime(), survey.getUpdateBy(),
                    survey.getUpdateTime())));
        }
    }

    private void checkViewLimit(SurveyUpdateDTO surveyUpdateDTO) {
        // 当下发范围为仅创建人可见,不用鉴权
        if (surveyUpdateDTO.getViewType() != 1 && surveyUpdateDTO.getIsTrain() == 0) {
            //查询投票的历史下发方案
            Long programmeId = resourceViewLimitService.getViewLimitIdByResourceId(surveyUpdateDTO.getId());
            //下发范围方案调整时,对调整的下发范围进行鉴权
            if (!Objects.equals(programmeId, surveyUpdateDTO.getProgrammeId())) {
                ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
                programmedIdQuery.setOldProgrammeId(programmeId);
                programmedIdQuery.setNewProgrammeId(surveyUpdateDTO.getProgrammeId());
                boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
                if (!checkViewLimit) {
                    throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
                }
            }
        }
    }

    private void sendPushFeignUpdate(SurveyUpdateDTO saveDTO) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = saveDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(saveDTO.getIsTrain()).orElse(0);
        PushResourceDTO pushResourceDTO = new PushResourceDTO().setResourceId(pushNoticeSetDTO.getResourceId())
            .setIsTrain(isTrain).setOperateState(1).setProgrammeId(saveDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;
        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split(StringPool.UNDERSCORE);
            String[] resourceId = pushNoticeSetDTO.getResourceId().split(StringPool.UNDERSCORE);
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);

            // 此时中 pushNoticeSetDTO 前端只会传 projectId_ 所以拼接好id
            pushResourceDTO.setResourceId(resourceId[0] + StringPool.UNDERSCORE + saveDTO.getId());
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
            pushResourceDTO.setResourceId(saveDTO.getId());
        }

        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setStartTime(saveDTO.getStartTime());
        pushAttributeDTO.setEndTime(saveDTO.getEndTime());
        pushAttributeDTO.setIntro(saveDTO.getDescription());

        String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);

        // 发送资源操作事件消息
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(saveDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue()) ? OperationEnum.PUBLISH_CANCEL
                : OperationEnum.PUBLISH, PushType.SURVEY.getKey(), saveDTO.getId()));
    }

    @Override
    public void removeSurveyByIdList(List<String> idList) {
        List<Survey> delSurveyList = listByIds(idList);
        if (!CollectionUtils.isEmpty(
            delSurveyList.stream().filter(s -> GeneralJudgeEnum.CONFIRM.getValue().equals(s.getPublished()))
                .collect(Collectors.toSet()))) {
            throw new BusinessException(SurveyErrorNoEnum.SELECT_CONTAINS_PUBLISHED);
        }
        List<Survey> surveyList = listByIds(idList);

        removeBatchByIds2(idList);

        // 发送消息，同步状态到岗位发展管理
        ActivityStatusChangeEvent.sendMsg(idList, ResourceTypeCodeEnum.SURVEY, null, 1, rabbitMqProducer);

        // 发送资源修改信息
        mqProducer.sendMsg(
            new ResourceChangeEvent(FirstInfoContentEnum.survey.name(), idList, GeneralJudgeEnum.CONFIRM.getValue(),
                GeneralJudgeEnum.NEGATIVE.getValue()));

        ISurveyService surveyService = SpringUtil.getBean(SURVEY_SERVICE_BEAN_NAME, ISurveyService.class);
        delSurveyList.forEach(Objects.requireNonNull(surveyService)::delLog);

        // 发送资源操作事件消息
        surveyList.forEach(survey -> {
            mqProducer.sendMsg(
                new ResourceOperateEvent(OperationEnum.DELETE, PushType.SURVEY.getKey(), survey.getId()));

            surveyViewLimitComponent.delViewLimit(survey.getId());

            // 发送资源同步事件消息
            if (Objects.equals(survey.getTrain(), IsTrainEnum.ITSELF.getValue())) {
                mqProducer.sendMsg(new ResourceSyncEvent(
                    new ResourceSyncDTO(OperationEnum.UPDATE,
                        ResourceTypeEnum.SURVEY.name(),
                        survey.getId(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        1,
                        null,
                        null,
                        UserThreadContext.getUserId(),
                        new Date())));
            }
        });
    }

    @Override
    @Log(type = Type.DELETE, targetId = "#survey.id", targetName = "#survey.surveyName", targetType = Log.TargetType.SURVEY)
    public void delLog(Survey survey) {
        // 此处无方法调用
    }

    @Override
    public PageInfo<SurveyListDTO> getSurveyPageInfo(SurveyListQuery surveyListQuery) {
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        surveyListQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        surveyListQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        surveyListQuery.setCurrentUserId(UserThreadContext.getUserId());

        PageInfo<SurveyListDTO> pageInfo = PageMethod.startPage(surveyListQuery.getPageNo(),
            surveyListQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.getSurveyManageList(surveyListQuery));

        Set<String> publishUserIdList = pageInfo.getList().stream().map(SurveyListDTO::getPublishBy)
            .collect(Collectors.toSet());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(publishUserIdList);
        pageInfo.getList().forEach(dto -> {
            dto.setViewResult(
                GeneralJudgeEnum.CONFIRM.getValue().equals(dto.getPublished()) ? SurveyConstant.VIEW_RESULT_YES
                    : SurveyConstant.VIEW_RESULT_NO);
            dto.setQuestionCount(surveyQuestionService.getQuestionCountBySurveyId(dto.getId()));
            if (dto.getEndTime() != null) {
                dto.setOverdue(dto.getEndTime().getTime() < System.currentTimeMillis() ? SurveyConstant.OVER_DUE_YES
                    : SurveyConstant.OVER_DUE_NO);
            }
            Optional.ofNullable(userMap.get(dto.getPublishBy()))
                .ifPresent(userInfo -> dto.setPublishBy(userInfo.getFullName()));
        });

        // 路由访问埋点
        mqProducer.sendMsg(
            new HomeRouterVisitEvent(RouterVisitEnum.OnlineSurvey.getRouterId(), UserThreadContext.getUserId(),
                RouterVisitEnum.OnlineSurvey.getName()));

        return pageInfo;
    }

    @Override
    public void publishSurvey(List<String> idList, Integer published) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new BusinessException(SurveyErrorNoEnum.ILLEGAL_TO_SUBMIT);
        }
        if (SurveyConstant.PUBLISH_YES == published) {
            update(new LambdaUpdateWrapper<Survey>().in(Survey::getId, idList)
                .ne(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY.getCode()).set(Survey::getPublished, published)
                .set(Survey::getPublishTime, new Date()).set(Survey::getPublishBy, UserThreadContext.getUserId()));
        } else {
            update(new LambdaUpdateWrapper<Survey>().in(Survey::getId, idList)
                .ne(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY.getCode()).set(Survey::getPublished, published)
                .set(Survey::getPublishTime, null).set(Survey::getPublishBy, null));
        }
        // 发送消息，同步状态到岗位发展管理
        ActivityStatusChangeEvent.sendMsg(idList, ResourceTypeCodeEnum.SURVEY, published, null, rabbitMqProducer);

        // 发送资源修改信息
        mqProducer.sendMsg(
            new ResourceChangeEvent(FirstInfoContentEnum.survey.name(), idList, GeneralJudgeEnum.NEGATIVE.getValue(),
                published));

        // 发送资源操作事件消息
        List<Survey> surveyList = listByIds(idList);
        surveyList.forEach(survey -> {
            mqProducer.sendMsg(new ResourceOperateEvent(
                Objects.equals(published, GeneralJudgeEnum.NEGATIVE.getValue()) ? OperationEnum.PUBLISH_CANCEL
                    : OperationEnum.PUBLISH, PushType.SURVEY.getKey(), survey.getId()));

            // 发送资源同步事件消息
            if (Objects.equals(survey.getTrain(), IsTrainEnum.ITSELF.getValue())) {
                mqProducer.sendMsg(new ResourceSyncEvent(
                    new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.SURVEY.name(), survey.getId(), null,
                        null, null,
                        null, published, null, null, null, UserThreadContext.getUserId(), new Date())));
            }
        });

    }

    @Override
    public SurveyInfoDTO getSurveyInfo(String id) {
        Survey survey = baseMapper.selectById(id);
        if (null == survey || survey.getSourceType().equals(SurveySourceTypeEnum.LIBRARY.getCode())) {
            throw new BusinessException(SurveyErrorNoEnum.SURVEY_ERROR_NO_EXIST);
        }

        // 调研库
        Survey surveyLib = baseMapper.selectById(survey.getQuoteLibId());

        return new SurveyInfoDTO().setSurveyNo(survey.getSurveyNo()).setId(survey.getId())
            .setSurveyName(survey.getSurveyName()).setDescription(survey.getDescription())
            .setSourceType(survey.getSourceType()).setLimit(surveyViewLimitComponent.getViewLimitBaseInfo(id))
            .setStartTime(survey.getStartTime()).setEndTime(survey.getEndTime()).setViewResult(survey.getViewResult())
            .setSurveyLibId(surveyLib == null ? "" : surveyLib.getId())
            .setSurveyLibName(surveyLib == null ? "" : surveyLib.getSurveyName()).setIsPublish(survey.getPublished())
            .setIsTrain(survey.getTrain()).setSortNo(survey.getSortNo()).setOrgId(survey.getOrgId())
            .setOrgName(orgFeign.getById(survey.getOrgId()).getOrgName()).setCreateBy(survey.getCreateBy())
            .setViewType(survey.getViewType());
    }

    @Override
    public SurveyPreviewDTO previewSurveyOrLibrary(Survey survey) {
        // 先查缓存
        SurveyPreviewDTO redisValue = (SurveyPreviewDTO) redisTemplate.opsForValue()
            .get(SurveyConstant.SURVEY_QUESTION_CACHE + ":" + UserThreadContext.getTenantId() + ":" + survey.getId());
        if (null != redisValue) {
            return redisValue;
        }
        // 导入调研或调研库直接查表 引入调研拿引入的id查表
        SurveyPreviewDTO surveyPreviewDTO = new SurveyPreviewDTO().setSurveyName(survey.getSurveyName())
            .setId(survey.getId()).setQuestionList(surveyQuestionService.getPreviewQuestionDTOListBySurveyId(
                survey.getSourceType().equals(SurveySourceTypeEnum.LIBRARY_QUOTE.getCode()) ? survey.getQuoteLibId()
                    : survey.getId()));

        redisTemplate.opsForValue().setIfAbsent(
            SurveyConstant.SURVEY_QUESTION_CACHE + ":" + UserThreadContext.getTenantId() + ":" + survey.getId(),
            surveyPreviewDTO);
        return surveyPreviewDTO;
    }

    @Override
    public SurveyAnalysisDTO analyseSurvey(String id,boolean isExport) {
        // 校验调研是否存在
        Survey survey = checkSurvey(id, false);
        // 判断调研添加方式
        List<SurveyAnalysisQuestionDTO> surveyAnalysisQuestionDTOList = null;
        // 导入调研直接查表
        if (survey.getSourceType().equals(SurveySourceTypeEnum.IMPORT.getCode())) {
            surveyAnalysisQuestionDTOList = surveyQuestionService.getSurveyAnalysisQuestionDTOListBySurveyId(id, id,isExport);
        }
        // 引入调研拿引入的id查表
        else if (survey.getSourceType().equals(SurveySourceTypeEnum.LIBRARY_QUOTE.getCode())) {
            surveyAnalysisQuestionDTOList = surveyQuestionService.getSurveyAnalysisQuestionDTOListBySurveyId(
                survey.getQuoteLibId(), id,isExport);
        }
        return new SurveyAnalysisDTO().setQuestionList(surveyAnalysisQuestionDTOList)
            .setSurveyName(survey.getSurveyName()).setDescription(survey.getDescription())
            .setSurveyCount(surveyRecordService.getCountBySurveyId(survey.getId())).setId(id)
            .setStartTime(survey.getStartTime()).setEndTime(survey.getEndTime());
    }

    @Override
    public PageInfo<SurveyLibraryListDTO> getSurveyLibraryPage(SurveyLibraryListQuery surveyLibraryListQuery) {
        // 参数
        Integer pageNo = surveyLibraryListQuery.getPageNo();
        Integer pageSize = surveyLibraryListQuery.getPageSize();

        // 当前登录用户
        surveyLibraryListQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        surveyLibraryListQuery.setCurrentUserId(UserThreadContext.getUserId());

        // 管辖范围
        surveyLibraryListQuery.setManagerAreaLevelPath(
            orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId()));

        // 2 查出原始分页数据
        PageInfo<Survey> sqlPageInfo = PageMethod.startPage(pageNo, pageSize)
            .doSelectPageInfo(() -> baseMapper.getSurveyLibraryPage(surveyLibraryListQuery));

        // 3 拼装返回结果
        List<SurveyLibraryListDTO> surveyLibraryListDTOList = sqlPageInfo.getList().stream().map(
            survey -> new SurveyLibraryListDTO().setId(survey.getId()).setSurveyName(survey.getSurveyName())
                .setCreateTime(survey.getCreateTime()).setCreateBy(userFeign.getUserFullNameById(survey.getCreateBy()))
                .setCategoryName(surveyCategoryService.getCategoryNameById(survey.getLibCategoryId()))
                .setCategoryId(survey.getLibCategoryId()).setAvailable(survey.getAvailable())
                .setQuestionCount(surveyQuestionService.getQuestionCountBySurveyId(survey.getId()))
                .setUpdateTime(survey.getUpdateTime()).setUpdateBy(userFeign.getUserFullNameById(survey.getUpdateBy()))
                .setOrgId(survey.getOrgId()).setOrgName(survey.getOrgName())).collect(Collectors.toList());

        // 4 分页
        PageInfo<SurveyLibraryListDTO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(sqlPageInfo, resultPageInfo);
        resultPageInfo.setList(surveyLibraryListDTOList);

        //路由访问埋点
        mqProducer.sendMsg(
            new HomeRouterVisitEvent(RouterVisitEnum.SurveyDepot.getRouterId(), UserThreadContext.getUserId(),
                RouterVisitEnum.SurveyDepot.getName()));

        return resultPageInfo;
    }

    @Override
    public SurveyLibraryToUpdateInfoDTO toEditSurveyLibrary(String id) {
        // 校验调研是否存在
        Survey survey = checkSurvey(id, false);
        return new SurveyLibraryToUpdateInfoDTO().setSurveyName(survey.getSurveyName())
            .setCategoryId(survey.getLibCategoryId());
    }

    @Override
    public void availableSurveyLibrary(List<String> idList, Integer available) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        if (available.equals(SurveyConstant.AVAILABLE_NO)) {
            // 若是禁用，则判断是否有被引用的调研库
            Long count = baseMapper.selectCount(new LambdaUpdateWrapper<Survey>().in(Survey::getQuoteLibId, idList)
                .eq(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY_QUOTE.getCode()));
            if (count > 0) {
                throw new BusinessException(SurveyErrorNoEnum.SELECT_CONTAINS_QUOTE);
            }
        }

        // 记录调研库操作
        List<Survey> surveys = baseMapper.selectList(new LambdaQueryWrapper<Survey>().in(Survey::getId, idList)
            .eq(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY.getCode()));
        String nameString = getNameString(surveys);
        baseLibraryRecordService.saveOperationRecord(nameString, surveys.size(),
            available == SurveyConstant.AVAILABLE_YES ? HandleTypeEnum.TP_ENABLE_RECORD
                : HandleTypeEnum.TP_DISABLE_RECORD, LibraryTypeEnum.LIB_SURVEY);

        update(new LambdaUpdateWrapper<Survey>().eq(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY.getCode())
            .in(Survey::getId, idList).set(Survey::getAvailable, available));
    }

    /**
     * 获取资源名称字符串，多个名称之间以“,”分隔
     *
     * @param list 资源列表
     * @return
     */
    private String getNameString(List<Survey> list) {
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(Survey::getSurveyName)
            .collect(Collectors.joining(StringPool.COMMA));
    }

    @Override
    public void deleteSurveyLibrary(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }

        // 判断是否有已启用的调研库
        Long count = baseMapper.selectCount(
            new LambdaUpdateWrapper<Survey>().eq(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY.getCode())
                .eq(Survey::getAvailable, SurveyConstant.AVAILABLE_YES).in(Survey::getId, idList));
        if (count > 0) {
            throw new BusinessException(SurveyErrorNoEnum.SELECT_CONTAINS_AVAILABLE);
        }
        LambdaUpdateWrapper<Survey> wrapper = new LambdaUpdateWrapper<Survey>().in(Survey::getId, idList)
            .eq(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY.getCode());
        List<Survey> surveys = baseMapper.selectList(wrapper);

        // 记录资源库操作
        String nameString = getNameString(surveys);
        if (!surveys.isEmpty()) {
            baseLibraryRecordService.saveOperationRecord(nameString, surveys.size(),
                HandleTypeEnum.TP_DELETE_RECORD, LibraryTypeEnum.LIB_SURVEY);
        }
        baseMapper.delete(wrapper);
    }

    @Override
    public void updateSurveyLibrary(String id, SurveyLibraryUpdateDTO surveyLibraryUpdateDTO) {
        Survey survey = baseMapper.selectOne(
            new LambdaUpdateWrapper<Survey>().eq(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY.getCode())
                .eq(Survey::getId, id));
        if (null == survey) {
            throw new BusinessException(SurveyErrorNoEnum.SURVEY_ERROR_NO_EXIST);
        }
        String surveyName = surveyLibraryUpdateDTO.getSurveyName();
        String categoryId = surveyLibraryUpdateDTO.getCategoryId();

        LambdaUpdateWrapper<Survey> updateWrapper = new LambdaUpdateWrapper<Survey>().eq(Survey::getSourceType,
                SurveySourceTypeEnum.LIBRARY.getCode()).eq(Survey::getId, id).set(Survey::getSurveyName, surveyName)
            .set(Survey::getLibCategoryId, categoryId)
            .set(StringUtils.isNotBlank(surveyLibraryUpdateDTO.getOrgId()), Survey::getOrgId,
                surveyLibraryUpdateDTO.getOrgId());

        // 记录资源库操作
        baseLibraryRecordService.saveOperationRecord(survey.getSurveyName(), 1, HandleTypeEnum.TP_UPDATE_RECORD,
            LibraryTypeEnum.LIB_SURVEY);

        update(new Survey(), updateWrapper);

        // 删除缓存
        redisTemplate.delete(SurveyConstant.SURVEY_QUESTION_CACHE + ":" + UserThreadContext.getTenantId() + ":" + id);
    }

    @Override
    public void saveSurveyLibrary(SurveyLibrarySaveDTO surveyLibrarySaveDTO) {
        // 参数
        String surveyName = surveyLibrarySaveDTO.getSurveyName();
        String surveyExcelPath = surveyLibrarySaveDTO.getSurveyExcelPath();
        String categoryId = surveyLibrarySaveDTO.getCategoryId();
        Integer available = surveyLibrarySaveDTO.getAvailable();
        String id = StringUtil.newId();

        ImportDataDTO importData = importDataFeign.getImportData(surveyExcelPath);
        ParseQuestionExcelDTO parseQuestionExcelDTO = parseQuestionExcel(importData.getExcel(), id);

        surveyQuestionService.saveBatch(parseQuestionExcelDTO.getQuestions());
        surveyQuestionOptionService.saveBatch(parseQuestionExcelDTO.getOptions());

        Survey survey = new Survey().setLibCategoryId(categoryId).setSurveyName(surveyName).setId(id)
            .setOrgId(UserThreadContext.getOrgId()).setSourceType(SurveySourceTypeEnum.LIBRARY.getCode())
            .setAvailable(available);

        // 记录资源库操作
        baseLibraryRecordService.saveOperationRecord(surveyName, 1, HandleTypeEnum.TP_ADD_RECORD,
            LibraryTypeEnum.LIB_SURVEY);
        // 调研库没有发布/未发布，但是预览会校验调研的发布状态，这里默认发布
        survey.setPublished(1);
        this.save(survey);
    }

    @Override
    public boolean checkCategoryQuoteByIdList(List<String> idList) {
        Long count = baseMapper.selectCount(
            new LambdaQueryWrapper<Survey>().eq(Survey::getSourceType, SurveySourceTypeEnum.LIBRARY.getCode())
                .in(Survey::getLibCategoryId, idList));
        return count > 0;
    }

    @Override
    public PageInfo<SurveyListApiDTO> getSurveyApiList(SurveyListApiQuery surveyListQuery) {
        surveyListQuery.setUserId(UserThreadContext.getUserId());
        // 分页查询调研列表
        PageInfo<SurveyListApiDTO> pageInfo = PageMethod.startPage(surveyListQuery.getPageNo(),
                surveyListQuery.getPageSize(), surveyListQuery.isCount())
            .doSelectPageInfo(() -> baseMapper.getSurveyList(surveyListQuery));
        pageInfo.setIsLastPage(pageInfo.getList().size() != surveyListQuery.getPageSize());
        // 响应参数处理
        pageInfo.getList().forEach(surveyListApiDTO -> {
            SurveyListApiDTO dto = baseMapper.getSurveyCashById(surveyListApiDTO.getId());
            CopyNotNullObjectUtil.copyProperties(dto, surveyListApiDTO);
            surveyListApiDTO.setQuestionCount(
                surveyQuestionService.getQuestionCountBySurveyId(surveyListApiDTO.getId()));
            surveyListApiDTO.setFinished(surveyListQuery.getFinished());
            surveyListApiDTO.setViewResult(surveyListApiDTO.getViewResult() == SurveyConstant.VIEW_RESULT_YES
                && surveyListQuery.getFinished() == SurveyConstant.FINISHED_YES ? SurveyConstant.VIEW_RESULT_YES
                : SurveyConstant.VIEW_RESULT_NO);
        });
        return pageInfo;
    }

    @Override
    public SurveyInfoApiDTO getSurveyApiInfo(String id, Integer isIgnoreView) {
        // 校验调研是否存在
        Survey survey = checkSurvey(id, true);
        // 校验可见范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            checkPermission(id);
        }
        // 查指定用户指定调研的调研提交记录
        SurveyRecord surveyRecord = surveyRecordService.getRecordBySurveyIdAndUserId(survey.getId(),
            UserThreadContext.getUserId());
        // 响应数据对象处理
        SurveyInfoApiDTO surveyInfoApiDTO = new SurveyInfoApiDTO();
        surveyInfoApiDTO.setId(survey.getId());
        surveyInfoApiDTO.setSurveyName(survey.getSurveyName());
        surveyInfoApiDTO.setExpireDate(survey.getEndTime());
        surveyInfoApiDTO.setQuestionCount(surveyQuestionService.getQuestionCountBySurveyId(survey.getId()));
        surveyInfoApiDTO.setFinishedCount(Math.toIntExact(survey.getFinishedCount()));
        surveyInfoApiDTO.setDescription(survey.getDescription());
        surveyInfoApiDTO.setFinished(null == surveyRecord ? SurveyConstant.FINISHED_NO : surveyRecord.getFinished());
        surveyInfoApiDTO.setFinishedTime(null == surveyRecord ? null : surveyRecord.getUpdateTime());
        surveyInfoApiDTO.setViewResult(surveyInfoApiDTO.getFinished() == SurveyConstant.FINISHED_YES
            && survey.getViewResult() == SurveyConstant.VIEW_RESULT_YES ? SurveyConstant.VIEW_RESULT_YES
            : SurveyConstant.VIEW_RESULT_NO);
        // 是否显示激励
        surveyInfoApiDTO.setShowExcitation(1);
        return surveyInfoApiDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SurveyPreviewDTO getSurveyQuestionsApiInfo(String id, Integer isIgnoreView) {
        String userId = UserThreadContext.getUserId();
        // 校验调研是否存在
        Survey survey = checkSurvey(id, true);
        // 校验可见范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            checkPermission(id);
        }
        // 调研时间校验
        long now = System.currentTimeMillis();
        if (null != survey.getStartTime()) {
            long startTime = survey.getStartTime().getTime();
            if (now < startTime) {
                throw new BusinessException(TrainErrorNoEnum.ERR_ACTIVITY_NOT_START,
                    null, DateUtil.formatDate(survey.getStartTime(), DateHelper.YYYYMMDD_HHMMSS),
                    DateUtil.formatDate(survey.getEndTime(), DateHelper.YYYYMMDD_HHMMSS));
            }
        }
        if (null != survey.getEndTime()) {
            long endTime = survey.getEndTime().getTime();
            if (now > endTime) {
                throw new BusinessException(TrainErrorNoEnum.ERR_ACTIVITY_END,
                    null, DateUtil.formatDate(survey.getStartTime(), "yyyy-MM-dd HH:mm:ss"),
                    DateUtil.formatDate(survey.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        // 保存调研记录
        SurveyRecord surveyRecord = surveyRecordService.getRecordBySurveyIdAndUserId(id, userId);
        if (surveyRecord == null) {
            surveyRecord = new SurveyRecord();
            surveyRecord.setId(StringUtil.newId());
            surveyRecord.setSurveyId(id);
            surveyRecord.setCreateBy(userId);
            surveyRecord.setFinished(SurveyConstant.FINISHED_NO);
            surveyRecord.setIsDel(DelEnum.NOT_DELETE.getValue());
            surveyRecordService.save(surveyRecord);

            // 发送资源同步事件消息
            if (Objects.equals(survey.getTrain(), IsTrainEnum.ITSELF.getValue())) {
                mqProducer.sendMsg(new ResourceRecordSyncEvent(
                    new ResourceRecordSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.SURVEY.name(),
                        surveyRecord.getId(),
                        survey.getId(), UserThreadContext.getUserId(), 0, UserThreadContext.getUserId(), new Date(),
                        UserThreadContext.getUserId(), new Date())));
            }
        }
        if (surveyRecord.getFinished().equals(SurveyConstant.FINISHED_YES)) {
            throw new BusinessException(SurveyErrorNoEnum.SURVEY_ERROR_HAS_JOIN);
        }
        return this.previewSurveyOrLibrary(survey);
    }

    @Override
    public SurveyResultDTO getSurveyResult(String id, Integer isIgnoreView) {
        // 校验调研是否存在
        Survey survey = checkSurvey(id, true);
        // 校验可见范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            checkPermission(id);
        }
        // 是否可以查看调研结果
        Integer viewResult = survey.getViewResult();
        if (viewResult.equals(SurveyConstant.VIEW_RESULT_NO)) {
            throw new BusinessException(SurveyErrorNoEnum.SURVEY_CAN_NOT_VIEW_RESULT);
        }
        List<SurveyQuestionResultDTO> questionResultDTOList;
        if (survey.getSourceType().equals(SurveySourceTypeEnum.LIBRARY_QUOTE.getCode())) {
            questionResultDTOList = surveyQuestionService.getSurveyResultQuestionDTOListBySurveyId(id,
                survey.getQuoteLibId());
        } else {
            questionResultDTOList = surveyQuestionService.getSurveyResultQuestionDTOListBySurveyId(id, id);
        }

        return new SurveyResultDTO().setSurveyName(survey.getSurveyName()).setId(survey.getId())
            .setDescription(survey.getDescription()).setStartTime(survey.getStartTime()).setEndTime(survey.getEndTime())
            .setQuestionList(questionResultDTOList).setCount(surveyRecordService.getCountBySurveyId(id));
    }

    @Override
    public SurveyResultDTO answerInfo(String surveyId, String userId) {
        Survey survey = Optional.ofNullable(getById(surveyId))
            .orElseThrow(() -> new BusinessException(SurveyErrorNoEnum.SURVEY_ERROR_NO_EXIST));
        List<SurveyQuestionResultDTO> questionResultDTOList;
        questionResultDTOList = surveyQuestionService.getUserSurveyResultList(surveyId, userId);
        return new SurveyResultDTO().setSurveyName(survey.getSurveyName()).setId(survey.getId())
            .setDescription(survey.getDescription()).setStartTime(survey.getStartTime()).setEndTime(survey.getEndTime())
            .setQuestionList(questionResultDTOList).setCount(surveyRecordService.getCountBySurveyId(surveyId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(String id, Integer isIgnoreView, SurveySubmitDTO surveySubmitDTO) {
        // 校验调研是否存在
        Survey survey = checkSurvey(id, true);
        // 校验可见范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            checkPermission(id);
        }
        // 获取用户调研记录
        SurveyRecord surveyRecord = surveyRecordService.getRecordBySurveyIdAndUserId(id, UserThreadContext.getUserId());
        if (surveyRecord == null) {
            throw new BusinessException(SurveyErrorNoEnum.ILLEGAL_TO_SUBMIT);
        }
        if (surveyRecord.getFinished().equals(SurveyConstant.FINISHED_YES)) {
            throw new BusinessException(ErrorNoEnum.ERR_REPEAT_SUBMIT);
        }
        // 保存用户答题详情
        SurveyPreviewDTO surveyPreviewDTO = this.previewSurveyOrLibrary(survey);
        List<SurveyRecordDetail> detailList = new ArrayList<>();
        List<SurveyPreviewQuestionDTO> allQuestionList = surveyPreviewDTO.getQuestionList();
        List<SurveyQuestionSubmitDTO> submitQuestionList = surveySubmitDTO.getQuestionList();
        for (SurveyPreviewQuestionDTO questionDTO : allQuestionList) {
            // 遍历所有题目 生成一条答题详情记录
            setUserAnswer(id, questionDTO, submitQuestionList, surveyRecord, detailList);
        }
        surveyRecordDetailService.saveBatch(detailList);
        surveyRecord.setFinished(SurveyConstant.FINISHED_YES);
        surveyRecord.setUpdateTime(new Date());
        surveyRecordService.updateById(surveyRecord);
        // 发送[完成调研]事件
        String userId = UserThreadContext.getUserId();
        mqProducer.sendMsg(new ExcitationMQEvent(
            new ExcitationMQEventDTO().setUserId(userId).setEventId(ExcitationEventEnum.finishSurvey.name())
                .setTargetName(survey.getSurveyName()).setTargetId(survey.getId()).setIsTrain(survey.getTrain())));
        // 发送调研完成事件
        log.info("发送调研完成事件!");
        mqProducer.send(new SurveyFinishEvent(id, userId, userId));

        // 发送资源同步事件消息
        if (Objects.equals(survey.getTrain(), IsTrainEnum.ITSELF.getValue())) {
            mqProducer.sendMsg(new ResourceRecordSyncEvent(
                new ResourceRecordSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.SURVEY.name(), surveyRecord.getId(),
                    survey.getId(), UserThreadContext.getUserId(), 1, UserThreadContext.getUserId(), new Date(),
                    UserThreadContext.getUserId(), new Date())));
        }
    }

    /**
     * 遍历所有题目 生成一条答题详情记录
     *
     * @param id 调研id
     * @param questionDTO 调研预览题目返回对象
     * @param submitQuestionList 调研题目提交对象
     * @param surveyRecord 调研记录表
     * @param detailList 调研记录详情表
     */
    private static void setUserAnswer(String id, SurveyPreviewQuestionDTO questionDTO,
        List<SurveyQuestionSubmitDTO> submitQuestionList, SurveyRecord surveyRecord,
        List<SurveyRecordDetail> detailList) {
        // 遍历所有题目 生成一条答题详情记录
        for (SurveyQuestionSubmitDTO questionSubmitDTO : submitQuestionList) {
            // 遍历用户提交的答题详情，题目id调研预览时id一样的，则根据类型设置用户答案
            if (questionDTO.getQuestionId().equals(questionSubmitDTO.getQuestionId())) {
                if (questionDTO.getQuestionType()
                    .equals(SurveyQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.getCode())) {
                    for (String answer : questionSubmitDTO.getAnswer()) {
                        SurveyRecordDetail surveyRecordDetail = new SurveyRecordDetail().setSurveyId(id)
                            .setAnswer(null).setIsDel(0).setAnswer("").setQuestionId(questionDTO.getQuestionId())
                            .setRecordId(surveyRecord.getId());
                        surveyRecordDetail.setId(StringUtil.newId());
                        surveyRecordDetail.setAnswer(answer);
                        detailList.add(surveyRecordDetail);
                    }
                } else {
                    SurveyRecordDetail surveyRecordDetail = new SurveyRecordDetail().setSurveyId(id).setAnswer(null)
                        .setIsDel(0).setAnswer("").setQuestionId(questionDTO.getQuestionId())
                        .setRecordId(surveyRecord.getId());
                    surveyRecordDetail.setId(StringUtil.newId());
                    surveyRecordDetail.setAnswer(questionSubmitDTO.getAnswer().get(0));
                    detailList.add(surveyRecordDetail);
                }
                break;
            }
        }
    }

    @Override
    public SurveyLibInfoDTO getSurveyLibInfo(String id) {
        Survey survey = baseMapper.selectById(id);
        if (null == survey || !survey.getSourceType().equals(SurveySourceTypeEnum.LIBRARY.getCode())) {
            throw new BusinessException(SurveyErrorNoEnum.SURVEY_ERROR_NO_EXIST);
        }
        return new SurveyLibInfoDTO().setId(survey.getId()).setSurveyName(survey.getSurveyName())
            .setDescription(survey.getDescription()).setSortNo(survey.getSortNo());
    }

    @Override
    public Integer getSurveyFinishNum(String userId, Collection<String> surveyIds) {
        return baseMapper.getSurveyFinishNum(userId, surveyIds);
    }

    @Override
    public CerDitchDTO getDitch(String contentId) {
        return baseMapper.getDitch(contentId);
    }

    @Override
    public List<LearningCalendarTaskDTO> findLearningCalenderSurveyTaskList(
        LearningCalendarTaskQuery learningCalendarTaskQuery) {
        return baseMapper.selectProTaskSurveyListByTime(learningCalendarTaskQuery);
    }

    @Override
    public List<String> getInvalidSurveyId(Collection<String> surveyIdList) {
        return baseMapper.getInvalidSurveyId(surveyIdList);
    }

    /**
     * 校验下发范围
     *
     * @param id 调研id
     */
    private void checkPermission(String id) {
        String userId = UserThreadContext.getUserId();
        boolean b = surveyViewLimitComponent.checkViewLimit(userId, id);
        if (!b) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
    }

    /**
     * 解析调研问卷文件题目并保存
     *
     * @param excel    调研问卷文件中的数据
     * @param surveyId 调研id
     * @return 存有题目列表和选项列表的map
     */
    private ParseQuestionExcelDTO parseQuestionExcel(String[][] excel, String surveyId) {

        // 校验问卷文件是否符合规范
        String[] header = ExcelTitleBaseCheckUtil.SURVEY_IMPORT_EXCEL;
        ExcelTitleBaseCheckUtil.baseCheck(excel, header);

        // 遍历excel 保存题目和选项
        List<SurveyQuestion> questions = new ArrayList<>();
        List<SurveyQuestionOption> options = new ArrayList<>();
        if (null == excel) {
            throw new BusinessException(SurveyErrorNoEnum.EXCEL_NOT_EXIST);
        }
        ExcelCheckMessage surveyCheck = new SurveyExcelTemplate().check(excel);
        if (!surveyCheck.getMessage().isEmpty()) {
            throw new BusinessException(SurveyErrorNoEnum.SURVEY_EXCEL_IMPORT_FAIL, null, JsonUtil.objToJson(surveyCheck.getMessage()));
        }
        for (int i = 1; i < excel.length; i++) {
            String questionId = StringUtil.newId();
            SurveyQuestion surveyQuestion = new SurveyQuestion().setSurveyId(surveyId).setId(questionId).setSortNo(i)
                .setAvailable(SurveyConstant.AVAILABLE_YES).setQuestionNo(i);
            checkExcelInfo(excel, i);
            surveyQuestion.setQuestionName(excel[i][0]);
            surveyQuestion.setQuestionCategory(excel[i][1]);
            Integer questionType = SurveyQuestionTypeEnum.getCodeByDesc(excel[i][2]);
            surveyQuestion.setQuestionType(questionType);
            questions.add(surveyQuestion);

            // 若包含选择题则保存选项
            if (questionType.equals(SurveyQuestionTypeEnum.QUESTION_TYPE_RADIO.getCode()) || questionType.equals(
                SurveyQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.getCode()) || questionType.equals(
                SurveyQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.getCode())) {
                // 换行切割选项
                String questionString = excel[i][3];
                String[] optionArray = questionString.split("\n");
                for (int j = 0; j < optionArray.length; j++) {
                    String optionCode = optionArray[j].substring(0, 1);
                    String optionDesc = optionArray[j].substring(1);
                    SurveyQuestionOption option = new SurveyQuestionOption().setQuestionId(questionId)
                        .setSurveyId(surveyId).setId(StringUtil.newId()).setSortNo(j)
                        .setAvailable(SurveyConstant.AVAILABLE_YES).setDescription(optionDesc).setCode(optionCode);
                    options.add(option);
                }
            }
        }
        return new ParseQuestionExcelDTO().setQuestions(questions).setOptions(options);
    }

    private static void checkExcelInfo(String[][] excel, int i) {
        if ("".equals(excel[i][0].trim())){
            throw new BusinessException(SurveyErrorNoEnum.ERR_SURVEY_QUESTION_NAME_NOT_EMPTY);
        }
        if ("".equals(excel[i][1].trim())){
            throw new BusinessException(SurveyErrorNoEnum.ERR_SURVEY_CATEGORY_NOT_EMPTY);
        }
        if (excel[i][0] != null && StringUtils.length(excel[i][0]) > 500) {
            throw new BusinessException(SurveyErrorNoEnum.ERR_SURVEY_QUESTION_NAME_TOO_LONG);
        }
        if (excel[i][1] != null && StringUtils.length(excel[i][1]) > 36) {
            throw new BusinessException(SurveyErrorNoEnum.ERR_SURVEY_QUESTION_CATEGORY_TOO_LONG);
        }
    }

    @Override
    public void surveyExportData(SurveyListQuery query) {
        IExportDataDTO exportDataDTO = new IExportDataDTO() {
            @Override
            public List<Map<String, Object>> getData(Integer pageNo, Integer pageSize) {
                ISurveyService service = SpringUtil.getBean(SURVEY_SERVICE_BEAN_NAME, ISurveyService.class);
                query.setExport(true);
                query.setPageNo(pageNo);
                query.setPageSize(pageSize);
                PageInfo<SurveyListDTO> pageInfo = null;
                if (service != null) {
                    pageInfo = service.getSurveyPageInfo(query);
                }
                List<Map<String, Object>> listExportDTOS = new ArrayList<>();

                if (pageInfo != null) {
                    for (SurveyListDTO dto : pageInfo.getList()) {
                        Map<String, Object> beanMap = JsonUtil.parseObjectToMap(dto);
                        String startTime = DateUtil.formatToStr(dto.getStartTime(), DateHelper.YYYYMMDD_HHMM);
                        String endTime = DateUtil.formatToStr(dto.getEndTime(), DateHelper.YYYYMMDD_HHMM);
                        beanMap.put("startAndEndTime", startTime.concat("~").concat(endTime));
                        listExportDTOS.add(beanMap);
                    }
                }
                return listExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Survey;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Survey.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportLib(SurveyLibraryListQuery surveyLibraryListQuery) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ISurveyService, SurveyLibraryListDTO>(
            surveyLibraryListQuery) {

            @Override
            protected ISurveyService getBean() {
                return SpringUtil.getBean(SURVEY_SERVICE_BEAN_NAME, ISurveyService.class);
            }

            @Override
            protected PageInfo<SurveyLibraryListDTO> getPageInfo() {
                return getBean().getSurveyLibraryPage((SurveyLibraryListQuery) pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.SurveyLib;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.SurveyLib.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<SurveyListApiDTO> searchSurvey(SurveySearchQuery surveySearchQuery) {
        SurveyListApiQuery surveyListApiQuery = new SurveyListApiQuery();
        BeanUtils.copyProperties(surveySearchQuery, surveyListApiQuery);
        surveyListApiQuery.setSurveyName(surveySearchQuery.getSearchKey());
        surveyListApiQuery.setFinished(0);
        surveyListApiQuery.setCount(true);
        return getSurveyApiList(surveyListApiQuery);
    }

    @Override
    @Async
    public void exportAnalyseSurveyData(String id) {
        IExportDataDTO exportDataDTO = new IExportDataDTO() {

            @Override
            public List<Map<String, Object>> getData(Integer pageNo, Integer pageSize) {
                return Collections.emptyList();
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.SurveyAnalysis;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.SurveyAnalysis.getType();
            }

        };
        SurveyAnalysisDTO data = this.analyseSurvey(id,true);
        List<SurveyAnalysisQuestionExportDTO> surveyAnalysisQuestionExportDTOList = new ArrayList<>();
        for (SurveyAnalysisQuestionDTO surveyAnalysisQuestionDTO : data.getQuestionList()) {

            Integer questionType = surveyAnalysisQuestionDTO.getQuestionType();
            SurveyAnalysisQuestionExportDTO surveyAnalysisQuestionExportDTO = new SurveyAnalysisQuestionExportDTO();
            surveyAnalysisQuestionExportDTO.setSortNo(surveyAnalysisQuestionDTO.getSortNo());
            surveyAnalysisQuestionExportDTO.setTitle(surveyAnalysisQuestionDTO.getQuestionDesc());
            surveyAnalysisQuestionExportDTO.setQuestionType(SurveyQuestionTypeEnum.getEventEnumByValue(questionType));

            if (SurveyQuestionTypeEnum.hasOption(questionType)) {
                int i = 0;
                // 处理客户答题选项 - 拆分方法，减少代码复杂度
                execByOptionList(surveyAnalysisQuestionDTO, i, surveyAnalysisQuestionExportDTO,
                    surveyAnalysisQuestionExportDTOList);
            } else {
                int j = 0;
                // 处理客户回答 - 拆分方法，减少代码复杂度
                execByAnswerList(surveyAnalysisQuestionDTO, j, surveyAnalysisQuestionExportDTO,
                    surveyAnalysisQuestionExportDTOList);
            }

        }
        List<Map<String, Object>> surveyAnalysisQuestionListExportDTOS = new ArrayList<>();
        for (SurveyAnalysisQuestionExportDTO surveyAnalysisQuestionExportDTO : surveyAnalysisQuestionExportDTOList) {
            Map<String, Object> beanMap = JsonUtil.parseObjectToMap(surveyAnalysisQuestionExportDTO);
            surveyAnalysisQuestionListExportDTOS.add(beanMap);
        }
        exportComponent.exportRecordByDataNoPage(exportDataDTO, surveyAnalysisQuestionListExportDTOS);
    }

    /**
     * 处理客户答题选项 - 拆分方法，减少代码复杂度
     *
     * @param surveyAnalysisQuestionDTO 调研分析题目列表对象
     * @param i 索引对象
     * @param surveyAnalysisQuestionExportDTO 导出调研分析结果
     * @param surveyAnalysisQuestionExportDTOList 导出调研分析结果
     */
    private static void execByOptionList(SurveyAnalysisQuestionDTO surveyAnalysisQuestionDTO, int i,
        SurveyAnalysisQuestionExportDTO surveyAnalysisQuestionExportDTO,
        List<SurveyAnalysisQuestionExportDTO> surveyAnalysisQuestionExportDTOList) {
        for (SurveyAnalysisQuestionOptionDTO surveyAnalysisQuestionOptionDTO : surveyAnalysisQuestionDTO.getQuestionOptionList()) {
            if (i == 0) {
                surveyAnalysisQuestionExportDTO.setQuestionOption(
                    surveyAnalysisQuestionOptionDTO.getOptionCode()
                        + surveyAnalysisQuestionOptionDTO.getOptionDesc());
                surveyAnalysisQuestionExportDTO.setTotalCount(
                    surveyAnalysisQuestionOptionDTO.getSelectTotalCount().toString());
                surveyAnalysisQuestionExportDTO.setRatio(surveyAnalysisQuestionOptionDTO.getSelectance() + "%");
                surveyAnalysisQuestionExportDTOList.add(surveyAnalysisQuestionExportDTO);
            } else {
                SurveyAnalysisQuestionExportDTO questionExportDTO = new SurveyAnalysisQuestionExportDTO();
                questionExportDTO.setQuestionOption(
                    surveyAnalysisQuestionOptionDTO.getOptionCode()
                        + surveyAnalysisQuestionOptionDTO.getOptionDesc());
                questionExportDTO.setTotalCount(
                    surveyAnalysisQuestionOptionDTO.getSelectTotalCount().toString());
                questionExportDTO.setRatio(
                    surveyAnalysisQuestionOptionDTO.getSelectance() + "%");
                surveyAnalysisQuestionExportDTOList.add(questionExportDTO);
            }
            i++;
        }
    }

    /**
     * 处理客户回答 - 拆分方法，减少代码复杂度
     *
     * @param surveyAnalysisQuestionDTO 调研分析题目列表对象
     * @param j 索引对象
     * @param surveyAnalysisQuestionExportDTO 导出调研分析结果
     * @param surveyAnalysisQuestionExportDTOList 导出调研分析结果
     */
    private static void execByAnswerList(SurveyAnalysisQuestionDTO surveyAnalysisQuestionDTO, int j,
        SurveyAnalysisQuestionExportDTO surveyAnalysisQuestionExportDTO,
        List<SurveyAnalysisQuestionExportDTO> surveyAnalysisQuestionExportDTOList) {
        for (String answer : surveyAnalysisQuestionDTO.getAnswerList()) {
            if (j == 0) {
                surveyAnalysisQuestionExportDTO.setQuestionOption(answer);
                surveyAnalysisQuestionExportDTOList.add(surveyAnalysisQuestionExportDTO);
            } else {
                SurveyAnalysisQuestionExportDTO surveyAnalysisQuestionExportDTO2 = new SurveyAnalysisQuestionExportDTO();
                surveyAnalysisQuestionExportDTO2.setQuestionOption(answer);
                surveyAnalysisQuestionExportDTOList.add(surveyAnalysisQuestionExportDTO2);
            }
            j++;
        }
    }

    @Override
    public Integer getMenuTaskCount(Integer status, String userId) {
        return baseMapper.getMenuTaskCount(status, userId);
    }

    @Override
    public PageInfo<SurveyDTO> getMenuTask(Integer status, BaseEntity entity) {
        return PageMethod.startPage(entity.getPageNo(), entity.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getMenuTask(status, entity.getCurrentUserId()));
    }

    @Override
    public PageInfo<SurveyRecordDetailDTO> getSurveyUserDetails(SurveyDetailListQuery query) {
        log.error("getSurveyUserDetails start"+JsonUtil.objToJson(query));
        PageInfo<SurveyRecordDetailDTO> objectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .setCount(false)
            .doSelectPageInfo(() -> baseMapper.querySurveyDetail(query));
        log.error("getSurveyUserDetails end"+JsonUtil.objToJson(query));



//        List<String> userIds = objectPageInfo.getList().stream().map(SurveyRecordDetailDTO::getCreateBy)
//            .filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toList());
//        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
//        Set<String> orgIds = userMap.values().stream().map(UserOrgDTO::getOrgId).collect(Collectors.toSet());
//        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
//        objectPageInfo.getList().forEach(surveyRecordDetailDTO -> {
//
//            //填充学员名及组织
//            UserOrgDTO userOrgDTO = userMap.get(surveyRecordDetailDTO.getCreateBy());
//            if (null != userOrgDTO) {
//                surveyRecordDetailDTO.setLoginName(userOrgDTO.getLoginName());
//                surveyRecordDetailDTO.setFullName(userOrgDTO.getFullName());
//                Optional.ofNullable(orgShowDTOMap.get(userOrgDTO.getOrgId())).ifPresent(orgShowDTO -> {
//                    surveyRecordDetailDTO.setOrgName(orgShowDTO.getOrgShortName());
//                    surveyRecordDetailDTO.setOrgPath(orgShowDTO.getLevelPathName());
//                });
//            }
//        });

        return objectPageInfo;
    }

    @Override
    public PageInfo<RemindListDTO> getRemindListById(RemindQueryDTO remindQueryDTO) {
        // 1 参数
        Integer pageNo = remindQueryDTO.getPageNo();
        Integer pageSize = remindQueryDTO.getPageSize();

        PageInfo<String> pageInfo;
        // 2 查询数据库
        if (StringUtils.isNotEmpty(remindQueryDTO.getUserIds())) {
            String[] stringArray = remindQueryDTO.getUserIds().split(",");
            remindQueryDTO.setUserIdsSet(Arrays.stream(stringArray).collect(Collectors.toSet()));
            pageInfo = PageMethod.startPage(remindQueryDTO.getPageNo(), remindQueryDTO.getPageSize()).doSelectPageInfo(
                () -> surveyViewLimitComponent.getViewLimitUserByUserIds(remindQueryDTO.getId(),
                    new ArrayList<>(remindQueryDTO.getUserIdsSet())));
        } else {
            pageInfo = PageMethod.startPage(remindQueryDTO.getPageNo(), remindQueryDTO.getPageSize())
                .doSelectPageInfo(() -> surveyViewLimitComponent.getViewLimitUser(remindQueryDTO.getId()));
        }
        Map<String, UserDTO> userDTOMap = userFeign.getUseListByIds(pageInfo.getList()).stream()
            .collect(Collectors.toMap(UserDTO::getId, Function.identity(), (k1, k2) -> k1));

        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(
            userDTOMap.values().stream().map(UserDTO::getOrgId).collect(Collectors.toSet()));
        // 3 拼接返回对象
        List<RemindListDTO> examRemindListDTOList = pageInfo.getList().stream().map(userId -> {
            UserDTO userById = userDTOMap.get(userId);
            Collection<String> surveyIds = new ArrayList<>();
            surveyIds.add(remindQueryDTO.getId());
            int overCount = getSurveyFinishNum(userId, surveyIds);
            String joinStatus = overCount > 0 ? "" : "未参加";

            return RemindListDTO.builder().fullName(userById.getFullName())
                .orgName(orgShowDTOMap.get(userById.getOrgId()).getOrgShortName())
                .orgPath(orgShowDTOMap.get(userById.getOrgId()).getLevelPathName()).loginName(userById.getLoginName())
                .joinStatus(joinStatus).userId(userById.getId()).build();
        }).filter(e -> StringUtils.isNotEmpty(e.getJoinStatus())).toList();

        // 4 返回对象分页
        PageInfo<RemindListDTO> resultPageInfo = new PageInfo<>(examRemindListDTOList);
        resultPageInfo.setTotal(pageInfo.getTotal());
        resultPageInfo.setPageNum(pageNo);
        resultPageInfo.setPageSize(pageSize);
        return resultPageInfo;
    }

    @Override
    public void exportSurveyDetailData(String surveyId) {
        Survey survey = baseMapper.selectById(surveyId);
        SurveyDetailListQuery queryDTO = new SurveyDetailListQuery();
        queryDTO.setSurveyId(surveyId);
        queryDTO.setSourceType(survey == null ? null : survey.getSourceType());
        log.info(JsonUtil.objToJson(queryDTO));

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ISurveyService, SurveyRecordDetailDTO>(queryDTO) {

            @Override
            protected ISurveyService getBean() {
                return SpringUtil.getBean(SURVEY_SERVICE_BEAN_NAME, ISurveyService.class);
            }

            @Override
            protected PageInfo<SurveyRecordDetailDTO> getPageInfo() {
                return getBean().getSurveyUserDetails((SurveyDetailListQuery) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.SurveyRecordDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.SurveyRecordDetail.getType();
            }

            @Override
            public int getPageNo() {
                return 40000;
            }

            @Override
            public int getMaxPage(){ return 25; }

        };

        exportComponent.exportRecord(exportDataDTO);

    }

    @Override
    public Integer getSurveyRecordCount(String userId, String taskContent) {
        return baseMapper.getSurveyRecordCount(userId, taskContent);
    }

    @Override
    public void reminders(RemindQuery remindQuery) {
        Integer type = remindQuery.getType();
        Survey survey = baseMapper.selectById(remindQuery.getId());

        if (Objects.equals(type, 1)) {
            List<String> userIds = remindQuery.getUserIds();
            if (Objects.isNull(userIds)) {
                throw new BusinessException(SurveyErrorNoEnum.ERR_SURVEY_USER_ID_LIST_EMPTY);
            }
            List<UserDTO> userDTOList = userFeign.getUseListByIds(userIds);

            StringBuilder pushArea = new StringBuilder();
            userDTOList.forEach(user -> pushArea.append(user.getFullName()).append(","));
            pushComponent.remindPush(userIds, pushArea, RemindPushEnum.SURVEY.getBizType(), survey.getId(),
                survey.getSurveyName());
        } else {
            RemindQueryDTO remindQueryDTO = new RemindQueryDTO();
            remindQueryDTO.setId(remindQuery.getId());
            int pageNo = 1;
            int pageSize = 500;
            PageInfo<RemindListDTO> remindListDTOPageInfo = new PageInfo<>();
            remindListDTOPageInfo.setHasNextPage(true);
            while (remindListDTOPageInfo.isHasNextPage()) {
                remindQueryDTO.setPageNo(pageNo);
                remindQueryDTO.setPageSize(pageSize);
                remindQueryDTO.setExport(true);
                remindListDTOPageInfo = getRemindListById(remindQueryDTO);
                List<String> userIdList = new ArrayList<>();
                StringBuilder pushArea = new StringBuilder();
                List<RemindListDTO> list = remindListDTOPageInfo.getList();
                if (!CollectionUtils.isEmpty(list)) {
                    list.forEach(remindListDTO -> userIdList.add(remindListDTO.getUserId()));
                    List<UserDTO> userDTOList = userFeign.getUseListByIds(userIdList);
                    userDTOList.forEach(user -> pushArea.append(user.getFullName()).append(","));
                    pushComponent.remindPush(userIdList, pushArea, RemindPushEnum.SURVEY.getBizType(), survey.getId(),
                        survey.getSurveyName());
                }
                pageNo++;
            }
        }
    }

    @Override
    public Map<String, SurveyDTO> getSurveyMapByIdList(Collection<String> surveyIdList) {
        if (CollectionUtils.isEmpty(surveyIdList)) {
            return new HashMap<>(8);
        }
        List<SurveyDTO> surveyList = baseMapper.getSurveyListByIdList(surveyIdList);
        surveyList.forEach(s -> Optional.ofNullable(surveyQuestionService.getQuestionCountBySurveyId(s.getId()))
            .ifPresent(s::setQuestionCount));
        return surveyList.stream().collect(Collectors.toMap(SurveyDTO::getId, s -> s));
    }

    @Override
    public void viewLimitChange(Integer viewType,Map<String, Long> viewLimitChangeMap) {
        log.info("viewLimitChange:" + JsonUtil.objToJson(viewLimitChangeMap));

        for (Map.Entry<String, Long> entry : viewLimitChangeMap.entrySet()) {
            String key = entry.getKey();
            Long value = viewLimitChangeMap.get(key);
            // 更新下发范围
            surveyViewLimitComponent.handleNewViewLimit(value, key);
            if (null != viewType) {
                //更新下发范围类型
                Survey saveInfo = new Survey();
                saveInfo.setId(key);
                saveInfo.setViewType(viewType);
                baseMapper.updateById(saveInfo);
            }
        }
    }

    @Override
    public SurveyDTO getRealityById(String id) {
        return baseMapper.getRealityById(id);
    }

    @Override
    public void updateSurveyFinishedCount(String id) {
        baseMapper.updateSurveyFinishedCount(id);
    }

    @Override
    public Survey checkSurvey(String id, boolean checkPublish) {
        Survey survey = baseMapper.selectById(id);
        if (null == survey) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST);
        }
        if (!checkPublish) {
            return survey;
        }
        if (survey.getPublished() == PublishEnum.NOT_PUBLISH.getValue()) {
            throw new BusinessException(ErrorNoEnum.ERR_IS_NO_PUBLISH);
        }

        if (ObjectUtils.isEmpty(survey.getEndTime())) {
            return survey;
        }
        if (survey.getEndTime().before(new Date())) {
            throw new BusinessException(SurveyErrorNoEnum.ERR_SURVEY_HAS_ENDED,
                null,
                DateHelper.formatDate(survey.getStartTime(), DateHelper.YYYYMMDD_HHMM),
                DateHelper.formatDate(survey.getEndTime(), DateHelper.YYYYMMDD_HHMM));
        }
        if (survey.getStartTime().after(new Date())) {
            throw new BusinessException(SurveyErrorNoEnum.ERR_SURVEY_TIME_NULL,
                null,
                DateHelper.formatDate(survey.getStartTime(), DateHelper.YYYYMMDD_HHMM),
                DateHelper.formatDate(survey.getEndTime(), DateHelper.YYYYMMDD_HHMM));
        }
        return survey;
    }

    /**
     * 调用推送feign
     *
     * @param saveDTO 保存实时dto
     */
    private void sendPushFeignSave(SurveySaveDTO saveDTO) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = saveDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(saveDTO.getIsTrain()).orElse(0);
        PushResourceDTO pushResourceDTO = new PushResourceDTO().setIsTrain(isTrain).setOperateState(0)
            .setProgrammeId(saveDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;
        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split(StringPool.UNDERSCORE);
            String[] resourceId = pushNoticeSetDTO.getResourceId().split(StringPool.UNDERSCORE);
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);

            // 此时中 pushNoticeSetDTO 前端只会传 projectId_ 所以拼接好id
            pushResourceDTO.setResourceId(resourceId[0] + StringPool.UNDERSCORE + saveDTO.getId());
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
            pushResourceDTO.setResourceId(saveDTO.getId());
        }

        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setStartTime(saveDTO.getStartTime());
        pushAttributeDTO.setEndTime(saveDTO.getEndTime());
        pushAttributeDTO.setIntro(saveDTO.getDescription());

        String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);

        // 发送资源操作事件消息
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(saveDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue()) ? OperationEnum.PUBLISH_CANCEL
                : OperationEnum.PUBLISH, PushType.SURVEY.getKey(), saveDTO.getId()));
    }

    @Override
    public List<ResourceBaseDTO> getSurveyBaseList(ResourceBaseQuery resourceBaseQuery) {
        return baseMapper.getSurveyBaseList(resourceBaseQuery);
    }

}
