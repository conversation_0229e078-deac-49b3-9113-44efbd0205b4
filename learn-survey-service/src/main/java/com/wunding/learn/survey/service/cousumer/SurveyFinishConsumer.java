package com.wunding.learn.survey.service.cousumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.ResourceFinishEvent;
import com.wunding.learn.common.mq.event.ResourceFinishEvent.ResourceEventRoutingKeyConstants;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.survey.service.service.ISurveyService;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * <p> 调研完成事件消费
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-04-18
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class SurveyFinishConsumer {

    private final ISurveyService surveyService;

    /**
     * 调研完成事件消息队列
     */
    public static final String SURVEY_FINISH_EVENT_CONSUMER_QUEUE = "SurveyFinishEventConsumerQueue";

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = SURVEY_FINISH_EVENT_CONSUMER_QUEUE), exchange = @Exchange(value = ResourceFinishEvent.SYSTEM_RESOURCE_FINISH_EXCHANGE, type = ResourceFinishEvent.SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE), key = {
        ResourceEventRoutingKeyConstants.SURVEY_FINISH_EVENT}), id = "surveyFinishEventConsumer")
    public void surveyFinishEventConsumer(@Payload ResourceFinishEvent resourceFinishEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        UserThreadContext.setTenantId(resourceFinishEvent.getTenantId());
        log.info("exampleAuditEventConsumer receive event :{} ", JsonUtil.objToJson(resourceFinishEvent));

        // 更新调研完成人数
        surveyService.updateSurveyFinishedCount(resourceFinishEvent.getContentId());
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(resourceFinishEvent,channel,deliveryTag, false);
    }

}
