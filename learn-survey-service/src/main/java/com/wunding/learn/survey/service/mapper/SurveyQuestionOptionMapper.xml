<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.survey.service.mapper.SurveyQuestionOptionMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.survey.service.mapper.SurveyQuestionOptionMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.survey.service.model.SurveyQuestionOption">
        <!--@Table survey_question_option-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="question_id" jdbcType="VARCHAR"
          property="questionId"/>
        <result column="survey_id" jdbcType="VARCHAR"
          property="surveyId"/>
        <result column="description" jdbcType="VARCHAR"
          property="description"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="available" jdbcType="INTEGER"
          property="available"/>
        <result column="is_del" jdbcType="INTEGER"
          property="isDel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, question_id, survey_id, `description`, sort_no, available, is_del
    </sql>

    <select id="getRadioAnalysisQuestionOptionDTOByQuestionIdAndSurveyId"
      resultType="com.wunding.learn.survey.service.admin.dto.SurveyAnalysisQuestionOptionDTO" useCache="false">
        select sqo.id                           optionId,
               sqo.description                  optionDesc,
               sqo.code                         optionCode,
               sqo.sort_no                      sortNo,
               ifnull(round((selectTotalCount /
                       (select count(id) total
                        from survey_record_detail
                        where question_id = #{questionId} and survey_id = #{surveyId})) * 100, 2), 0)
                                                selectance,
               ifnull(srdd.selectTotalCount, 0) selectTotalCount
        from survey_question_option sqo
                 left join (select srd.answer optionId, count(id) selectTotalCount
                            from survey_record_detail srd
                            where question_id = #{questionId} and survey_id = #{surveyId}
                            group by answer) srdd on srdd.optionId = sqo.id
        where sqo.question_id = #{questionId}
        order by sqo.sort_no
    </select>

    <select id="getMultiAnalysisQuestionOptionDTOByQuestionIdAndSurveyId"
      resultType="com.wunding.learn.survey.service.admin.dto.SurveyAnalysisQuestionOptionDTO" useCache="false">
        select sqo.id                           optionId,
               sqo.description                  optionDesc,
               sqo.code                         optionCode,
               sqo.sort_no                      sortNo,
               ifnull(round(((select count(id) total from survey_record_detail where answer = optionId and survey_id = #{surveyId}) /
                       (select count(distinct create_by)
                        from survey_record_detail
                        where is_del = 0
                          and (
                            question_id = #{questionId} and survey_id = #{surveyId}
                            ))) * 100, 2), 0)       selectance,
               ifnull(srdd.selectTotalCount, 0) selectTotalCount
        from survey_question_option sqo
                 left join (select srd.answer optionId, count(*) selectTotalCount
                            from survey_record_detail srd
                            where question_id = #{questionId} and survey_id = #{surveyId}
                            group by answer) srdd on srdd.optionId = sqo.id
        where sqo.question_id = #{questionId}
        order by sqo.sort_no
    </select>
</mapper>
