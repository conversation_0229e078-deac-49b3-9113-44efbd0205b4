package com.wunding.learn.survey.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.survey.service.model.DocumentQuestion;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 资料库题目表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2023-01-10
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface DocumentQuestionMapper extends BaseMapper<DocumentQuestion> {

}
