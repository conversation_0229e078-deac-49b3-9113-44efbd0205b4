package com.wunding.learn.special.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum;
import com.wunding.learn.common.viewlimit.model.BaseViewLimit;
import com.wunding.learn.special.api.service.dto.SpecialViewLimitCheckResultDTO;
import com.wunding.learn.special.service.component.SpecialViewLimitComponent;
import com.wunding.learn.special.service.mapper.SpecialViewLimitMapper;
import com.wunding.learn.special.service.model.SpecialViewLimit;
import com.wunding.learn.special.service.service.ISpecialViewLimitService;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 专题下发范围表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liuxiuyong</a>
 * @since 2022-08-09
 */
@Slf4j
@Service("specialViewLimitService")
public class SpecialViewLimitServiceImpl extends ServiceImpl<SpecialViewLimitMapper, SpecialViewLimit> implements
    ISpecialViewLimitService {

    @Resource
    SpecialViewLimitComponent specialViewLimitComponent;

    @Override
    public SpecialViewLimitCheckResultDTO checkViewPermision(String userId, String resourceId) {
        SpecialViewLimitCheckResultDTO resultDTO = new SpecialViewLimitCheckResultDTO();
        //判断有没有权限
        resultDTO.setCheckResult(baseMapper.checkViewLimit(userId, resourceId) > 0 ? 1 : 0);
        //没有权限，判断没有权限的类型
        if (Objects.equals(resultDTO.getCheckResult(), GeneralJudgeEnum.NEGATIVE.getValue())) {
            resultDTO.setUnPassType(isNoMemberCard(resourceId) ? 2 : 1);
        }
        return resultDTO;
    }

    private boolean isNoMemberCard(String resourceId) {
        // 获取资源是否配置了会员权限，
        List<BaseViewLimit> viewLimits = new ArrayList<>(
            specialViewLimitComponent.getViewLimitByResourceId(resourceId));
        Set<String> resourceOperationCategoryIds = viewLimits.stream()
            .filter(limit -> Objects.equals(limit.getCategoryType(), ViewLimitTypeEnum.MemberLimit.name()))
            .map(limit -> limit.getCategoryId())
            .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(resourceOperationCategoryIds)) {
            //没有权限的原因是没有会员卡权限
            return true;
        }
        // 2.其他情况，用户没有权限
        return false;
    }
}
