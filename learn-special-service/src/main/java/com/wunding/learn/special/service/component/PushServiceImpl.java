package com.wunding.learn.special.service.component;

import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.push.api.component.PushService;
import com.wunding.learn.push.api.dto.PushUserManageDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.ArrayList;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component("specialPushService")
public class PushServiceImpl implements PushService {

    @Resource
    private SpecialViewLimitComponent specialViewLimitComponent;
    @Resource
    private UserFeign userFeign;

    @Override
    public List<PushUserManageDTO> getPushUserManageList(String resourceId, String type) {
        List<PushUserManageDTO> list = new ArrayList<>();
        if (PushType.SPECIAL_TOPIC.getKey().equals(type)) {
            // 专题推送用户
            List<String> userIdList = specialViewLimitComponent.getViewLimitUser(resourceId);
            List<UserDTO> userList = userFeign.getUseListByIds(userIdList);
            PushUserManageDTO pushUserManageDTO;
            for (UserDTO u : userList) {
                pushUserManageDTO = new PushUserManageDTO();
                pushUserManageDTO.setUserId(u.getId());
                pushUserManageDTO.setUserName(u.getFullName());
                pushUserManageDTO.setUserMail(u.getEmail());
                list.add(pushUserManageDTO);
            }
        }
        return list;
    }
}
