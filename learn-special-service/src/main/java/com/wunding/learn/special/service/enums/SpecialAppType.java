package com.wunding.learn.special.service.enums;

/**
 * @Author: aixinrong
 * @Date: 2022/8/2 11:14
 */
public enum SpecialAppType {

    /**
     * 结业
     */
    COMPLETION(0, "completion", "结业"),

    /**
     * 公告
     */
    NOTICE(1, "info", "公告"),
    /**
     * 资料
     */
    DATA(2, "files", "资料"),

    /**
     * 费用
     */
    COST(3, "cost", "费用");

    private int no;
    private String appType;
    private String appName;

    SpecialAppType(int no, String appType, String appName) {
        this.no = no;
        this.appType = appType;
        this.appName = appName;
    }

    public int getNo() {
        return no;
    }

    public void setNo(int no) {
        this.no = no;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public static SpecialAppType getByNo(int no) {
        for (SpecialAppType projectAppType : SpecialAppType.values()) {
            if (projectAppType.getNo() == no) {
                return projectAppType;
            }
        }
        return null;
    }

    public static SpecialAppType getByAppType(String appType) {
        for (SpecialAppType projectAppType : SpecialAppType.values()) {
            if (projectAppType.getAppType().equals(appType)) {
                return projectAppType;
            }
        }
        return null;
    }

    public static String getByAppTypeName(String appType) {
        for (SpecialAppType c : SpecialAppType.values()) {
            if (c.getAppType().equals(appType)) {
                return c.getAppName();
            }
        }
        return null;
    }

}
