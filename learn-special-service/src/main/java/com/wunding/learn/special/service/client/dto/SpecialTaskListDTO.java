package com.wunding.learn.special.service.client.dto;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 专题任务列表
 *
 * <AUTHOR>
 * @date 2022/09/20
 */
@Data
@Accessors(chain = true)
@Schema(name = "SpecialTaskListDTO", description = "专题任务列表对象")
public class SpecialTaskListDTO {


    /**
     * 任务id
     */
    @Schema(description = "任务id")
    private String id;

    /**
     * 专题图片
     */
    @Schema(description = "专题图片")
    private NamePath headimage;

    /**
     * 专题内容标题
     */
    @Schema(description = "专题内容标题")
    private String title;

    /**
     * 专题内容标题
     */
    @Schema(description = "专题描述")
    private String desc;

}
