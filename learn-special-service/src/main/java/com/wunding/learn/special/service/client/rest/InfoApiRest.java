package com.wunding.learn.special.service.client.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.special.service.client.dto.SpecialApiInfoDTO;
import com.wunding.learn.special.service.client.dto.SpecialApiInfoDetailDTO;
import com.wunding.learn.special.service.client.query.SpecialApiInfoQuery;
import com.wunding.learn.special.service.service.IInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: mlearn
 * @description: <p>专题应用-公告</p>
 * @author: suchenyu
 * @create: 2022-07-13 10:59
 **/
@RestController
@RequestMapping("${module.special.contentPath:/}api/info")
@Tag(description = "专题应用-公告", name = "InfoRest")
@Validated
public class InfoApiRest {

    @Resource
    private IInfoService iInfoService;

    @GetMapping("/list")
    @Operation(operationId = "list", summary = "获取公告列表", description = "获取公告列表 协议2424")
    public Result<PageInfo<SpecialApiInfoDTO>> getApiInfoList(
        @Valid @ParameterObject SpecialApiInfoQuery specialApiInfoQuery) {
        return Result.success(iInfoService.getApiInfo(specialApiInfoQuery));
    }

    /**
     * 保存公告浏览记录，并返回资讯在线做课地址
     *
     * @return {@link Result}<{@link SpecialApiInfoDetailDTO}>
     */
    @GetMapping("/detail/{infoId}")
    @Operation(operationId = "detail", summary = "触发公告浏览记录并获取公告预览html", description = "触发公告浏览记录并获取公告预览html")
    public Result<SpecialApiInfoDetailDTO> detail(@PathVariable String infoId) {
        return Result.success(iInfoService.detailInfo(infoId));
    }


}
