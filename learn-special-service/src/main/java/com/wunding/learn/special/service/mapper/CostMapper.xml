<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.special.service.mapper.CostMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.special.service.mapper.CostMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.special.service.model.Cost">
        <!--@Table cost-->
                <id column="id" jdbcType="VARCHAR" property="id"/>
                <result column="name" jdbcType="VARCHAR"
                        property="name"/>
                <result column="budget_cost" jdbcType="VARCHAR"
                        property="budgetCost"/>
                <result column="actual_cost" jdbcType="TINYINT"
                        property="actualCost"/>
                <result column="mark" jdbcType="TINYINT"
                        property="mark"/>
                <result column="create_by" jdbcType="VARCHAR"
                        property="createBy"/>
                <result column="create_time" jdbcType="TIMESTAMP"
                        property="createTime"/>
                <result column="update_by" jdbcType="VARCHAR"
                        property="updateBy"/>
                <result column="update_time" jdbcType="TIMESTAMP"
                        property="updateTime"/>
                <result column="is_del" jdbcType="TINYINT"
                        property="isDel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, budget_cost, actual_cost, mark, create_by, create_time, update_by, update_time, is_del
    </sql>

    <select id="getCostById" resultType="com.wunding.learn.special.service.model.Cost" useCache="false">
        select <include refid="Base_Column_List"></include>
        from cost
        where customer_id = #{id}
        order by create_time desc
        limit 1
    </select>
</mapper>
