package com.wunding.learn.special.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.special.service.model.SpecialViewLimit;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 专题下发范围表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">liuxiuyong</a>
 * @since 2022-08-09
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface SpecialViewLimitMapper extends BaseMapper<SpecialViewLimit> {

    int checkViewLimit(String userId, String resourceId);
}
