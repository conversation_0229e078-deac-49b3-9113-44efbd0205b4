package com.wunding.learn.payment.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.payment.service.admin.dto.InvoiceInfoDTO;
import com.wunding.learn.payment.service.admin.dto.InvoiceStatusUpdateDTO;
import com.wunding.learn.payment.service.admin.dto.OrderCancelInvoiceDTO;
import com.wunding.learn.payment.service.admin.dto.OrderInvoiceHandleDTO;
import com.wunding.learn.payment.service.admin.dto.OrgInvoiceBaseDTO;
import com.wunding.learn.payment.service.admin.dto.OrgInvoiceSaveDTO;
import com.wunding.learn.payment.service.model.PaymentInvoice;

/**
 * <p> 订单发票 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gq</a>
 * @since 2023-07-31
 */
public interface IPaymentInvoiceService extends IService<PaymentInvoice> {

    /**
     * 编辑发票信息
     *
     * @param infoDTO
     */
    void editData(InvoiceInfoDTO infoDTO);

    /**
     * 提交开票申请
     *
     * @param saveDTO
     */
    void saveData(OrgInvoiceSaveDTO saveDTO);

    /**
     * 提交开票申请
     *
     * @param saveDTO
     */
    void saveOrUpdateData(OrgInvoiceSaveDTO saveDTO);

    /**
     * 根据开票id获取开票信息
     *
     * @param id
     * @return
     */
    InvoiceInfoDTO getDataById(String id);

    /**
     * 更新
     *
     * @param saveDTO
     */
    void updateDataById(OrgInvoiceSaveDTO saveDTO);

    /**
     * 更新默认开票信息
     *
     * @param saveDTO
     */
    void updateDefaultInvoice(OrgInvoiceBaseDTO saveDTO, String customerId);

    /**
     * 更新发票状态
     *
     * @param updateDTO
     */
    void updateDataStatus(InvoiceStatusUpdateDTO updateDTO);

    /**
     * 确认开票
     *
     * @param handleDTO
     */
    void confirmInvoice(OrderInvoiceHandleDTO handleDTO);

    /**
     * 退票
     *
     * @param cancelInvoiceDTO
     */
    void cancelInvoice(OrderCancelInvoiceDTO cancelInvoiceDTO);
}
