package com.wunding.learn.payment.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import com.wunding.learn.payment.service.admin.dto.OrgOrderMemberImportDTO;
import com.wunding.learn.payment.service.admin.dto.OrgOrderMemberLimitedDTO;
import com.wunding.learn.payment.service.admin.dto.OrgOrderMemberSaveDTO;
import com.wunding.learn.payment.service.service.IOrgOrderMemberService;
import com.wunding.learn.user.api.dto.OrgOrderMemberDTO;
import com.wunding.learn.user.api.query.OrgOrderMemberPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @className OrgPaymentOrderUserRest
 * @description TODO
 * @date 2023/8/7 15:32
 */
@RestController
@RequestMapping("${module.user.contentPath:/}orgOrderMember")
@Tag(description = "机构订单会员管理", name = "OrgOrderMemberRest")
public class OrgOrderMemberRest {

    @Resource
    private IOrgOrderMemberService orgOrderMemberService;

    @GetMapping
    @Operation(operationId = "pageOrgOrderMember", description = "会员列表", summary = "会员列表")
    public Result<PageInfo<OrgOrderMemberDTO>> pageOrgOrderMember(
        @ParameterObject @Valid OrgOrderMemberPageQuery query) {
        return Result.success(orgOrderMemberService.pageOrgOrderMember(query));
    }

    @PostMapping("/export")
    @Operation(operationId = "pageOrgOrderMember", description = "导出会员列表", summary = "导出会员列表")
    public Result<ExportResultDTO> exportOrgOrderMember(@RequestBody @Valid OrgOrderMemberPageQuery query) {
        orgOrderMemberService.exportData(query);
        return Result.success();
    }

    @PostMapping("/import")
    @Operation(operationId = "importOrgOrderMember", description = "导入会员", summary = "导入会员")
    public Result<ImportResultDTO> importOrgOrderMember(@RequestBody @Valid OrgOrderMemberImportDTO importDTO) {
        return Result.success(orgOrderMemberService.importOrgOrderMember(importDTO));
    }

    @PostMapping
    @Operation(operationId = "saveOrgOrderMember", description = "新建会员", summary = "新建会员")
    public Result<Void> saveOrgOrderMember(@RequestBody @Valid OrgOrderMemberSaveDTO saveDTO) {
        orgOrderMemberService.saveOrgOrderMember(saveDTO);
        return Result.success();
    }


    @DeleteMapping("/{ids}")
    @Operation(operationId = "removeOrgOrderMember", description = "删除会员", summary = "删除会员")
    public Result<Void> removeOrgOrderMember(
        @PathVariable("ids") @Parameter(description = "主键id(多个数据使用,拼接)") String ids) {
        orgOrderMemberService.removeOrgOrderMember(ids);
        return Result.success();
    }


    @GetMapping("/{orderId}/limited")
    @Operation(operationId = "getOrgOrderMemberLimited", description = "查询订单会员限制", summary = "查询订单会员限制")
    public Result<OrgOrderMemberLimitedDTO> getOrgOrderMemberLimited(
        @Parameter(description = "订单id") @PathVariable("orderId") String id) {
        return Result.success(orgOrderMemberService.getOrgOrderMemberLimited(id));
    }
}
