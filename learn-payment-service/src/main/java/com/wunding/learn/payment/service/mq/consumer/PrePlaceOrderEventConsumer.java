package com.wunding.learn.payment.service.mq.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.payment.service.mq.event.PrePlaceOrderEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 预下单事件消费者(关闭逾期未支付订单)
 *
 * <AUTHOR>
 * @date 2023/7/20
 */
@Component
@Slf4j
public class PrePlaceOrderEventConsumer {

    /**
     * 支付超时时间
     */
    @Value("${payment.overdue_time}")
    private Integer paymentOverdueTime;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MqConst.PRE_PAY_ORDER_EVENT_QUEUE), exchange = @Exchange(value = MqConst.EXCHANGE_TOPIC, type = ExchangeTypes.TOPIC), key = {
        MqConst.PRE_PAY_ORDER_EVENT_ROUTING_KEY}))
    public void handleEvent(@Payload PrePlaceOrderEvent event, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
        Channel channel) {
        log.info("PrePlaceOrderEventConsumer: {}", JsonUtil.objToJson(event));
        // 订单是否存在，不存在则直接ack确认消息
        // 是否达到了订单超时时间
        String orderId = event.getOrderId();
        // 如果未支付状态，主动查询一次
        // 如果还是未支付，关闭订单，订单是否是关闭状态
        // 主动取消微信订单
        // 关闭支付流水
        // ack消息
    }
}
