package com.wunding.learn.payment.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.payment.service.model.PaymentTradeRefund;

/**
 * <p> 退款交易流水表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gq</a>
 * @since 2023-07-14
 */
public interface IPaymentTradeRefundService extends IService<PaymentTradeRefund> {

    /**
     * 生成支付交易流水单号
     *
     * @return
     */
    String createNewTradeSn();
}
