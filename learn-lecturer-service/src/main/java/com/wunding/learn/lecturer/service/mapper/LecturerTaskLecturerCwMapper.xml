<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.lecturer.service.mapper.LecturerTaskLecturerCwMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.lecturer.service.mapper.LecturerTaskLecturerCwMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.lecturer.service.model.LecturerTaskLecturerCw">
        <!--@Table lecturer_task_lecturer_cw-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="cw_id" jdbcType="VARCHAR"
          property="cwId"/>
        <result column="task_lecturer_id" jdbcType="VARCHAR"
          property="taskLecturerId"/>
        <result column="lecturer_id" jdbcType="VARCHAR"
          property="lecturerId"/>
    </resultMap>

    <resultMap id="LecturerCoursewareDTOMap" type="com.wunding.learn.lecturer.service.client.dto.LecturerCoursewareDTO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <collection javaType="list" ofType="com.wunding.learn.lecturer.service.client.dto.LecturerCoursewareInfoDTO"
          property="coursewareList">
            <result column="cw_id" property="id"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <select id="getLecturerByCwId" resultType="com.wunding.learn.lecturer.api.dto.LecturerDetailDTO" useCache="false">
        select group_concat(tt.id separator '|')   id,
               group_concat(tt.name separator '|') name
        from (select distinct t.id, t.name
              from (select l.id, l.name
                    from lecturer_examination le
                             left join lecturer l
                                       on le.lecturer_id = l.id
                    where le.id in
                          (select task_lecturer_id from lecturer_task_lecturer_cw where cw_id = #{cwId})
                      and l.is_del = 0
                    order by le.begin_time desc) t) tt
    </select>

    <select id="getLecturerCoursewareList" resultMap="LecturerCoursewareDTOMap" useCache="false">
        select ltlc.lecturer_id as id,
               ltlc.cw_id
        from lecturer_task_lecturer_cw ltlc,
             lecturer l
        where ltlc.lecturer_id = l.id
          and l.is_del = 0
          and ltlc.project_id = #{projectId}
        <if test="coursewareIds != null and coursewareIds.size() > 0">
            and ltlc.cw_id in
            <foreach close=")" collection="coursewareIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getLecturerByCwIds" resultType="com.wunding.learn.lecturer.api.dto.LecturerDetailDTO" useCache="false">
        select le.cw_id cwId, le.lecturer_id lecturerId, l.name
        from lecturer_examination le left join lecturer l on le.lecturer_id = l.id and l.is_del = 0
        where le.id in
        <foreach item="item" collection="taskLecturerIdList" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by le.begin_time desc
    </select>

    <select id="getTaskLecturerIdByCwIdList" resultType="java.lang.String">
        select task_lecturer_id
        from lecturer_task_lecturer_cw
        <if test="cwIds != null and cwIds.size() > 0">
            where cw_id in
            <foreach close=")" collection="cwIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <sql id="Base_Column_List">
        id
        , cw_id, task_lecturer_id, lecturer_id
    </sql>
</mapper>
