package com.wunding.learn.lecturer.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.ImportExcelFileDTO;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.dto.LecturerCourseDTO;
import com.wunding.learn.lecturer.api.dto.LecturerCourseAuthFeignDTO;
import com.wunding.learn.lecturer.service.admin.dto.CourseTeacherPageDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerCourseAuthByCoursePageDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerCourseAuthByLecturerPageDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerCourseAuthenticationSaveDTO;
import com.wunding.learn.lecturer.service.admin.query.CourseLecturerListQuery;
import com.wunding.learn.lecturer.service.admin.query.LecturerCourseAuthByCourseQuery;
import com.wunding.learn.lecturer.service.admin.query.LecturerCourseAuthByLecturerQuery;
import com.wunding.learn.lecturer.service.admin.query.LecturerCourseQuery;
import com.wunding.learn.lecturer.service.client.dto.LecturerCourseAuthClientDTO;
import com.wunding.learn.lecturer.service.model.LecturerCourseAuthentication;

import java.util.List;
import java.util.concurrent.Future;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 讲师课程认证表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2023-03-15
 */
public interface ILecturerCourseAuthenticationService extends IService<LecturerCourseAuthentication> {

    ILecturerCourseAuthenticationService getBean();

    /**
     * 保存数据
     *
     * @param saveDTO
     */
    void saveData(LecturerCourseAuthenticationSaveDTO saveDTO);

    /**
     * 删除数据
     *
     * @param ids
     */
    void deleteData(String ids);

    /**
     * 课程讲师认证-By讲师-列表
     *
     * @param query
     * @return
     */
    PageInfo<LecturerCourseAuthByLecturerPageDTO> listByLecturer(LecturerCourseAuthByLecturerQuery query);

    @Async
    void exportByLecturer(LecturerCourseAuthByLecturerQuery query);

    /**
     * 课程讲师认证-By课程-列表
     *
     * @param query
     * @return
     */
    PageInfo<LecturerCourseAuthByCoursePageDTO> listByCourse(LecturerCourseAuthByCourseQuery query);

    @Async
    void exportByCourse(LecturerCourseAuthByCourseQuery query);

    /**
     * 更新所有的课程信息
     */
    void updateCourseInfo(CourseInfoDTO courseInfoDTO);

    /**
     * 讲师课程开发明细
     *
     * @param query
     * @return
     */
    PageInfo<LecturerCourseDTO> lecturerCourseList(LecturerCourseQuery query);

    /**
     * 讲师课程开发明细
     *
     * @param query
     * @return
     */
    @Async
    void exportLecturerCourseList(LecturerCourseQuery query);

    /**
     * 授课讲师明细
     *
     * @param query
     * @return
     */
    PageInfo<CourseTeacherPageDTO> courseLecturerList(CourseLecturerListQuery query);

    /**
     * 导入数据
     *
     * @param dto
     */
    Future<ImportResultDTO> importData(ImportExcelFileDTO dto);

    @Async
    void exportCourseLecturerList(CourseLecturerListQuery query);

    /**
     * 通过课程id获取课程认证讲师信息
     * @param courseId
     * @return
     */
    List<LecturerCourseAuthFeignDTO> getLecturerCourseAuthByCourseId(String courseId);

    /**
     * 通过讲师id获取课程认证讲师信息
     * @param lecturerId
     * @return
     */
    List<LecturerCourseAuthClientDTO> getLecturerCourseAuthByLecturerId(String lecturerId);

    @Async
    void exportByLecturerCourse(LecturerCourseAuthByLecturerQuery query);
}
