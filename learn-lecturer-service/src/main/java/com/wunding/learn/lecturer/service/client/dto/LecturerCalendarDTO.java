package com.wunding.learn.lecturer.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: LecturerScheduleDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/6/20 11:23
 */
@Data
@Schema(name = "LecturerScheduleDTO", description = "讲师档期返回对象")
public class LecturerCalendarDTO {

    @Schema(description = "唯一标识, 使用日期为id， 格式为 yyyyMMdd, 如 20190204")
    private String id;

    @Schema(description = "日期")
    private Date date;

    @Schema(description = "上午的预定状态, 0 不可预定， 1 可预定, 2 已预定")
    private Integer morning;

    @Schema(description = "下午的预定状态, 0 不可预定， 1 可预定, 2 已预定")
    private Integer afternoon;

    @Schema(description = "晚上的预定状态, 0 不可预定， 1 可预定, 2 已预定")
    private Integer evening;

}
