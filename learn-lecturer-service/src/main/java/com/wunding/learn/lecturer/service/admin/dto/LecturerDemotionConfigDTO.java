package com.wunding.learn.lecturer.service.admin.dto;

import com.wunding.learn.common.viewlimit.dto.ViewLimitTypeDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: LecturerDemotionConfigDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/5/18 9:39
 */
@Data
@Schema(name = "LecturerDemotionConfigDTO", description = "讲师降级规则返回对象")
public class LecturerDemotionConfigDTO implements Serializable {

    public static final long serialVersionUID = 1L;

    /**
     *
     */
    @Schema(description = "id")
    private String id;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String serialNumber;

    /**
     * 是否自动降级 0-否 1-是
     */
    @Schema(description = "是否自动降级 0-否 1-是")
    private Integer autoDemotion;

    /**
     * 是否启用 0-否 1-是
     */
    @Schema(description = "是否启用 0-否 1-是")
    private Integer isAvailable;

    /**
     * 当前等级名称
     */
    @Schema(description = "当前等级名称")
    private String currentLevelName;

    /**
     * 当前类型名称
     */
    @Schema(description = "当前类型名称")
    private String currentTypeName;

    /**
     * 当前等级id
     */
    @Schema(description = "当前等级id")
    private String currentLevelId;

    /**
     * 当前类型id
     */
    @Schema(description = "当前类型id")
    private String currentTypeId;

    /**
     * 降级后等级名称
     */
    @Schema(description = "降级后等级名称")
    private String afterDemotionLevelName;


    /**
     * 降级后类型名称
     */
    @Schema(description = "降级后类型名称")
    private String afterDemotionTypeName;
    /**
     * 降级后等级id
     */
    @Schema(description = "降级后等级id")
    private String afterDemotionLevelId;


    /**
     * 降级后类型id
     */
    @Schema(description = "降级后类型id")
    private String afterDemotionTypeId;


    /**
     * 预警邮件间隔天数
     */
    @Schema(description = "预警邮件间隔天数")
    private Integer emailIntervalDays;


    /**
     * 下次预警时间
     */
    @Schema(description = "下次预警时间")
    private Date nextWarnTime;
    /**
     * 邮件模板id
     */
    @Schema(description = "邮件模板id")
    private String emailTemplateId;

    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    private Integer viewType;

    @Schema(description = "下发范围基本信息")
    private ViewLimitBaseInfoDTO limit;


}
