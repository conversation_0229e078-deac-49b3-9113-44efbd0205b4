package com.wunding.learn.lecturer.service.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.lecturer.service.model.LecturerRemovalConfig;

/**
 * <p> 讲师出库规则配置 数据操作接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2024-02-20
 */
public interface LecturerRemovalConfigDao extends IService<LecturerRemovalConfig> {

    /**
     * 保存讲师出库规则
     *
     * @param lecturerRemovalConfig
     */
    void saveLecturerRemovalConfig(LecturerRemovalConfig lecturerRemovalConfig);


    /**
     * 更新讲师出库规则
     * @param lecturerRemovalConfig
     */
    void updateLecturerRemovalConfig(LecturerRemovalConfig lecturerRemovalConfig);


    /**
     * 删除讲师出库规则
     * @param lecturerRemovalConfig
     */
    void delLecturerRemovalConfig(LecturerRemovalConfig lecturerRemovalConfig);

    /**
     * 启用讲师出库规则
     *
     * @param lecturerRemovalConfig
     */
    void enable(LecturerRemovalConfig lecturerRemovalConfig);

    /**
     * 禁用讲师出库规则
     *
     * @param lecturerRemovalConfig
     */
    void disable(LecturerRemovalConfig lecturerRemovalConfig);
}
