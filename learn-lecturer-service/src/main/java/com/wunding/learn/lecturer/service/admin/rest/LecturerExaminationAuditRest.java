package com.wunding.learn.lecturer.service.admin.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.lecture.LecturerErrorNoEnum;
import com.wunding.learn.common.enums.other.ExaminationStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.lecturer.service.admin.dto.LecturerExaminationAuditDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerTeachRewardConfigPageDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerTeachRewardLimitDTO;
import com.wunding.learn.lecturer.service.service.ILecturerExaminationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/6/17
 */
@RestController("lecturerLecturerExaminationAuditRest")
@RequestMapping("${module.lecturer.contentPath:/}examinationAudit")
@Tag(description = "讲师授课记录审核", name = "LecturerExaminationAuditRest")
public class LecturerExaminationAuditRest {

    @Resource
    private ILecturerExaminationService lecturerExaminationService;

    @PostMapping("/audit")
    @Operation(operationId = "audit", summary = "审核讲师授课数据", description = "审核讲师授课信息数据")
    public Result<Void> auditExamination(@RequestBody @Valid LecturerExaminationAuditDTO lecturerExaminationAuditDTO)
        throws IOException {
        if (StringUtils.isBlank(lecturerExaminationAuditDTO.getIdStr())) {
            throw new BusinessException(LecturerErrorNoEnum.ERR_AUDIT_PRIMARY_KEY_NOT_NULL);
        }
        if (!ExaminationStatusEnum.isEffective(lecturerExaminationAuditDTO.getStatus())) {
            throw new BusinessException(LecturerErrorNoEnum.ERR_AUDIT_STATUS_INVALID);
        }
        if (ExaminationStatusEnum.AUDIT_REFUSE.getValue() == lecturerExaminationAuditDTO.getStatus() &&
            StringUtils.isBlank(lecturerExaminationAuditDTO.getReason())) {
            throw new BusinessException(LecturerErrorNoEnum.ERR_AUDIT_REFUSE_REASON_NOT_NULL);
        }
        lecturerExaminationService.auditLecturerExamination(lecturerExaminationAuditDTO);
        return Result.success();
    }

    @GetMapping("/getCalcRule")
    @Operation(operationId = "getCalcRule", summary = "根据ID获取命中规则 ", description = "根据ID获取命中规则")
    public Result<LecturerTeachRewardConfigPageDTO> getCalcRule(@RequestParam("id") @Valid String id)
        throws IOException {

        LecturerTeachRewardConfigPageDTO dto  =lecturerExaminationService.getCalcRule(id);
        return Result.success(dto);
    }

    @GetMapping("/getLimitRule")
    @Operation(operationId = "getLimitRule", summary = "根据ID获取限制规则 ", description = "根据ID获取限制规则")
    public Result<LecturerTeachRewardLimitDTO> getLimitRule(@RequestParam("id") @Valid String id)
        throws IOException {


        LecturerTeachRewardLimitDTO dto  =lecturerExaminationService.getLimitRule(id);
        return Result.success(dto);
    }

}
