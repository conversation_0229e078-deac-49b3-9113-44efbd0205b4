package com.wunding.learn.lecturer.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

/**
 * 讲师升级规则查询对象
 *
 * <AUTHOR>
 * @title: LecturerUpgradeConfigQueryDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/5/18 9:29
 */
@Data
public class LecturerUpgradeConfigQueryDTO extends BaseEntity {

    @Parameter(description = "当前等级id")
    private String currentLevelId;

    @Parameter(description = "当前类型id")
    private String currentTypeId;

    @Parameter(description = "晋级后等级id")
    private String afterPromotionLevelId;

    @Parameter(description = "晋级后类型id")
    private String afterPromotionTypeId;

    @Parameter(description = "是否自动升级 0-否 1-是")
    private Integer autoUpgrade;

    @Parameter(description = "是否启用 0-否 1-是")
    private Integer isAvailable;

}
