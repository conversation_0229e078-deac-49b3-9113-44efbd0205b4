package com.wunding.learn.lecturer.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.AvailableDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerRemovalConfigDTO;
import com.wunding.learn.lecturer.service.admin.dto.SaveLecturerRemovalConfigDTO;
import com.wunding.learn.lecturer.service.admin.query.LecturerRemovalConfigQueryDTO;
import com.wunding.learn.lecturer.service.model.LecturerRemovalConfig;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 讲师出库规则配置表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">李毅慧</a>
 * @since 2022-05-11
 */
public interface ILecturerRemovalConfigService extends IService<LecturerRemovalConfig> {

    /**
     * 获取讲师出口规则对象分页数据
     *
     * @param lecturerRemovalConfigQueryDTO 讲师出口规则查询对象
     * @return 讲师出口规则对象分页数据
     */
    PageInfo<LecturerRemovalConfigDTO> findLecturerRemovalConfigPage(
        LecturerRemovalConfigQueryDTO lecturerRemovalConfigQueryDTO);

    /**
     * 保存/更新讲师出口规则对象数据
     *
     * @param saveLecturerRemovalConfigDTO 讲师出库规则保存/更新对象
     */
    void saveOrUpdateLecturerRemovalConfig(SaveLecturerRemovalConfigDTO saveLecturerRemovalConfigDTO);

    /**
     * 删除讲师出口规则对象数据
     *
     * @param ids 主键(id)字符串
     */
    void delLecturerRemovalConfigByIds(String ids);

    /**
     * 启用/禁用出口规则
     *
     * @param availableDTO 启用禁用参数对象
     */
    void available(AvailableDTO availableDTO);

    /**
     * 根据id获取一个出库规则
     *
     * @param id
     * @return
     */
    LecturerRemovalConfigDTO getLecturerRemovalConfigById(String id);

    /**
     * 导出讲师出库规则配置列表的数据
     *
     * @param lecturerRemovalConfigQueryDTO
     * @return
     */
    @Async
    void export(LecturerRemovalConfigQueryDTO lecturerRemovalConfigQueryDTO);
}
