package com.wunding.learn.market.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.market.service.mapper.VoteViewLimitMapper;
import com.wunding.learn.market.service.model.VoteViewLimit;
import com.wunding.learn.market.service.service.IVoteViewLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 投票可见范围 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2022-11-02
 */
@Slf4j
@Service("voteViewLimitService")
public class VoteViewLimitServiceImpl extends ServiceImpl<VoteViewLimitMapper, VoteViewLimit> implements
    IVoteViewLimitService {

}
