package com.wunding.learn.common.enums.viewlimit;

import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.i18n.util.EnumI18n;

/**
 * 可见范围类型枚举
 */
public enum ViewLimitTypeEnum implements EnumI18n {

    /**
     * 2 1时间身份
     */
    TIME(TypeEnum.TYPE_IDENTITY, CodeEnum.CODE_1, "时间身份", "TimeLimit"),

    /**
     * 2 2业务条线
     */
    BUSINESS(TypeEnum.TYPE_IDENTITY, CodeEnum.CODE_2, "业务条线", "BusinessLimit"),

    /**
     * 2 3管理者层级
     */
    MANAGE_LEVEL(TypeEnum.TYPE_IDENTITY, CodeEnum.CODE_3, "管理者层级", "ManageLevelLimit"),

    /**
     * 2 4岗位族、岗位、岗位层级
     */
    POST(TypeEnum.TYPE_IDENTITY, CodeEnum.CODE_4, "岗位族、岗位、岗位层级", "PostLimit"),

    /**
     * 2 5讲师层级
     */
    LECTURER(TypeEnum.TYPE_IDENTITY, CodeEnum.CODE_5, "讲师层级", "LecturerLimit"),

    /**
     * 2 6职级
     */
    JOB_LEVEL(TypeEnum.TYPE_IDENTITY, CodeEnum.CODE_6, "职级", "JobLevelLimit"),

    /**
     * 2 9 学员会员
     */
    MemberLimit(TypeEnum.TYPE_IDENTITY, CodeEnum.CODE_9, "学院会员", "MemberLimit"),

    /**
     * 1 1部门
     *
     * @return
     */
    OrgLimit(TypeEnum.TYPE_ORG, CodeEnum.CODE_1, "部门", "OrgLimit"),

    /**
     * 3 1人员
     *
     * @return
     */
    UserLimit(TypeEnum.TYPE_USER, CodeEnum.CODE_1, "人员", "UserLimit"),

    /**
     * 4 工种
     */
    WorkLimit(TypeEnum.TYPE_WORK, CodeEnum.CODE_7, "工种", "WorkLimit");

    // 类型 1-部门 2-身份 3-人员 4-工种
    private TypeEnum type;

    // 编号
    private CodeEnum code;

    // 名称
    @EnumI18nProperty
    private String name;

    // 英文名称
    private String enName;

    ViewLimitTypeEnum(TypeEnum type, CodeEnum code, String name, String enName) {
        this.type = type;
        this.code = code;
        this.name = name;
        this.enName = enName;
    }

    // 根据类型和编号返回枚举
    public static ViewLimitTypeEnum getEnumBy(int type, int code) {
        for (ViewLimitTypeEnum value : ViewLimitTypeEnum.values()) {
            if (value.type.type == type && value.code.code == code) {
                return value;
            }
        }
        return null;
    }

    // 根据enName返回枚举
    public static ViewLimitTypeEnum getEnumByCategoryType(String categoryType) {
        for (ViewLimitTypeEnum value : ViewLimitTypeEnum.values()) {
            if (value.enName.equals(categoryType)) {
                return value;
            }
        }
        return null;
    }

    public TypeEnum getType() {
        return type;
    }

    public CodeEnum getCode() {
        return code;
    }

    public String getName() {
        return i18n(name(), this.name);
    }

    public String getEnName() {
        return enName;
    }

    /**
     * 类型枚举 1 部门 2 身份 3 人员
     */
    public enum TypeEnum {
        /**
         * 1 部门
         */
        TYPE_ORG(1),

        /**
         * 2 身份
         */
        TYPE_IDENTITY(2),

        /**
         * 3 人员
         */
        TYPE_USER(3),

        /**
         * 4 工种
         */
        TYPE_WORK(4);

        int type;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        TypeEnum(int type) {
            this.type = type;
        }
    }

    /**
     * 编号枚举
     */
    public enum CodeEnum {
        /**
         * 部门/人员/身份-1时间身份
         */
        CODE_1(1),

        /**
         * 身份-2业务条线
         */
        CODE_2(2),

        /**
         * 身份-3管理者层级
         */
        CODE_3(3),
        /**
         * 身份-4岗位族、岗位、岗位层级
         */
        CODE_4(4),
        /**
         * 身份-5讲师层级
         */
        CODE_5(5),
        /**
         * 身份-6职级
         */
        CODE_6(6),

        /**
         * 工种
         */
        CODE_7(7),

        /**
         * 身份-9学院会员
         */
        CODE_9(9),
        ;
        int code;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        CodeEnum(int code) {
            this.code = code;
        }
    }
}
