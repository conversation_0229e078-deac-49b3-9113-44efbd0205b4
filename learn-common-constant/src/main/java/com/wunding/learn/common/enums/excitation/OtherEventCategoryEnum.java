package com.wunding.learn.common.enums.excitation;

/**
 * 其他时间类别
 *
 * <AUTHOR>
 * @date 2022/11/2
 */
public enum OtherEventCategoryEnum {
    // 话题悬赏
    topicReward,

    // 积分抽奖
    integralDraw,

    // 兑换商品
    exchangeCommodity,

    // 兑换金币
    exchangeGoldCoin,

    // 手动导入积分
    manualImportIntegral,

    // 手动导入金币
    manualImportGoldCoin,

    // 积分清零
    integralClearing;

    /**
     * 判断是否属于交易激励类型
     *
     * @param id 激励事件ID
     * @return 是否属于
     */
    public static boolean isOtherEvent(String id) {
        OtherEventCategoryEnum[] values = OtherEventCategoryEnum.values();
        for (OtherEventCategoryEnum categoryEnum : values) {
            if (categoryEnum.name().equals(id)) {
                return true;
            }
        }
        return false;
    }

}
