package com.wunding.learn.common.constant.other;

/**
 * 复制过来常量类，后续把有用的挪出去，本类需要删除
 *
 * @deprecated
 * <AUTHOR>
 * @date 2022/6/9
 */
@Deprecated(since="1.0")
public class ApiConstantUtil {

    private ApiConstantUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 响应头类型
     */
    public static final String POST_HEADER_CONTENT_TYPE = "application/json;charset=UTF-8";

    /**
     * 消息返回错误号
     */
    public static final String KEY_ERR_NO = "errno";
    /**
     * 消息
     */
    public static final String KEY_MESSAGE = "message";
    /**
     * 上传图片格式
     */
    public static final String PIC_FORMAT_ERROR_MSG = "上传图片格式不正确，请上传jpg、png、jpeg、bmp、svg、gif格式的图片";

    /**
     * 内置文件 scorm
     */
    public static final String SCORM_XML = "scorm.xml";
    /**
     * 内置文件 pdf
     */
    public static final String INDEX_PDF = "index.pdf";

    /**
     * 话题模块 热门
     */
    public static final String POST_HOT = "hot";
    /**
     * 话题模块 推荐
     */
    public static final String POST_DIGEST = "digest";
    /**
     * 话题模块 我的帖子
     */
    public static final String POST_MY_POSTS = "myposts";
    /**
     * 话题模块 我的回复帖子
     */
    public static final String POST_MY_REPLY = "myreply";
    /**
     * 话题模块 我的关注帖子
     */
    public static final String POST_MY_PV = "mypv";

    /**
     * 数字转换格式
     */
    public static final String NUMBER_FORMAT_STANDARD = "###.##";
}
