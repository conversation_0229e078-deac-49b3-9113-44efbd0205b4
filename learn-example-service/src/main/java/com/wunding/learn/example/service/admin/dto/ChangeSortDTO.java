package com.wunding.learn.example.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p> 
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2025/5/16
 */
@Data
@Schema(name = "ChangeSortDTO", description = "拖动排序保存对象")
public class ChangeSortDTO {

    @Schema(description = "分类ID")
    @NotNull(message = "分类ID不能为空")
    private String id;

    @Schema(description = "新的序号")
    @NotNull(message = "新的排序值不能为空")
    private Integer sortNo;
}
