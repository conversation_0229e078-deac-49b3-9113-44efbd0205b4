package com.wunding.learn.example.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@Schema(name = "ExampleCategoryDTO", description = "案例分类对象")
public class ExampleCategoryDTO implements Serializable {

    private static final long serialVersionUID = 4223775820699574181L;

    @Schema(description = "案例id")
    private String id;

    @Schema(description = "案例分类名称")
    private String exampleName;

    @Schema(description = "子类")
    private List<ExampleCategoryDTO> childrenList;
}
