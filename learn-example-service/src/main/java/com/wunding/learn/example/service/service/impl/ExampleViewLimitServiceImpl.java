package com.wunding.learn.example.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.example.service.mapper.ExampleViewLimitMapper;
import com.wunding.learn.example.service.model.ExampleViewLimit;
import com.wunding.learn.example.service.service.IExampleViewLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 案例库可见范围表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-29
 */
@Slf4j
@Service("exampleViewLimitService")
public class ExampleViewLimitServiceImpl extends ServiceImpl<ExampleViewLimitMapper, ExampleViewLimit> implements
    IExampleViewLimitService {

}
