package com.wunding.learn.example.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.example.ExampleErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.SaveViewLimitDTO;
import com.wunding.learn.common.dto.SysCategoryDTO;
import com.wunding.learn.common.enums.category.CategoryType;
import com.wunding.learn.common.enums.other.CodePrefixEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.common.viewlimit.dto.ViewLimitTypeDTO;
import com.wunding.learn.example.service.admin.dto.ExpertInfoDTO;
import com.wunding.learn.example.service.admin.dto.ExpertLibDTO;
import com.wunding.learn.example.service.admin.dto.ExpertLibSaveDTO;
import com.wunding.learn.example.service.admin.query.ExpertLibQuery;
import com.wunding.learn.example.service.dao.ExpertDao;
import com.wunding.learn.example.service.mapper.ExampleExpertMapper;
import com.wunding.learn.example.service.model.Example;
import com.wunding.learn.example.service.model.Expert;
import com.wunding.learn.example.service.model.ExpertCategory;
import com.wunding.learn.example.service.service.IExampleService;
import com.wunding.learn.example.service.service.IExpertCategoryService;
import com.wunding.learn.example.service.service.IExampleExpertService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.service.CategoryFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 专家库 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-31
 */
@Slf4j
@Service("exampleExpertService")
public class ExampleExpertServiceImpl extends ServiceImpl<ExampleExpertMapper, Expert> implements IExampleExpertService, CommandLineRunner {

    private static final AtomicLong CODE = new AtomicLong(1);

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private UserFeign userFeign;

    @Resource
    private IExpertCategoryService expertCategoryService;

    @Resource
    private ExportComponent exportComponent;

    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;

    @Resource(name = "expertDao")
    private ExpertDao expertDao;

    @Resource
    private CategoryFeign categoryFeign;

    @Resource
    @Lazy
    private IExampleService exampleService;

    @Override
    @SuppressWarnings("unchecked")
    public void run(String... args) throws Exception {
        for (Object tenantId : redisTemplate.opsForHash().keys(TenantRedisKeyConstant.DB_KEY)) {
            String tid = ((String) tenantId).replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, "");
            UserThreadContext.setTenantId(tid);
            initExpertNo();
        }
        UserThreadContext.remove();
    }

    //    @PostConstruct
    private void initExpertNo() {
        String oldCode = baseMapper.getMaxExpertNumber();
        Long expertNumber = CodePrefixEnum.EXPERT.stringToLong(oldCode);
        // 等于1才初始化
        if (ObjectUtils.equals(CODE.get(), 1L)) {
            CODE.set(expertNumber);
        }
    }

    @Override
    public PageInfo<ExpertLibDTO> list(ExpertLibQuery expertLibQuery) {
        String currentUserId = UserThreadContext.getUserId();
        expertLibQuery.setCurrentUserId(currentUserId);
        expertLibQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        expertLibQuery.setManagerAreaOrgIds(orgFeign.findUserManageAreaLevelPath(currentUserId));
        if (StringUtils.isNotBlank(expertLibQuery.getUserIds())) {
            expertLibQuery.setUserIdsVo(TranslateUtil.translateBySplit(expertLibQuery.getUserIds(), String.class));
        }
        PageInfo<ExpertLibDTO> pageInfo = PageMethod.startPage(expertLibQuery.getPageNo(), expertLibQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.list(expertLibQuery));
        List<String> userIds = pageInfo.getList().stream().map(ExpertLibDTO::getUserId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        Set<String> orgIdSet = userMap.values().stream().map(UserOrgDTO::getOrgId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
        // 案例条线
        Set<String> businessIds = getBusinessIds(pageInfo);
        Map<String, SysCategoryDTO> categoryMapByIdList = categoryFeign.getCategoryMapByIdList(businessIds);
        for (ExpertLibDTO expertLibDTO : pageInfo.getList()) {
            Optional.ofNullable(userMap.get(expertLibDTO.getUserId())).ifPresent(user -> {
                expertLibDTO.setUserName(user.getFullName());
                expertLibDTO.setUserLoginName(user.getLoginName());

                Optional.ofNullable(orgShowDTOMap.get(user.getOrgId())).ifPresent(orgShowDTO -> {
                    expertLibDTO.setOrgName(orgShowDTO.getOrgShortName());
                    expertLibDTO.setOrgPath(orgShowDTO.getLevelPathName());
                    expertLibDTO.setUserOrgName(user.getOrgName());
                });
            });
            Optional.ofNullable(expertLibDTO.getAuditOrgIds()).filter(StringUtils::isNotBlank).ifPresent(orgIds -> {
                List<String> orgIdList = TranslateUtil.translateBySplit(orgIds, String.class);
                List<String> orgNameList = orgFeign.getOrgNameByIds(orgIdList);
                expertLibDTO.setAuditOrgName(orgNameList.stream().collect(Collectors.joining(",")));
            });
            Optional.ofNullable(expertLibDTO.getAuditBusinessIds()).filter(StringUtils::isNotBlank).ifPresent(ids -> {
                List<String> businessIdsList =  TranslateUtil.translateBySplit(ids, String.class);
                List<String> bNames = new ArrayList<>();
                for (String bId : businessIdsList) {
                    SysCategoryDTO sysCategoryDTO = categoryMapByIdList.get(bId);
                    if (sysCategoryDTO != null) {
                        bNames.add(sysCategoryDTO.getCategoryName());
                    }
                }
                expertLibDTO.setAuditBusinessName(bNames.stream().collect(Collectors.joining(",")));
            });
        }
        return pageInfo;
    }

    private static Set<String> getBusinessIds(PageInfo<ExpertLibDTO> pageInfo) {
        Set<String> businessIdStrs = pageInfo.getList().stream().map(ExpertLibDTO::getAuditBusinessIds)
            .collect(Collectors.toSet());
        Set<String> businessIds = new HashSet<>();
        for (String businessIdStr : businessIdStrs) {
            if (StringUtils.isNotBlank(businessIdStr)) {
                businessIds.addAll(TranslateUtil.translateBySplit(businessIdStr, String.class));
            }
        }
        return businessIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(ExpertLibSaveDTO expertLibSaveDTO) {
        if (StringUtils.isNotBlank(expertLibSaveDTO.getId())) {
            update(expertLibSaveDTO);
            handelBusinessCategoryCanDel();
            return;
        }
        create(expertLibSaveDTO);
        handelBusinessCategoryCanDel();
    }

    private void handelBusinessCategoryCanDel() {
        // 条线有两处地方使用：专家、案例库
        List<Example> exampleList = exampleService.list();
        List<ExpertCategory> expertCategories = expertCategoryService.list(
            new LambdaQueryWrapper<ExpertCategory>().eq(ExpertCategory::getCategoryType,
                CategoryType.ExpertBusinessTags.name()));
        // 没地方使用，可删除
        if (CollectionUtils.isEmpty(exampleList) && CollectionUtils.isEmpty(expertCategories)) {
            categoryFeign.deleteCategoryByType(CategoryType.ExampleBusiness.name());
            return;
        }
        Set<String> collect = exampleList.stream().map(Example::getBusinessId).collect(Collectors.toSet());
        Set<String> collect1 = expertCategories.stream().map(ExpertCategory::getCategoryId).collect(Collectors.toSet());
        Set<String> all = new HashSet<>();
        all.addAll(collect);
        all.addAll(collect1);
        if (CollectionUtils.isEmpty(all)) {
            categoryFeign.deleteCategoryByType(CategoryType.ExampleBusiness.name());
            return;
        }
        categoryFeign.updateCategoryByListCanDel(all, CategoryType.ExampleBusiness.name());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String ids) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        baseMapper.deleteCategory(idList);
        List<Expert> experts = listByIds(idList);
        List<String> userIds = experts.stream().map(Expert::getUserId).collect(Collectors.toList());
        Map<String, UserDTO> simpleUserMap = userFeign.getSimpleUserMap(userIds);
        experts.forEach(expert -> {
            UserDTO userDTO = simpleUserMap.get(expert.getUserId());
            expertDao.delExpert(expert,
                Optional.ofNullable(userDTO).isPresent() ? userDTO.getFullName() : ""
            );
        });
    }

    @Override
    public ExpertInfoDTO expertInfo(String id) {
        Expert expert = getById(id);
        if (Optional.ofNullable(expert).isEmpty()) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_EXPERT_NULL);
        }
        ExpertInfoDTO expertInfo = new ExpertInfoDTO();
        expertInfo.setId(expert.getId());
        expertInfo.setUserId(expert.getUserId());
        UserDTO userInfo = userFeign.getUserById(expert.getUserId());
        Optional.ofNullable(userInfo).ifPresent(info -> {
            expertInfo.setUserLoginName(info.getLoginName());
            expertInfo.setUserName(info.getFullName());
            expertInfo.setUserOrgName(info.getOrgName());
        });
        LambdaQueryWrapper<ExpertCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpertCategory::getExpertId, expertInfo.getId());
        queryWrapper.eq(ExpertCategory::getCategoryType, CategoryType.ExpertOrgTags.toString());
        List<ExpertCategory> auditOrgInfo = expertCategoryService.list(queryWrapper);
        Set<String> orgIds = auditOrgInfo.stream().map(ExpertCategory::getCategoryId).collect(Collectors.toSet());
        Map<String, OrgDTO> orgMap = orgFeign.getOrgMapByIds(orgIds);
        if (!CollectionUtils.isEmpty(auditOrgInfo)) {
            expertInfo.setAuditOrgList(auditOrgInfo.stream().map(a -> {
                ViewLimitTypeDTO limitDTO = new ViewLimitTypeDTO();
                limitDTO.setCategoryId(a.getCategoryId());
                limitDTO.setCategoryType("OrgLimit");
                Optional.ofNullable(orgMap.get(a.getCategoryId())).ifPresent(orgInfo -> {
                    limitDTO.setCategoryName(orgInfo.getOrgName());
                });
                return limitDTO;
            }).collect(Collectors.toList()));
        }
        queryWrapper.clear();
        queryWrapper.eq(ExpertCategory::getExpertId, expertInfo.getId());
        queryWrapper.eq(ExpertCategory::getCategoryType, CategoryType.ExpertBusinessTags.toString());
        List<ExpertCategory> businessInfo = expertCategoryService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(businessInfo)) {
            expertInfo.setAuditBusinessIdList(
                businessInfo.stream().map(ExpertCategory::getCategoryId).collect(Collectors.toList()));
        }
        return expertInfo;
    }

    private void update(ExpertLibSaveDTO expertLibSaveDTO) {
        Expert update = new Expert();
        update.setId(expertLibSaveDTO.getId());
        saveOrUpdateCategory(expertLibSaveDTO);
        //更新并记录日志
        Expert expert = getById(expertLibSaveDTO.getId());
        UserDTO user = userFeign.getUserById(expert.getUserId());
        String userName = Optional.ofNullable(user).isPresent() ? user.getFullName() : "";
        expertDao.updateExpert(update, userName);
    }

    private void create(ExpertLibSaveDTO expertLibSaveDTO) {
        UserDTO createExpertUser = userFeign.getUserById(expertLibSaveDTO.getUserId());
        if (createExpertUser.getIsDel().equals(GeneralJudgeEnum.CONFIRM.getValue())) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_USER_DELETE);
        }
        if (count(new LambdaQueryWrapper<Expert>().eq(Expert::getUserId, expertLibSaveDTO.getUserId())) > 0) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_EXPERT_EXIST);
        }
        Expert create = new Expert();
        String expertId = StringUtil.newId();
        create.setId(expertId);
        expertLibSaveDTO.setId(expertId);
        saveOrUpdateCategory(expertLibSaveDTO);
        create.setExNumber(generateCode());
        create.setUserId(expertLibSaveDTO.getUserId());
        create.setOrgId(createExpertUser.getOrgId());
        expertDao.saveExpert(create, createExpertUser.getFullName());
    }

    private void saveOrUpdateCategory(ExpertLibSaveDTO expertLibSaveDTO) {
        if (StringUtils.isNotBlank(expertLibSaveDTO.getId())) {
            baseMapper.deleteCategory(Arrays.asList(expertLibSaveDTO.getId()));
        }
        List<SaveViewLimitDTO> auditOrgList = expertLibSaveDTO.getAuditOrgList();
        List<String> auditBusinessIdList = expertLibSaveDTO.getAuditBusinessIdList();
        if (!CollectionUtils.isEmpty(auditOrgList)) {
            List<String> auditOrgIdList = auditOrgList.stream().map(SaveViewLimitDTO::getCategoryId)
                .collect(Collectors.toList());
            saveExpertCate(auditOrgIdList, CategoryType.ExpertOrgTags.toString(), expertLibSaveDTO.getId());
        }
        if (!CollectionUtils.isEmpty(auditBusinessIdList)) {
            saveExpertCate(auditBusinessIdList, CategoryType.ExpertBusinessTags.toString(), expertLibSaveDTO.getId());
        }
    }

    private void saveExpertCate(List<String> categoryIdList, String categoryType, String expertId) {
        List<ExpertCategory> newExpertCateList = new ArrayList<>();
        for (String categoryId : categoryIdList) {
            ExpertCategory newExpertCate = new ExpertCategory();
            newExpertCate.setId(StringUtil.newId());
            newExpertCate.setExpertId(expertId);
            newExpertCate.setCategoryId(categoryId);
            newExpertCate.setCategoryType(categoryType);
            newExpertCateList.add(newExpertCate);
        }
        if (!CollectionUtils.isEmpty(newExpertCateList)) {
            expertCategoryService.saveBatch(newExpertCateList);
        }
    }

    private String generateCode() {
        return CodePrefixEnum.EXPERT.generateCode(CODE);
    }

    @Override
    @Async
    public void exportExpert(ExpertLibQuery expertLibQuery) {
        // 构建并实现接口类的方法 返回导出的接口类对象
        IExportDataDTO exportDataDTO = buildExportExpertDataDTO(expertLibQuery);
        exportComponent.exportRecord(exportDataDTO);
    }

    /**
     * 构建并实现接口类的方法 返回导出的接口类对象
     *
     * @param expertLibQuery
     * @return
     */
    public IExportDataDTO buildExportExpertDataDTO(ExpertLibQuery expertLibQuery) {
        return new IExportDataDTO() {
            @Override
            public List<Map<String, Object>> getData(Integer pageNo, Integer pageSize) {
                IExampleExpertService expertService = SpringUtil.getBean("exampleExpertService", IExampleExpertService.class);
                expertLibQuery.setExport(true);
                expertLibQuery.setPageNo(pageNo);
                expertLibQuery.setPageSize(pageSize);
                assert expertService != null;
                PageInfo<ExpertLibDTO> expertListByPage = expertService.list(expertLibQuery);
                List<Map<String, Object>> expertListExportDTOS = new ArrayList<>();
                for (ExpertLibDTO expertListDTO : expertListByPage.getList()) {
                    Map<String, Object> beanMap = JsonUtil.parseObjectToMap(expertListDTO);
                    expertListExportDTOS.add(beanMap);
                }
                return expertListExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ExpertLib;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ExpertLib.getType();
            }

        };
    }
}
