package com.wunding.learn.certification.service.client.dto;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 通过体系获得认证dto
 *
 * <AUTHOR>
 * @date 2022/09/01
 */
@Data
@Accessors(chain = true)
@Schema(name = "CertificationBySetupDTO", description = "通过体系获得认证对象")
public class CertificationBySetupDTO {

    @Schema(description = "认证体系id")
    private String setupId;

    @Schema(description = "认证体系名称")
    private String setupName;

    @Schema(description = "证书等级详情")
    private List<CertificationLevelListVo> certificationLevelListVos;

    @Schema(description = "证书分类详情")
    private List<CertCategoryListVo> certCategoryListVos;

    @Data
    @Accessors(chain = true)
    @Schema(name = "CertificationLevelListVo", description = "证书等级详情对象")
    public static class CertificationLevelListVo {

        @Schema(description = "证书等级id")
        private String levelId;

        @Schema(description = "证书等级名称")
        private String levelName;

        @Schema(description = "等级图标")
        private NamePath coverImage;

        @Schema(description = "证书详情")
        private List<CertificationClientDTO> certificationApiVos;
    }

    @Data
    @Accessors(chain = true)
    @Schema(name = "CertCategoryListVo", description = "证书分类详情对象")
    public static class CertCategoryListVo {

        @Schema(description = "证书分类id")
        private String certCategoryId;

        @Schema(description = "证书分类名称")
        private String certCategoryName;

        @Schema(description = "证书详情")
        private List<CertificationClientDTO> certificationApiVos;
    }

}
