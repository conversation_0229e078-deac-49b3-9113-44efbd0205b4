package com.wunding.learn.certification.service.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 证书关联类型
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenjinneng</a>
 * @since 2025/02/24
 */
public enum CertificationCategoryTypeEnum {

    /**
     * 课程
     */
    COURSE_RELATE("course", "课程", "CourseManagement"),

    /**
     * 调研
     */
    SURVEY_RELATE("survey", "调研", "OnlineSurvey"),

    /**
     * 考试
     */
    EXAM_RELATE("exam", "考试", "ExamManagement"),

    /**
     * 闯关游戏
     */
    PROMOTED_GAME("promotedGame", "闯关游戏", "PassGameManage"),

    /**
     * 学习项目
     */
    PROJECT_RELATE("project", "学习项目", "StudyProjManage"),

    /**
     * 评价
     */
    APPRAISE("appraise", "评价", "AssessCommonManagement"),

    /**
     * 招募
     */
    RECRUIT("recruit", "招募", "RecruitList"),

    /**
     * 资格认证
     */
    QUALIFICATION("qualification", "任职资格", "Qualifications"),

    /**
     * 培训班级-课程
     */
    TRAIN_CLASS("trainClass", "培训项目-班级", "TrainingProjManage"),

    /**
     * 学习地图
     */
    LEARN_MAP("learningMap", "学习地图", "LearningMap");

    /**
     * 类型
     */
    private final String type;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 后台路由
     */
    private final String router;

    CertificationCategoryTypeEnum(String type, String name, String router) {
        this.type = type;
        this.name = name;
        this.router = router;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getRouter() {
        return router;
    }

    public static List<CertificationCategoryTypeEnum> getAllCategoryType() {
        List<CertificationCategoryTypeEnum> list = new ArrayList<>();
        list.add(CertificationCategoryTypeEnum.COURSE_RELATE);
        list.add(CertificationCategoryTypeEnum.SURVEY_RELATE);
        list.add(CertificationCategoryTypeEnum.EXAM_RELATE);
        list.add(CertificationCategoryTypeEnum.PROMOTED_GAME);
        list.add(CertificationCategoryTypeEnum.PROJECT_RELATE);
        list.add(CertificationCategoryTypeEnum.APPRAISE);
        list.add(CertificationCategoryTypeEnum.RECRUIT);
        list.add(CertificationCategoryTypeEnum.QUALIFICATION);
        list.add(CertificationCategoryTypeEnum.TRAIN_CLASS);
        list.add(CertificationCategoryTypeEnum.LEARN_MAP);
        return list;
    }
}
