package com.wunding.learn.certification.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.certification.service.admin.dto.KnowResourceInfoClientDTO;
import com.wunding.learn.certification.service.admin.dto.KnowResourceInfoDTO;
import com.wunding.learn.certification.service.mapper.JobQualificationKnowResourceMapper;
import com.wunding.learn.certification.service.model.JobQualificationKnowResource;
import com.wunding.learn.certification.service.service.IJobQualificationKnowResourceService;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.course.api.dto.UserCourseRecordDTO;
import com.wunding.learn.course.api.service.CourseFeign;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 任职资格-知识内容-资源表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2024-01-19
 */
@Slf4j
@Service("jobQualificationKnowResourceService")
public class JobQualificationKnowResourceServiceImpl extends
    ServiceImpl<JobQualificationKnowResourceMapper, JobQualificationKnowResource> implements
    IJobQualificationKnowResourceService {

    @Resource
    private CourseFeign courseFeign;

    @Override
    public List<KnowResourceInfoDTO> getResourceList(String qualificationId) {
        List<KnowResourceInfoDTO> resourceList = baseMapper.getResourceList(qualificationId);
        if (!CollectionUtils.isEmpty(resourceList)) {
            Set<String> resourceIdList = resourceList.stream().map(KnowResourceInfoDTO::getResourceId)
                .collect(Collectors.toSet());
            ResourceBaseQuery resourceBaseQuery = new ResourceBaseQuery();
            resourceBaseQuery.setResourceIdList(resourceIdList);
            Map<String, ResourceBaseDTO> courseMap = courseFeign.getCourseBaseInfo(resourceBaseQuery);
            resourceList.forEach(
                resource -> Optional.ofNullable(courseMap.get(resource.getResourceId())).ifPresent(courseInfo -> {
                    resource.setResourceName(courseInfo.getTitle());
                    resource.setResourceIsDel(courseInfo.getIsDel());
                    resource.setResourceIsPublish(courseInfo.getIsPublish());
                    resource.setTotalCoursewareDuration(courseInfo.getTotalCoursewareDuration());
                }));
        }
        return resourceList;
    }

    @Override
    public void delResource(String qualificationId) {
        LambdaQueryWrapper<JobQualificationKnowResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JobQualificationKnowResource::getQualificationId, qualificationId);
        remove(queryWrapper);
    }

    @Override
    public List<KnowResourceInfoClientDTO> getUserResourceList(String qualificationId, String userId) {
        List<KnowResourceInfoDTO> list = baseMapper.getResourceList(qualificationId);
        List<KnowResourceInfoClientDTO> resourceList = BeanListUtils.copyList(list, KnowResourceInfoClientDTO.class);
        if (!CollectionUtils.isEmpty(resourceList)) {
            Set<String> resourceIdList = resourceList.stream().map(KnowResourceInfoClientDTO::getResourceId)
                .collect(Collectors.toSet());
            ResourceBaseQuery resourceBaseQuery = new ResourceBaseQuery();
            resourceBaseQuery.setResourceIdList(resourceIdList);
            Map<String, ResourceBaseDTO> courseMap = courseFeign.getCourseBaseInfo(resourceBaseQuery);
            resourceList.forEach(
                resource -> Optional.ofNullable(courseMap.get(resource.getResourceId())).ifPresent(courseInfo -> {
                    resource.setResourceName(courseInfo.getTitle());
                    resource.setResourceIsDel(courseInfo.getIsDel());
                    resource.setTotalCoursewareDuration(courseInfo.getTotalCoursewareDuration());
                    // 获取记录
                    UserCourseRecordDTO userCourseRecordDTO = courseFeign.getUserCourseRecord(userId,
                        resource.getResourceId());
                    if (null == userCourseRecordDTO) {
                        resource.setStatus(2);
                    } else {
                        resource.setStatus(userCourseRecordDTO.getIsLearned());
                        resource.setDuration(userCourseRecordDTO.getDuration());
                    }
                }));
        }
        return resourceList;
    }

    @Override
    public List<KnowResourceInfoDTO> getResourceListByQualificationId(String qualificationId) {
        return baseMapper.getResourceList(qualificationId);
    }
}
