package com.wunding.learn.certification.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/17
 */
@Data
@Schema(name = "TrainWithOutSummaryDTO",description = "外部培训报名人次对象")
public class TrainWithOutSummaryDTO {
    @Schema(description = "总人次")
    private Integer sum;

    @Schema(description = "内部未确认人次")
    private Integer unConfirm;

}
