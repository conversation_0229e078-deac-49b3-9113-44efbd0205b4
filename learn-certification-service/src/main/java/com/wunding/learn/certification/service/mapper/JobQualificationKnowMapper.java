package com.wunding.learn.certification.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.certification.service.model.JobQualificationKnow;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 任职资格-知识内容表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2024-01-19
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface JobQualificationKnowMapper extends BaseMapper<JobQualificationKnow> {

}
