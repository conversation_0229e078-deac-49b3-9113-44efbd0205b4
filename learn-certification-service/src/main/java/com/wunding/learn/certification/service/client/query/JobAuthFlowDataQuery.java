package com.wunding.learn.certification.service.client.query;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> 资格认证申请流程数据查询对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-03-05
 */
@Data
@Accessors(chain = true)
@Schema(name = "JobAuthFlowDataQuery", description = "资格认证申请流程数据查询对象")
public class JobAuthFlowDataQuery {

    @Parameter(description = "是否审核员 0否 1是")
    @NotNull(message = "是否审核员不能为空")
    private Integer isAuditor;

    /**
     * 资格认证申请id
     */
    @Schema(description = "资格认证申请id")
    @NotBlank(message = "资格认证申请id不能为空")
    private String authApplyRecordId;

    /**
     * 资格认证申请流程记录id
     */
    @Schema(description = "资格认证申请流程记录id")
    @NotBlank(message = "资格认证申请流程记录id不能为空")
    private String authApplyFlowRecordId;

}
