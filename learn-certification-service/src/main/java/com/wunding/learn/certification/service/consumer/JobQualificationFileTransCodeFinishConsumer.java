package com.wunding.learn.certification.service.consumer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rabbitmq.client.Channel;
import com.wunding.learn.certification.service.biz.IJobAuthExamineFlowJudgeBiz;
import com.wunding.learn.certification.service.client.dto.JobAuthApplyFlowDataCommitClientDTO;
import com.wunding.learn.certification.service.model.JobAuthApplyFlowRecord;
import com.wunding.learn.certification.service.model.JobAuthApplyRecord;
import com.wunding.learn.certification.service.model.JobAuthentication;
import com.wunding.learn.certification.service.model.JobQualification;
import com.wunding.learn.certification.service.service.IJobAuthApplyFlowRecordService;
import com.wunding.learn.certification.service.service.IJobAuthApplyRecordService;
import com.wunding.learn.certification.service.service.IJobAuthenticationService;
import com.wunding.learn.certification.service.service.IJobQualificationKnowService;
import com.wunding.learn.certification.service.service.IJobQualificationService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.TransCodeFinishEvent;
import com.wunding.learn.common.mq.event.appraise.MeetingProviderOperateEvent;
import com.wunding.learn.common.mq.event.certification.JobQualificationMeetingIsFinishResourceChangeEvent;
import com.wunding.learn.common.mq.event.certification.JobQualificationResourceChangeEvent;
import com.wunding.learn.common.mq.event.user.UserInfoChangeEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.user.api.service.UserFeign;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.BeanUtils;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 文件转码完成消息队列消费者
 *
 * <AUTHOR>
 * @date 2022/7/1
 */
@Component
@Slf4j
public class JobQualificationFileTransCodeFinishConsumer {

    /**
     * Routing Key
     */
    public static final String JOB_QUALIFICATION_FILE_TRANS_CODE_FINISH_ROUTING_KEY = "JOB_QUALIFICATION_FILE";

    /**
     * 课件转码完成消息队列
     */
    private static final String JOB_QUALIFICATION_FILE_TRANS_CODE_FINISH_CONSUMER_QUEUE = "jobQualificationFileTransCodeFinishConsumerQueue";

    @Resource
    private IJobQualificationService jobQualificationService;

    @Resource
    private IJobAuthenticationService jobAuthenticationService;

    @Resource
    private IJobAuthExamineFlowJudgeBiz jobAuthExamineFlowJudgeClientBiz;

    @Resource
    private IJobAuthApplyRecordService jobAuthApplyRecordService;

    @Resource
    private IJobAuthApplyFlowRecordService jobAuthApplyFlowRecordService;

    @Resource
    private UserFeign userFeign;


    /**
     * 文件转码完成回调通知处理器
     *
     * @param transCodeFinishEvent
     * @param deliveryTag
     * @param channel
     * @throws IOException
     */
    @RabbitListener(concurrency = "1", bindings = @QueueBinding(value = @Queue(value = JOB_QUALIFICATION_FILE_TRANS_CODE_FINISH_CONSUMER_QUEUE),
        exchange = @Exchange(value = TransCodeFinishEvent.EXCHANGE, type = ExchangeTypes.TOPIC),
        key = JOB_QUALIFICATION_FILE_TRANS_CODE_FINISH_ROUTING_KEY),
        id = "jobQualificationFileTransCodeFinishHandler")
    public void jobQualificationFileTransCodeFinishHandler(@Payload TransCodeFinishEvent transCodeFinishEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("jobQualificationFileTransCodeFinishHandler transCodeFinishEvent: {}",
            JsonUtil.objToJson(transCodeFinishEvent));

        UserThreadContext.setTenantId(transCodeFinishEvent.getTenantId());

        // 文件转码状态回调更新
        JobQualification jobQualification = new JobQualification();
        jobQualification.setId(transCodeFinishEvent.getBizId());
        jobQualification.setTransformStatus(transCodeFinishEvent.getStatus());
        jobQualification.setMime(transCodeFinishEvent.getMime());
        jobQualification.setUpdateBy("admin");
        jobQualification.setUpdateTime(new Date());
        jobQualificationService.updateById(jobQualification);
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(transCodeFinishEvent, channel, deliveryTag, false);
    }


    private static final String JOB_QUALIFICATION_RESOURCE_CHANGE_CONSUMER_QUEUE = "jobQualificationResourceChangeConsumerQueue";

    @Resource
    private IJobQualificationKnowService jobQualificationKnowService;

    /**
     * 任职资格绑定资源修改处理器
     *
     * @param changeEvent
     * @param deliveryTag
     * @param channel
     * @throws IOException
     */
    @RabbitListener(concurrency = "1", bindings = @QueueBinding(value = @Queue(value = JOB_QUALIFICATION_RESOURCE_CHANGE_CONSUMER_QUEUE),
        exchange = @Exchange(value = JobQualificationResourceChangeEvent.JOB_QUALIFICATION_RESOURCE_CHANGE_EXCHANGE, type = ExchangeTypes.TOPIC),
        key = JobQualificationResourceChangeEvent.JOB_QUALIFICATION_RESOURCE_CHANGE_KEY),
        id = "jobQualificationResourceChangeHandler")
    public void jobQualificationResourceChangeHandler(@Payload JobQualificationResourceChangeEvent changeEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("jobQualificationResourceChangeHandler transCodeFinishEvent: {}", JsonUtil.objToJson(changeEvent));

        UserThreadContext.setTenantId(changeEvent.getTenantId());

        // 绑定资源修改回调同步
        jobQualificationKnowService.synchronizationResource(changeEvent.getBindId(), changeEvent.getContentMode());
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(changeEvent, channel, deliveryTag, false);
    }

    /**
     * 答辩会完成初始化下一部公示，执行提交审核
     *
     * @param changeEvent
     * @param deliveryTag
     * @param channel
     * @throws IOException
     */
    @RabbitListener(concurrency = "1", bindings = @QueueBinding(value = @Queue(value = JOB_QUALIFICATION_RESOURCE_CHANGE_CONSUMER_QUEUE),
        exchange = @Exchange(value = JobQualificationResourceChangeEvent.JOB_QUALIFICATION_RESOURCE_CHANGE_EXCHANGE, type = ExchangeTypes.TOPIC),
        key = JobQualificationResourceChangeEvent.JOB_QUALIFICATION_RESOURCE_CHANGE_KEY),
        id = "jobQualificationMeetingIsFinishResourceChangeHandler")
    public void jobQualificationMeetingIsFinishResourceChangeHandler(
        @Payload JobQualificationMeetingIsFinishResourceChangeEvent changeEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("jobQualificationMeetingIsFinishResourceChangeHandler transCodeFinishEvent: {}",
            JsonUtil.objToJson(changeEvent));

        UserThreadContext.setTenantId(changeEvent.getTenantId());

        // 初始化下一步
        JobAuthApplyFlowDataCommitClientDTO dto = new JobAuthApplyFlowDataCommitClientDTO();
        BeanUtils.copyProperties(changeEvent, dto);
        jobAuthExamineFlowJudgeClientBiz.commitMyJudgeById(dto);
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(changeEvent, channel, deliveryTag, false);
    }


    /**
     * 答辩完成修改用户申请记录答辩状态
     *
     * @param meetingProviderOperateEvent
     * @param deliveryTag
     * @param channel
     * @throws IOException
     */
    @RabbitListener(concurrency = "1", bindings = @QueueBinding(value = @Queue(value = MeetingProviderOperateEvent.MEETING_PROVIDER_STUDY_QUEUE),
        exchange = @Exchange(value = MeetingProviderOperateEvent.MEETING_PROVIDER_CHANGE, type = ExchangeTypes.TOPIC),
        key = MeetingProviderOperateEvent.MEETING_PROVIDER_ROUTE_KEY),
        id = "updateApplyRecordMeetingStatus")
    public void updateApplyRecordMeetingStatus(@Payload MeetingProviderOperateEvent meetingProviderOperateEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("updateApplyRecordMeetingStatus meetingProviderOperateEvent: {}",
            JsonUtil.objToJson(meetingProviderOperateEvent));

        UserThreadContext.setTenantId(meetingProviderOperateEvent.getTenantId());

        // 答辩完成修改用户申请记录答辩状态
        if (!CollectionUtils.isEmpty(meetingProviderOperateEvent.getUserIdList())) {
            Optional.ofNullable(jobQualificationService.getById(
                meetingProviderOperateEvent.getJobQualificationId())).ifPresent(jobQualification -> {
                LambdaQueryWrapper<JobAuthentication> jobAuthenticationQuery = new LambdaQueryWrapper<>();
                jobAuthenticationQuery.eq(JobAuthentication::getPostId, jobQualification.getPostId());
                Optional.ofNullable(jobAuthenticationService.getOne(jobAuthenticationQuery))
                    .ifPresent(jobAuthentication -> {
                        for (String userId : meetingProviderOperateEvent.getUserIdList()) {
                            LambdaQueryWrapper<JobAuthApplyRecord> authApplyRecordQuery = new LambdaQueryWrapper<>();
                            authApplyRecordQuery.eq(JobAuthApplyRecord::getAuthenticationId, jobAuthentication.getId());
                            authApplyRecordQuery.eq(JobAuthApplyRecord::getApplyUserId, userId);
                            Optional.ofNullable(jobAuthApplyRecordService.getOne(authApplyRecordQuery))
                                .ifPresent(applyRecord -> {
                                    LambdaUpdateWrapper<JobAuthApplyRecord> jobAuthApplyRecordUpdate = new LambdaUpdateWrapper<>();
                                    jobAuthApplyRecordUpdate.set(JobAuthApplyRecord::getMeetingStatus,
                                        meetingProviderOperateEvent.getOperateType());
                                    jobAuthApplyRecordUpdate.eq(JobAuthApplyRecord::getId, applyRecord.getId());
                                    jobAuthApplyRecordService.update(jobAuthApplyRecordUpdate);
                                });
                        }
                    });
            });
        }
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(meetingProviderOperateEvent, channel, deliveryTag, false);
    }

    /**
     * 答辩完成修改用户申请记录答辩状态
     *
     * @param userInfoChangeEvent
     * @param deliveryTag
     * @param channel
     * @throws IOException
     */
    @RabbitListener(concurrency = "1", bindings = @QueueBinding(value = @Queue(value = UserInfoChangeEvent.USER_INFO_CHANGE_QUEUE),
        exchange = @Exchange(value = UserInfoChangeEvent.USER_INFO_CHANGE_CHANGE, type = ExchangeTypes.TOPIC),
        key = UserInfoChangeEvent.USER_INFO_CHANGE_ROUTE_KEY),
        id = "updateApplyFlowRecordSuperiorId")
    public void updateApplyFlowRecordSuperiorId(@Payload UserInfoChangeEvent userInfoChangeEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("updateApplyFlowRecordSuperiorId userInfoChangeEvent: {}",
            JsonUtil.objToJson(userInfoChangeEvent));
        ConsumerAckUtil.basicAck(userInfoChangeEvent, channel, deliveryTag, false);

        UserThreadContext.setTenantId(userInfoChangeEvent.getTenantId());

        // 该用户所有当前流程在上级审核的流程记录
        List<JobAuthApplyFlowRecord> jobAuthApplyFlowRecordList = jobAuthApplyFlowRecordService.getCurrentSuperiorFlow(
            userInfoChangeEvent.getUserId());
        if (!CollectionUtils.isEmpty(jobAuthApplyFlowRecordList)) {
            String superiorId = Optional.ofNullable(userFeign.getSuperiorIdByUserId(userInfoChangeEvent.getUserId()))
                .orElse(StringUtils.EMPTY);
            LambdaUpdateWrapper<JobAuthApplyFlowRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(JobAuthApplyFlowRecord::getApplySuperiorId, superiorId);
            updateWrapper.in(JobAuthApplyFlowRecord::getId,
                jobAuthApplyFlowRecordList.stream().map(JobAuthApplyFlowRecord::getId).collect(
                    Collectors.toList()));
            jobAuthApplyFlowRecordService.update(updateWrapper);
        }
        UserThreadContext.remove();
    }
}
