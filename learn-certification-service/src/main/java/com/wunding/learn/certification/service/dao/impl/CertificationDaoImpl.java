package com.wunding.learn.certification.service.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.certification.service.dao.CertificationDao;
import com.wunding.learn.certification.service.mapper.CertificationMapper;
import com.wunding.learn.certification.service.model.Certification;
import com.wunding.learn.common.aop.log.annotation.Log;
import com.wunding.learn.common.aop.log.annotation.Log.Type;
import org.springframework.stereotype.Repository;

/**
 * 认证 数据操作类
 *
 * <AUTHOR>
 * @date 2024-02-08
 */
@Repository("certificationDao")
public class CertificationDaoImpl extends ServiceImpl<CertificationMapper, Certification> implements CertificationDao {

    @Override
    @Log(type = Log.Type.CREATE, targetId = "#certification.id", targetName = "#certification.name", targetType = Log.TargetType.CERTIFICATION)
    public void saveCertification(Certification certification) {
        save(certification);
    }

    @Override
    @Log(type = Type.UPDATE, targetId = "#certification.id", targetName = "#certification.name", targetType = Log.TargetType.CERTIFICATION)
    public void updateCertification(Certification certification) {
        updateById(certification);
    }

    @Override
    @Log(type = Type.DELETE, targetId = "#certification.id", targetName = "#certification.name", targetType = Log.TargetType.CERTIFICATION)
    public void delCertification(Certification certification) {
        removeById(certification);
    }

    @Override
    @Log(type = Type.ENABLED, targetId = "#certification.id", targetName = "#certification.name", targetType = Log.TargetType.CERTIFICATION)
    public void enableCertification(Certification certification) {
        updateById(certification);
    }

    @Override
    @Log(type = Type.DISABLED, targetId = "#certification.id", targetName = "#certification.name", targetType = Log.TargetType.CERTIFICATION)
    public void disableCertification(Certification certification) {
        updateById(certification);
    }
}
