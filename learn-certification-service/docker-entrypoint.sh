#!/bin/bash
mkdir -p /data/logs/certification/
java -javaagent:/app/BOOT-INF/lib/transmittable-thread-local-2.14.2.jar \
 -XX:+UnlockDiagnosticVMOptions \
 -XX:+UnlockExperimentalVMOptions \
 -XX:-OmitStackTraceInFastThrow \
 -Dfile.encoding=UTF-8 \
 -Djava.security.egd=file:/dev/./urandom \
 -Dnetworkaddress.cache.ttl=10  \
 -XX:MaxGCPauseMillis=100 \
 -XX:G1HeapRegionSize=32m \
 --add-opens java.base/java.lang=ALL-UNNAMED \
 --add-opens java.base/java.math=ALL-UNNAMED \
 --add-opens java.base/java.util=ALL-UNNAMED \
 --add-opens java.base/java.net=ALL-UNNAMED \
 --add-opens java.base/java.lang.invoke=ALL-UNNAMED \
 -XX:+HeapDumpOnOutOfMemoryError \
 -XX:HeapDumpPath=/data/logs/certification/heap.hprof \
 -Duser.timezone=GMT+08 \
 org.springframework.boot.loader.launch.JarLauncher