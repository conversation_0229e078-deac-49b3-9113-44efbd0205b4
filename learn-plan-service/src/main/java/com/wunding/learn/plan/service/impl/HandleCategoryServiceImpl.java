package com.wunding.learn.plan.service.impl;

import com.wunding.learn.plan.service.HandleCategoryService;
import com.wunding.learn.user.api.service.CategoryFeign;
import java.util.Collection;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service("handleCategoryService")
public class HandleCategoryServiceImpl implements HandleCategoryService {
    
    @Resource
    private CategoryFeign categoryFeign;
    
    @Override
    public void handleCategoryCanDel(Collection<String> categoryIds, String type) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            categoryFeign.deleteCategoryByType(type);
        } else {
            categoryFeign.updateCategoryByListCanDel(categoryIds, type);
        }
    }
}
