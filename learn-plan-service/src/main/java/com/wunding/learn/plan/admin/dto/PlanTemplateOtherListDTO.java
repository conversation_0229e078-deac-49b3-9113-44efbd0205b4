package com.wunding.learn.plan.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 表单模板配置字段列表
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "TemplateOtherListDTO", description = "表单模板配置字段列表")
public class PlanTemplateOtherListDTO {

    /**
     * id
     */
    @Schema(description = "id")
    private String id;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer sortNo;

    /**
     * 是否启用 0-否 1-是
     */
    @Schema(description = "是否启用 0-否 1-是")
    private Integer isEnable;

    /**
     * 字段名称
     */
    @Schema(description = "字段名称")
    private String fieldName;


}
