package com.wunding.learn.plan.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.category.service.impl.CategorysServiceImpl;
import com.wunding.learn.plan.admin.dto.PlanInventoryDTO;
import com.wunding.learn.plan.admin.dto.PlanInventorySaveDTO;
import com.wunding.learn.plan.admin.query.PlanInventoryQuery;
import com.wunding.learn.plan.api.service.PlanInventoryFeign;
import com.wunding.learn.plan.api.service.dto.EnrollMemberInfoVO;
import com.wunding.learn.plan.api.service.dto.EnrollMemberResourceAo;
import com.wunding.learn.plan.api.service.dto.EnrollMemberResourceVo;
import com.wunding.learn.plan.api.service.dto.ImportApplyDTO;
import com.wunding.learn.plan.api.service.dto.PlanInventoryFeignDTO;
import com.wunding.learn.plan.model.Plan;
import com.wunding.learn.plan.model.PlanInventory;
import com.wunding.learn.plan.service.IEnrollMemberResourceService;
import com.wunding.learn.plan.service.IPlanInventoryService;
import com.wunding.learn.plan.service.IPlanService;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 计划清单实现
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/3/22 16:14
 */
@RestController
@Slf4j
@RequestMapping("${module.plan.contentPath:/}")
public class PlanInventoryFeignImpl implements PlanInventoryFeign {

    @Resource
    private IPlanInventoryService planInventoryService;

    @Resource
    private IEnrollMemberResourceService enrollMemberResourceService;

    @Resource
    private IPlanService planService;

    @Override
    public Map<String, String> getPlanNameBatchPlanInventoryIds(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new HashMap<>();
        }

        List<PlanInventory> planInventoryList = planInventoryService.findAllPlanInventory(batchIds);

        if (CollectionUtils.isEmpty(planInventoryList)) {
            return new HashMap<>();
        }

        Map<String, String> inventoryPlanMap = planInventoryList.stream()
            .collect(Collectors.toMap(PlanInventory::getId, PlanInventory::getPlanId, (key1, key2) -> key1));

        Set<String> planIds = planInventoryList.stream().map(PlanInventory::getPlanId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(planIds)) {
            return new HashMap<>();
        }
        List<Plan> planList = planService.findAllPlan(planIds);
        Map<String, String> planNameMap = planList.stream()
            .collect(Collectors.toMap(Plan::getId, Plan::getPlanName, (key1, key2) -> key1));

        Map<String, String> result = new HashMap<>();

        for (Map.Entry<String, String> entry : inventoryPlanMap.entrySet()) {
            String inventoryId = entry.getKey();
            String planId = entry.getValue();
            String planName = null;
            if(StringUtils.isNotBlank(planId)){
                planName = planNameMap.get(planId);
            }
            result.put(inventoryId, planName);
        }

        return result;
    }

    @Override
    public String getPlanIdByInventoryId(String inventoryId) {
        PlanInventory data = planInventoryService.getById(inventoryId);
        if (Objects.isNull(data)) {
            return StringUtils.EMPTY;
        }
        return data.getPlanId();
    }

    @Override
    public List<String> applyCheckAndSave(ImportApplyDTO importApplyDTO) {
        return planInventoryService.applyCheckAndSave(importApplyDTO);
    }

    @Override
    public void saveOrUpdatePlanInventory(PlanInventoryFeignDTO inventoryFeignDTO) {
        PlanInventorySaveDTO saveDTO = new PlanInventorySaveDTO();
        Optional.ofNullable(planInventoryService.getOne(new LambdaQueryWrapper<PlanInventory>()
            .eq(PlanInventory::getPlanId, inventoryFeignDTO.getPlanId())
            .eq(PlanInventory::getUserId, inventoryFeignDTO.getUserId()).last("limit 1")))
            .ifPresent(planInventory -> inventoryFeignDTO.setId(planInventory.getId()));
        BeanUtils.copyProperties(inventoryFeignDTO, saveDTO);
        planInventoryService.saveOrUpdatePlanInventory(saveDTO);
    }

    @Override
    public void saveOrUpdateDefaultApply(EnrollMemberResourceAo inventoryFeignDTO) {
        // 调用实现
        enrollMemberResourceService.saveOrUpdateDefaultApply(inventoryFeignDTO);
    }

    @Override
    public void saveDefaultApplyBatch(Collection<EnrollMemberResourceAo> inventoryFeignDTOCollection) {
        log.info("<<<<<<<<<<<<<< PlanInventoryFeign saveDefaultApplyBatch: data size {}", inventoryFeignDTOCollection.size());
        if (CollectionUtils.isEmpty(inventoryFeignDTOCollection)){
            return ;
        }
        long startTime = System.currentTimeMillis();
        inventoryFeignDTOCollection.forEach(inventoryFeignDTO -> {
            enrollMemberResourceService.saveOrUpdateDefaultApply(inventoryFeignDTO);
        });
        log.info(">>>>>>>>>>>>>>> PlanInventoryFeign saveDefaultApplyBatch:  spend {}",
             System.currentTimeMillis() - startTime);
    }

    @Override
    public Map<String, PlanInventoryFeignDTO> getApplyMapByUserIds(String resourcesId, Set<String> userIds) {
        if (StringUtils.isBlank(resourcesId) || CollectionUtils.isEmpty(userIds)) {
            return new HashMap<>();
        }
        PlanInventoryQuery query = new PlanInventoryQuery();
        query.setPlanId(resourcesId);
        query.setUserIds(userIds);
        query.setExport(true);
        query.setPageSize(Integer.MAX_VALUE);
        // 根据用户id查询报名信息列表
        PageInfo<PlanInventoryDTO> pageInfo = planInventoryService.selectApplyInventoryByPage(query);
        List<PlanInventoryDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        // 封装数据
        List<PlanInventoryFeignDTO> result = new ArrayList<>();
        list.forEach(planInventoryDTO -> {
            PlanInventoryFeignDTO dto = new PlanInventoryFeignDTO();
            BeanUtils.copyProperties(planInventoryDTO, dto);
            result.add(dto);
        });
        return result.stream().collect(
            Collectors.toMap(PlanInventoryFeignDTO::getUserId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public Map<String, EnrollMemberInfoVO> getDefaultApplyMapByUserIds(Set<String> applyRecordIds) {
        return enrollMemberResourceService.getDefaultApplyMapByUserIds(applyRecordIds);
    }

    @Override
    public EnrollMemberInfoVO getEnrollMemberInfo(String enrollId, String userId) {
        return enrollMemberResourceService.getEnrollMemberInfoByEnrollIdAndUserId(enrollId, userId);
    }

    @Override
    public EnrollMemberInfoVO getEnrollMemberInfoByApplyRecordId(String applyRecordId) {
        return enrollMemberResourceService.getEnrollMemberInfoByApplyRecordId(applyRecordId);
    }

    @Override
    public Map<String, EnrollMemberResourceVo> getEnrollMemberResourceVoByApplyRecordIds(
        Collection<String> applyRecordIds) {
        return enrollMemberResourceService.getEnrollMemberResourceVoByApplyRecordIds(applyRecordIds);
    }

    @Override
    public List<String> getCompanyPhone(String phone) {
        // 过滤联系人和单位联系人都是自己的情况(相当于自己开票)
        return enrollMemberResourceService.getCompanyPhone(phone);
    }

    @Override
    public void deleteApplyInfo(String applyId, String userId) {
        if (StringUtils.isNotBlank(applyId) && StringUtils.isNotBlank(userId)) {
            planInventoryService.remove(new LambdaQueryWrapper<PlanInventory>()
                .eq(PlanInventory::getPlanId, applyId)
                .eq(PlanInventory::getUserId, userId));
        }
    }
}
