package com.wunding.learn.plan.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.ResourceMemberDTO;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.form.enums.RelevanceTypeEnums;
import com.wunding.learn.common.form.service.IFormTemplateRelevanceService;
import com.wunding.learn.common.mq.event.plan.FormDeleteEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.plan.admin.dto.PlanImportPlanInventoryDTO;
import com.wunding.learn.plan.admin.dto.PlanInventoryDTO;
import com.wunding.learn.plan.admin.dto.PlanInventorySaveDTO;
import com.wunding.learn.plan.admin.dto.PlanTemplateColumnDTO;
import com.wunding.learn.plan.admin.query.PlanInventoryQuery;
import com.wunding.learn.plan.admin.query.PlanTemplateColumnQuery;
import com.wunding.learn.plan.api.service.FormManageFeign;
import com.wunding.learn.plan.api.service.dto.PlanImportInventoryFeignDTO;
import com.wunding.learn.plan.api.service.dto.PlanInventoryFeignDTO;
import com.wunding.learn.plan.api.service.dto.PlanTemplateColumnFeignDTO;
import com.wunding.learn.plan.api.service.query.PlanInventoryFeignQuery;
import com.wunding.learn.plan.api.service.query.PlanTemplateColumnFeignQuery;
import com.wunding.learn.plan.mapper.PlanFormTemplateMapper;
import com.wunding.learn.plan.model.PlanFormTemplate;
import com.wunding.learn.plan.model.PlanInventory;
import com.wunding.learn.plan.service.IPlanFormTemplateService;
import com.wunding.learn.plan.service.IPlanInventoryService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/3/29
 */
@RestController
@Slf4j
@RequestMapping("${module.plan.contentPath:/}")
public class FormManageFeignImpl implements FormManageFeign {

    @Resource(name = "planInventoryService")
    private IPlanInventoryService planInventoryService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private IPlanFormTemplateService planFormTemplateService;
    @Resource
    private IFormTemplateRelevanceService formTemplateRelevanceService;

    @Resource
    private PlanFormTemplateMapper planFormTemplateMapper;


    @Override
    public PageInfo<PlanInventoryFeignDTO> getFormRecord(PlanInventoryFeignQuery query) {
        log.debug("getFormRecord param:{}", query);
        PlanInventoryQuery targetQuery = new PlanInventoryQuery();
        BeanUtils.copyProperties(query, targetQuery);
        PageInfo<PlanInventoryFeignDTO> targetPage = new PageInfo<>();
        PageInfo<PlanInventoryDTO> sourcePage = planInventoryService.selectInventoryByPage(targetQuery);
        BeanUtils.copyProperties(sourcePage, targetPage);
        List<PlanInventoryDTO> sourceList = sourcePage.getList();
        List<PlanInventoryFeignDTO> targetList = new ArrayList<>();
        if (null != sourceList) {
            targetList = BeanListUtils.copyListProperties(sourceList, PlanInventoryFeignDTO::new);
        }
        targetPage.setList(targetList);
        log.debug("getFormRecord result:{}", targetPage);
        return targetPage;
    }

    @Override
    public List<PlanTemplateColumnFeignDTO> getConfigTemplateColumn(PlanTemplateColumnFeignQuery query) {
        log.debug("getConfigTemplateColumn param:{}", query);
        PlanTemplateColumnQuery targetQuery = new PlanTemplateColumnQuery();
        BeanUtils.copyProperties(query, targetQuery);
        List<PlanTemplateColumnDTO> source = planInventoryService.getConfigTemplateColumn(targetQuery);
        List<PlanTemplateColumnFeignDTO> target = BeanListUtils.copyListProperties(source, PlanTemplateColumnFeignDTO::new);
        log.debug("getConfigTemplateColumn result:{}", target);
        return target;
    }

    @Override
    public List<String> importFormRecord(PlanImportInventoryFeignDTO importInventoryFeignDTO) {
        log.debug("importFormRecord param:{}", importInventoryFeignDTO);
        PlanImportPlanInventoryDTO importPlanInventoryDTO = new PlanImportPlanInventoryDTO();
        BeanUtils.copyProperties(importInventoryFeignDTO, importPlanInventoryDTO);
        List<String> list = planInventoryService.importFormRecord(importPlanInventoryDTO);
        log.debug("importFormRecord result:{}", list);

        return list;
    }


    @Override
    public void deleteFormRecord(String id) {
        log.debug("deleteFormRecord param:{}", id);
        if (!StringUtils.hasText(id)) {
            return;
        }
        List<String> list = Arrays.asList(id.split(","));
        LambdaQueryWrapper<PlanInventory> query = new LambdaQueryWrapper<>();
        query.in(PlanInventory::getId, list);
        List<PlanInventory> inventories = planInventoryService.getBaseMapper().selectList(query);

        planInventoryService.removeByIds(list);
        for (PlanInventory inventory : inventories) {
            //删除相应的记录分，根据userId, activeId
            String activeId = inventory.getPlanId();
            String userId = inventory.getCreateBy();
            //TODO 发消息删除数据
            FormDeleteEvent event = new FormDeleteEvent(userId, activeId);
            mqProducer.sendMsg(event);

        }
    }

    @Override
    public String getTempIdByResourceId(String id) {
        log.debug("getTempIdByResourceId param:{}", id);
        return planInventoryService.getTempIdByResourceId(id);
    }

    @Override
    public String exportTemplate(PlanTemplateColumnFeignQuery query) {
        log.debug("exportTemplate param:{}", query);
        PlanTemplateColumnQuery targetQuery = new PlanTemplateColumnQuery();
        BeanUtils.copyProperties(query, targetQuery);
        String result = planInventoryService.exportTemplate(targetQuery);
        log.debug("exportTemplate result:{}", result);
        return result;
    }


    @Override
    public Map<String, String> getNameBatchIds(Collection<String> batchIds) {
        log.debug("getNameBatchIds param:{}", batchIds);
        return planFormTemplateService.getBaseMapper().selectBatchIds(batchIds).stream()
            .collect(Collectors.toMap(PlanFormTemplate::getId, PlanFormTemplate::getFormName, (key1, key2) -> key1));
    }

    @Override
    public Map<String, ResourceMemberDTO> getResourceMemberBatchIds(Collection<String> batchIds) {
        Map<String, String> map = getNameBatchIds(batchIds);

        return map.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry -> {
            ResourceMemberDTO resourceMemberDTO = new ResourceMemberDTO();
            resourceMemberDTO.setName(entry.getValue());
            return resourceMemberDTO;
        }));
    }


    @Override
    public PlanInventoryFeignDTO getFormDetail(String planId, String templateId) {
        String userId = UserThreadContext.getUserId();
        log.debug("getFormDetail planId:{},templateId:{},userId:{}", planId, templateId, userId);
        PlanInventoryFeignDTO target = new PlanInventoryFeignDTO();

        LambdaQueryWrapper<PlanInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanInventory::getTemplateId, templateId)
            .eq(PlanInventory::getPlanId, planId)
            .eq(PlanInventory::getCreateBy, userId).getSqlFirst();
        PlanInventory source = planInventoryService.getOne(queryWrapper);
        if (null == source) {
            return target;
        }
        BeanUtils.copyProperties(source, target);
        log.debug("getFormDetail result:{}", target);
        return target;
    }

    @Override
    public void saveFormDetail(PlanInventoryFeignDTO dto) {
        log.debug("saveFormDetail param:{}", dto);
        if (null == dto) {
            return;
        }
        PlanInventorySaveDTO target = new PlanInventorySaveDTO();
        BeanUtils.copyProperties(dto, target);
        planInventoryService.saveOrUpdatePlanInventory(target);
    }

    @Override
    public Map<String, Integer> getFormFinish(ResourceUserQuery resourceUserQuery) {
        Map<String, Integer> map = new HashMap<>();
        if (org.apache.commons.lang3.StringUtils.isBlank(resourceUserQuery.getResourceId()) || CollectionUtils.isEmpty(
            resourceUserQuery.getUserIdList())) {
            return map;
        }
        LambdaQueryWrapper<PlanInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanInventory::getPlanId, resourceUserQuery.getResourceId());
        queryWrapper.eq(PlanInventory::getStatus, 2);
        queryWrapper.in(PlanInventory::getCreateBy, resourceUserQuery.getUserIdList());
        planInventoryService.list(queryWrapper).forEach(dto -> map.put(dto.getCreateBy(), GeneralJudgeEnum.CONFIRM.getValue()));
        return map;
    }
    
    @Override
    public PlanInventoryFeignDTO getApplyFormDetail(String templateId, String applyId) {
        String userId = UserThreadContext.getUserId();
        log.debug("getApplyFormDetail templateId:{},userId:{}", templateId, userId);
        PlanInventoryFeignDTO target = new PlanInventoryFeignDTO();
    
        LambdaQueryWrapper<PlanInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanInventory::getTemplateId, templateId)
            .eq(PlanInventory::getPlanId, applyId)
            .eq(PlanInventory::getUserId, userId)
            .getSqlFirst();
        PlanInventory source = planInventoryService.getOne(queryWrapper);
        if (null == source) {
            return target;
        }
        BeanUtils.copyProperties(source, target);
        log.debug("getApplyFormDetail result:{}", target);
        return target;
    }


    @Override
    public void changeFormRelevance(String formTemplateId,String resourceId,String resourceName,String model){
        //修改-资源表单关联资源配置
        formTemplateRelevanceService.changeFormTemplateRelevance(formTemplateId,resourceId,
            resourceId,resourceName, model,
            RelevanceTypeEnums.FULL.getCode());
    }

    @Override
    public void removeFormRelevance(String resourceId){
        //移除-资源表单关联资源配置
        formTemplateRelevanceService.removeFormTemplateRelevance(resourceId);
    }

    @Override
    public ResourceDeleteInfoDTO getFormIsDelById(String resourceId) {
        return planFormTemplateMapper.getFormIsDelById(resourceId);
    }
}
