package com.wunding.learn.user.api.dto.examine;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 审核配置流程评分标准表dto对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "ExamineFlowCriterionDTO", description = "审核配置流程评分标准表dto对象")
public class ExamineFlowCriterionDTO {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String questionName;

    /**
     * 评分标准
     */
    @Schema(description = "评分标准")
    private String pointDesc;

    /**
     * 分制
     */
    @Schema(description = "分制")
    private Integer scoreInstitution;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNo;

}
