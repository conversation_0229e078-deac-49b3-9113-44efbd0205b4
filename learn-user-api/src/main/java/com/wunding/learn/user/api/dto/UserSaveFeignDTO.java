package com.wunding.learn.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统用户表
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(name = "UserSaveFeignDTO", description = "系统用户表")
public class UserSaveFeignDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String id;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String employeeNo;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * 用户全名
     */
    @Schema(description = "用户全名")
    private String fullName;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nikeName;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;

    /**
     * 所属部门id
     */
    @Schema(description = "所属部门id")
    private String orgId;

    /**
     * 工作区域
     */
    @Schema(description = "工作区域")
    private String workingArea;

    /**
     * 姓
     */
    @Schema(description = "姓")
    private String firstName;

    /**
     * 名
     */
    @Schema(description = "名")
    private String lastName;

    /**
     * 用户全名的拼音
     */
    @Schema(description = "用户全名的拼音")
    private String pinyin;

    /**
     * 性别 1：男 2：女
     */
    @Schema(description = "性别 1：男 2：女")
    private Integer sex;

    /**
     * 工作电话
     */
    @Schema(description = "工作电话")
    private String workPhone;

    /**
     * 手机
     */
    @Schema(description = "手机")
    private String telephone;

    /**
     * 短号
     */
    @Schema(description = "短号")
    private String shortMobile;

    /**
     * 邮件
     */
    @Schema(description = "邮件")
    private String email;

    /**
     * 是否锁定
     */
    @Schema(description = "是否锁定")
    private Integer isLock;

    /**
     * 是否可用
     */
    @Schema(description = "是否可用")
    private Integer isAvailable;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDel;

    /**
     * 新增人
     */
    @Schema(description = "新增人")
    private String createBy;

    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    private Date createTime;

    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    private String updateBy;

    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    private Date updateTime;

    /**
     * 是否为超级管理员
     */
    @Schema(description = "是否为超级管理员")
    private Integer isSuper;

    /**
     * 等级
     */
    @Schema(description = "等级")
    private String userLevelId;

    /**
     * 是否专家
     */
    @Schema(description = "是否专家")
    private Integer isExpert;

    /**
     * 直播用户id
     */
    @Schema(description = "直播用户id")
    private BigDecimal liveUserId;

    /**
     * 是否部门终审员 0不是 1是
     */
    @Schema(description = "是否部门终审员 0不是 1是")
    private Integer lsDepartAdmin;

    /**
     * 生日
     */
    @Schema(description = "生日")
    private Date birthday;

    /**
     * 入职日期
     */
    @Schema(description = "入职日期")
    private Date joinDate;

    /**
     * 在职状态,0为离职，1为在职，-1为删除，默认1
     */
    @Schema(description = "在职状态,0为离职，1为在职，-1为删除，默认1")
    private Integer isWork;

    /**
     * 学历
     */
    @Schema(description = "学历")
    private String education;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    private String idNumber;


    /**
     * 用户类型[0：内部用户，1：外部用户，2：机构用户，默认0]
     */
    @Schema(description = "用户类型[0：内部用户，1：外部用户，2：机构用户，默认0]")
    private Integer userType;

    @Schema(description = "微信用户的open_id")
    private String openId;

    /**
     * 用户体系 1-售后体系 2-内训体系
     */
    @Schema(description = "用户体系 1-售后体系 2-内训体系")
    private Integer systemType;

    /**
     * 是否已注册人脸识别。0未注册 1已注册
     */
    @Schema(description = "是否已注册人脸识别。0未注册 1已注册")
    private Integer isAddFace;

}
