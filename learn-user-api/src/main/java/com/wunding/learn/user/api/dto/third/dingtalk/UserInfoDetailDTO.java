package com.wunding.learn.user.api.dto.third.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 查询用户详情响应对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2023-8-8
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoDetailDTO {

    /**
     * 返回码
     */
    @JsonProperty(value = "errcode")
    private Integer errCode;

    /**
     * 对返回码的文本描述内容
     */
    @JsonProperty(value = "errmsg")
    private String errMsg;

    /**
     * 返回结果。
     */
    @JsonProperty(value = "result")
    private DepartmentUserDTO.UserInfo userInfo;

}
