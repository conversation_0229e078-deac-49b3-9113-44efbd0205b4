package com.wunding.learn.user.api.service;

import com.wunding.learn.user.api.dto.sync.SyncOrgDTO;
import com.wunding.learn.user.api.dto.sync.SyncPostDTO;
import com.wunding.learn.user.api.dto.sync.SyncUserDTO;
import feign.Request;
import java.util.Collection;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${learn.service.learn-user-service}", name = "learn-user-service", path = "/user")
public interface SyncDataFeign {

    /**
     * 同步职级
     * @param jobLevelNameSet 职级名称集合
     */
    @PostMapping("/sync/syncJobLevel")
    void syncJobLevel(@RequestBody Collection<String> jobLevelNameSet, Request.Options options);

    /**
     * 同步组织数据
     * @param syncOrgList 组织数据
     * @param type 同步类型
     */
    @PostMapping("/sync/syncOrgData")
    void syncOrgData(@RequestBody List<SyncOrgDTO> syncOrgList, @RequestParam("type") Integer type, Request.Options options);

    /**
     * 同步身份数据
     * @param syncPostList 身份数据
     * @param type 同步类型
     */
    @PostMapping("/sync/syncIdentityData")
    void syncIdentityData(@RequestBody List<SyncPostDTO> syncPostList, @RequestParam("type") Integer type,Request.Options options);

    /**
     * 同步用户数据
     * @param syncUserList 用户数据
     * @param type 同步类型
     */
    @PostMapping("/sync/syncUserData")
    void syncUserData(@RequestBody List<SyncUserDTO> syncUserList, @RequestParam("type") Integer type,Request.Options options);

}
