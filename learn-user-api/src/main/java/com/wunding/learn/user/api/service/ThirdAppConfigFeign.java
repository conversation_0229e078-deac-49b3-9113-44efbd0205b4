package com.wunding.learn.user.api.service;

import com.wunding.learn.user.api.dto.ThirdAppConfigDTO;
import com.wunding.learn.user.api.dto.third.CropInfoDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.Mapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${learn.service.learn-user-service}", name = "learn-user-service", path = "/user")
public interface ThirdAppConfigFeign {


    @PostMapping("/thirdAppConfig/saveCustom")
    void saveCustom(@RequestBody ThirdAppConfigDTO thirdAppConfig);


    @PostMapping("/thirdAppConfig/updateCustom")
    void updateCustom(@RequestBody ThirdAppConfigDTO thirdAppConfig);

    @GetMapping("/thirdAppConfig/list")
    List<ThirdAppConfigDTO> list();

    @PostMapping("/thirdAppConfig/deleteById")
    void deleteById(@RequestParam("id") Long id);


}
