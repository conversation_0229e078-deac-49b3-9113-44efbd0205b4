package com.wunding.learn.user.api.dto.sync;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 企微岗位表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2023-05-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "PostDTO", description = "通用岗位表")
public class PostDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 岗位id
     */
    @Schema(description = "岗位id")
    private String id;


    /**
     * 岗位名称
     */
    @Schema(description = "岗位名称")
    private String name;

    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    private Date createTime;


    /**
     * 新增人员
     */
    @Schema(description = "新增人员")
    private String createBy;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    private Date updateTime;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDel;

    /**
     * 企业微信类型（支持配置多个企业微信）：0-澳美点击 1-奥美领航
     */

    @Schema(description = "企业微信类型（支持配置多个企业微信）：0-澳美点击 1-奥美领航")
    private Integer multiChannelType;

}
