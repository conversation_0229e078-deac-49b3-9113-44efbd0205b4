package com.wunding.learn.user.api.service;

import com.wunding.learn.user.api.dto.AiBaseConfigDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${learn.service.learn-user-service}", name = "learn-user-service", path = "/user")
public interface AiBaseConfigFeign {

    @GetMapping("/ai/getAiConfig")
    AiBaseConfigDTO getAiConfig();
}
