package com.wunding.learn.user.api.dto.third.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 钉钉获取用户通讯录个人信息新版SDK响应对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-08-03
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonUserInfoDTO {

    /**
     * 请求编号
     */
    @JsonProperty(value = "requestid")
    private String requestId;

    /**
     * 返回码
     */
    @JsonProperty(value = "code")
    private String code;

    /**
     * 返回码提示语
     */
    @JsonProperty(value = "message")
    private String message;

    /**
     * 用户的钉钉昵称。
     */
    @JsonProperty(value = "nick")
    private String nick;

    /**
     * 头像URL。
     */
    @JsonProperty(value = "avatarUrl")
    private String avatarUrl;

    /**
     * 用户的手机号。
     */
    @JsonProperty(value = "mobile")
    private String mobile;

    /**
     * 用户的openId。
     */
    @JsonProperty(value = "openId")
    private String openId;

    /**
     * 用户的unionId。
     */
    @JsonProperty(value = "unionId")
    private String unionId;

    /**
     * 用户的个人邮箱。
     */
    @JsonProperty(value = "email")
    private String email;

    /**
     * 手机号对应的国家号。
     */
    @JsonProperty(value = "stateCode")
    private String stateCode;

}
