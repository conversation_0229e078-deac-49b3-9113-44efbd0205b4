package com.wunding.learn.user.api.dto.third.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 钉钉获取部门用户详情旧版SDK响应对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-08-04
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentUserDTO {

    /**
     * 返回码
     */
    @JsonProperty(value = "errcode")
    private Integer errCode;

    /**
     * 对返回码的文本描述内容
     */
    @JsonProperty(value = "errmsg")
    private String errMsg;

    /**
     * 返回结果。
     */
    @JsonProperty(value = "result")
    private Result result;

    /**
     * 返回结果。
     */
    @Data
    @Accessors(chain = true)
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {

        /**
         * 是否还有更多的数据：true：有 false：没有
         */
        @JsonProperty(value = "has_more")
        private Boolean hasMore;

        /**
         * 下一次分页的游标。如果has_more为false，表示没有更多的分页数据。
         */
        @JsonProperty(value = "next_cursor")
        private Integer nextCursor;

        /**
         * 用户信息列表。
         */
        @JsonProperty(value = "list")
        private List<UserInfo> list;

    }

    /**
     * 用户信息列表。
     */
    @Data
    @Accessors(chain = true)
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {

        /**
         * 用户的userId。
         */
        @JsonProperty(value = "userid")
        private String userId;

        /**
         * 用户在当前开发者企业帐号范围内的唯一标识。
         */
        @JsonProperty(value = "unionid")
        private String unionId;

        /**
         * 用户姓名。
         */
        @JsonProperty(value = "name")
        private String name;

        /**
         * 头像地址。
         */
        @JsonProperty(value = "avatar")
        private String avatar;

        /**
         * 国际电话区号。
         */
        @JsonProperty(value = "state_code")
        private String stateCode;

        /**
         * 手机号码。
         */
        @JsonProperty(value = "mobile")
        private String mobile;

        /**
         * 是否号码隐藏：
         * <p>
         * true：隐藏 隐藏手机号后，手机号在个人资料页隐藏，但仍可对其发DING、发起钉钉免费商务电话。
         * <p>
         * false：不隐藏
         */
        @JsonProperty(value = "hide_mobile")
        private Boolean hideMobile;

        /**
         * 分机号。
         */
        @JsonProperty(value = "telephone")
        private String telephone;

        /**
         * 员工账号。
         */
        @JsonProperty(value = "job_number")
        private String jobNumber;

        /**
         * 职位。
         */
        @JsonProperty(value = "title")
        private String title;

        /**
         * 员工邮箱。
         */
        @JsonProperty(value = "email")
        private String email;

        /**
         * 员工的企业邮箱。
         */
        @JsonProperty(value = "org_email")
        private String orgEmail;

        /**
         * 办公地点。
         */
        @JsonProperty(value = "work_place")
        private String workPlace;

        /**
         * 备注。
         */
        @JsonProperty(value = "remark")
        private String remark;

        /**
         * 所属部门id列表。
         */
        @JsonProperty(value = "dept_id_list")
        private Integer[] deptIdList;

        /**
         * 员工在对应的部门中的排序。
         */
        @JsonProperty(value = "dept_order_list")
        private List<DeptOrder> deptOrderList;

        /**
         * 员工在部门中的排序。
         */
        @JsonProperty(value = "dept_order")
        private Long deptOrder;

        /**
         * 扩展属性。
         */
        @JsonProperty(value = "extension")
        private String extension;

        /**
         * 入职时间，Unix时间戳，单位毫秒。
         */
        @JsonProperty(value = "hired_date")
        private Long hiredDate;

        /**
         * 是否激活了钉钉：true：已激活 false：未激活
         */
        @JsonProperty(value = "active")
        private Boolean active;

        /**
         * 是否为企业的管理员：true：是 false：不是
         */
        @JsonProperty(value = "admin")
        private Boolean admin;

        /**
         * 是否为企业的老板：true：是 false：不是
         */
        @JsonProperty(value = "boss")
        private Boolean boss;

        /**
         * 是否是部门的主管：true：是 false：不是
         */
        @JsonProperty(value = "leader")
        private Boolean leader;

        /**
         * 是否专属帐号：true：是 false：不是
         */
        @JsonProperty(value = "exclusive_account")
        private Boolean exclusiveAccount;

    }

    /**
     * 员工在对应的部门中的排序。
     */
    @Data
    @Accessors(chain = true)
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeptOrder {

        /**
         * 部门id。
         */
        @JsonProperty(value = "dept_id")
        private Integer deptId;

        /**
         * 员工在部门中的排序。
         */
        @JsonProperty(value = "order")
        private Long order;

    }

}
