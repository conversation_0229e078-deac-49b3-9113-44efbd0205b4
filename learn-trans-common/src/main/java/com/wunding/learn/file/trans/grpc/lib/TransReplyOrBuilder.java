// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: trans.proto

// Protobuf Java Version: 3.25.3
package com.wunding.learn.file.trans.grpc.lib;

public interface TransReplyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:TransReply)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   **
   * 转码状态
   * </pre>
   *
   * <code>int32 status = 1;</code>
   * @return The status.
   */
  int getStatus();

  /**
   * <pre>
   **
   * 转码日志
   * </pre>
   *
   * <code>string log = 2;</code>
   * @return The log.
   */
  java.lang.String getLog();
  /**
   * <pre>
   **
   * 转码日志
   * </pre>
   *
   * <code>string log = 2;</code>
   * @return The bytes for log.
   */
  com.google.protobuf.ByteString
      getLogBytes();

  /**
   * <pre>
   **
   * 消息类型
   * </pre>
   *
   * <code>string type = 3;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <pre>
   **
   * 消息类型
   * </pre>
   *
   * <code>string type = 3;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <pre>
   **
   * 转码记录id
   * </pre>
   *
   * <code>string transId = 4;</code>
   * @return The transId.
   */
  java.lang.String getTransId();
  /**
   * <pre>
   **
   * 转码记录id
   * </pre>
   *
   * <code>string transId = 4;</code>
   * @return The bytes for transId.
   */
  com.google.protobuf.ByteString
      getTransIdBytes();

  /**
   * <pre>
   **
   * 输出文件路径
   * </pre>
   *
   * <code>string filePath = 5;</code>
   * @return The filePath.
   */
  java.lang.String getFilePath();
  /**
   * <pre>
   **
   * 输出文件路径
   * </pre>
   *
   * <code>string filePath = 5;</code>
   * @return The bytes for filePath.
   */
  com.google.protobuf.ByteString
      getFilePathBytes();

  /**
   * <pre>
   **
   * 输出文件路径
   * </pre>
   *
   * <code>string zipFilePath = 6;</code>
   * @return The zipFilePath.
   */
  java.lang.String getZipFilePath();
  /**
   * <pre>
   **
   * 输出文件路径
   * </pre>
   *
   * <code>string zipFilePath = 6;</code>
   * @return The bytes for zipFilePath.
   */
  com.google.protobuf.ByteString
      getZipFilePathBytes();

  /**
   * <pre>
   **
   * 视频清晰度
   * </pre>
   *
   * <code>repeated .VideoClarity videoClarity = 7;</code>
   */
  java.util.List<com.wunding.learn.file.trans.grpc.lib.VideoClarity> 
      getVideoClarityList();
  /**
   * <pre>
   **
   * 视频清晰度
   * </pre>
   *
   * <code>repeated .VideoClarity videoClarity = 7;</code>
   */
  com.wunding.learn.file.trans.grpc.lib.VideoClarity getVideoClarity(int index);
  /**
   * <pre>
   **
   * 视频清晰度
   * </pre>
   *
   * <code>repeated .VideoClarity videoClarity = 7;</code>
   */
  int getVideoClarityCount();
  /**
   * <pre>
   **
   * 视频清晰度
   * </pre>
   *
   * <code>repeated .VideoClarity videoClarity = 7;</code>
   */
  java.util.List<? extends com.wunding.learn.file.trans.grpc.lib.VideoClarityOrBuilder> 
      getVideoClarityOrBuilderList();
  /**
   * <pre>
   **
   * 视频清晰度
   * </pre>
   *
   * <code>repeated .VideoClarity videoClarity = 7;</code>
   */
  com.wunding.learn.file.trans.grpc.lib.VideoClarityOrBuilder getVideoClarityOrBuilder(
      int index);

  /**
   * <pre>
   **
   * 提取的文本文件路径
   * </pre>
   *
   * <code>string extractTextPath = 8;</code>
   * @return The extractTextPath.
   */
  java.lang.String getExtractTextPath();
  /**
   * <pre>
   **
   * 提取的文本文件路径
   * </pre>
   *
   * <code>string extractTextPath = 8;</code>
   * @return The bytes for extractTextPath.
   */
  com.google.protobuf.ByteString
      getExtractTextPathBytes();

  /**
   * <pre>
   **
   * 视频提取的音频文件
   * </pre>
   *
   * <code>string mp3FilePath = 9;</code>
   * @return The mp3FilePath.
   */
  java.lang.String getMp3FilePath();
  /**
   * <pre>
   **
   * 视频提取的音频文件
   * </pre>
   *
   * <code>string mp3FilePath = 9;</code>
   * @return The bytes for mp3FilePath.
   */
  com.google.protobuf.ByteString
      getMp3FilePathBytes();

  /**
   * <pre>
   **
   * 文件大小
   * </pre>
   *
   * <code>int32 fileSize = 10;</code>
   * @return The fileSize.
   */
  int getFileSize();
}
