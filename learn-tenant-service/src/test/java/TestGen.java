//import com.wunding.learn.common.util.json.JsonUtil;
//import com.wunding.learn.tenant.service.admin.dto.TenantConfigDTO;
//import com.wunding.learn.tenant.service.admin.dto.TenantDeploymentDataBaseDTO;
//import com.wunding.learn.tenant.service.admin.dto.TenantModuleDTO;
//import com.wunding.learn.tenant.service.enums.ModuleEnum;
//import com.wunding.learn.tenant.service.model.Tenant;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Random;
//import java.util.concurrent.ThreadLocalRandom;
//import org.apache.commons.lang3.EnumUtils;
//
//public class TestGen {
//
//    private static final String version = "V7.0.5";
//
//
//    public static void main(String[] args) {
////        long currentTimeMillis = System.currentTimeMillis();
////        Date createTime = new Date(currentTimeMillis);
////        Date updateTime = new Date(currentTimeMillis + 60 * 30 * 1000);
////        LocalDateTime createDateTime = LocalDateTime.ofInstant(createTime.toInstant(), ZoneId.systemDefault());
////        LocalDateTime updateDateTime = LocalDateTime.ofInstant(updateTime.toInstant(), ZoneId.systemDefault());
////        int compareValue = createDateTime.plusMinutes(30).compareTo(updateDateTime);
////        System.out.println("create_time :" + createTime.getTime());
////        System.out.println("create_time :" + updateTime.getTime());
////
////        System.out.println(compareValue);
//        Tenant tenant = new Tenant().setColor("#1212121").setCustomCode("95278848")
//                .setApplicationPlatformName("WUNDING").setLoginName("admin").setPassword("password");
//        String tenantId = tenant.getId();
//
//        List<TenantModuleDTO> allModules = new ArrayList<>();
//        EnumUtils.getEnumList(ModuleEnum.class).forEach(entity -> {
//            TenantModuleDTO tenantModuleDTO = new TenantModuleDTO();
//            tenantModuleDTO.setModuleCode(entity.getCode());
//            tenantModuleDTO.setModuleName(entity.getName());
//            allModules.add(tenantModuleDTO);
//        });
//
//        Map<String, TenantDeploymentDataBaseDTO.ServiceDbConfig> modules = new HashMap<>();
//        TenantDeploymentDataBaseDTO tenantDeploymentDataBaseDTO = new TenantDeploymentDataBaseDTO();
//
//        allModules.forEach(module -> {
//            TenantDeploymentDataBaseDTO.ServiceDbConfig moduleDTO = new TenantDeploymentDataBaseDTO.ServiceDbConfig();
//            String host = "mysql";
//            // 数据库名称 = 模块名 + （唯一）组织编码
//            String databaseName = module.getModuleCode() + "_" + tenant.getCustomCode();
//            String password = generateRandomPassword(8);
//
//            moduleDTO.setHost(host)
//                    .setUser(databaseName)
//                    .setPassword(password)
//                    .setName(databaseName)
//                    .setPort("3306");
//
//            modules.put(module.getModuleCode(), moduleDTO);
//
//        });
//
//        TenantConfigDTO configs = new TenantConfigDTO().setTenantId(tenantId).setDownloadApp(1)
//                .setPrivacyPolicy(1).setId("6666666666");
//
//        tenantDeploymentDataBaseDTO.setDb(new TenantDeploymentDataBaseDTO.DeploymentModuleDbDTO().setVersion(version).setServices(modules));
//        // 组装租户镜像数据
//        tenantDeploymentDataBaseDTO.setSlug(tenant.getCustomCode())
//                .setName(tenant.getApplicationPlatformName())
//                .setAdmin(tenant.getLoginName())
//                .setPassword(tenant.getPassword())
//                .setLogo("66666666666")
//                .setColor(tenant.getColor())
//                // FIXME 这里焦点颜色页面没有设置的地方先给背景色一样的
//                .setActiveColor(tenant.getColor())
//                .setPrivacyPolicy(configs.getPrivacyPolicy())
//                .setDownloadApp(configs.getDownloadApp())
//                .setLanguage(configs.getLanguage());
//
//        String json = JsonUtil.objToJson(tenantDeploymentDataBaseDTO);
//        System.out.println(json);
//    }
//
//    //随机生成指定长度的 密码
//    public static String generateRandomPassword(int len) {
//        char charr[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@#$%^&*.?".toCharArray();
//        StringBuilder sb = new StringBuilder();
//        Random r = ThreadLocalRandom.current();
//        for (int x = 0; x < len; ++x) {
//            sb.append(charr[r.nextInt(charr.length)]);
//        }
//        return sb.toString();
//    }
//}
