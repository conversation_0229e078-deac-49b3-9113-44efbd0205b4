package com.wunding.learn.tenant.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 权限路由模板列表对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "TenantPermissionRouterTemplateListDTO", description = "权限路由模板列表对象")
public class TenantPermissionRouterTemplateListDTO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 上级id
     */
    @Schema(description = "父键id")
    private String parentId;

    /**
     * 上级名称
     */
    @Schema(description = "父路由名称")
    private String parentName;

    /**
     * 路由ID
     */
    @Schema(description = "路由ID")
    private String routerId;

    /**
     * 名称
     */
    @Schema(description = "路由名称")
    private String name;

    /**
     * 标题
     */
    @Schema(description = "路由标题")
    private String title;

    /**
     * 顺序
     */
    @Schema(description = "顺序")
    private Integer sortNo;

    /**
     * 路径
     */
    @Schema(description = "路径")
    private String path;

    /**
     * 组件
     */
    @Schema(description = "组件")
    private String component;

    /**
     * 类型
     */
    @Schema(description = "type1 路由 2按钮")
    private Integer type;
} 