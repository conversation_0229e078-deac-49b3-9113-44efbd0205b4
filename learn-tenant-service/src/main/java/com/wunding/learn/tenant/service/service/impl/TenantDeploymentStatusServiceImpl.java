package com.wunding.learn.tenant.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.tenant.service.enums.DeploymentStatusEnum;
import com.wunding.learn.tenant.service.mapper.TenantDeploymentStatusMapper;
import com.wunding.learn.tenant.service.model.TenantDeploymentStatus;
import com.wunding.learn.tenant.service.service.ITenantDeploymentStatusService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 租户部署状态表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">YangLeQun</a>
 * @since 2023-02-22
 */
@Slf4j
@Service("tenantDeploymentStatusService")
public class TenantDeploymentStatusServiceImpl extends
    ServiceImpl<TenantDeploymentStatusMapper, TenantDeploymentStatus> implements ITenantDeploymentStatusService {

    @Override
    public List<TenantDeploymentStatus> getList() {
        LambdaQueryWrapper<TenantDeploymentStatus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantDeploymentStatus::getStatus, DeploymentStatusEnum.DEPLOYING.getCode());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public TenantDeploymentStatus getDeploymentStatus(String tenantId) {
        LambdaQueryWrapper<TenantDeploymentStatus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantDeploymentStatus::getTenantId, tenantId);
        return baseMapper.selectOne(queryWrapper);
    }
}
