package com.wunding.learn.tenant.service.service.impl;


import static com.wunding.learn.common.util.excel.ExcelUtil.exportExcel1;
import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.houbb.heaven.util.util.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Maps;
import com.wunding.learn.common.bean.DataBaseInfo;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.dto.TenantBucketMsg;
import com.wunding.learn.common.mq.event.LogoJsonFileConfigEvent;
import com.wunding.learn.common.mq.event.TenantDownloadCoursewareEvent;
import com.wunding.learn.common.mq.event.TenantNoticeEvent;
import com.wunding.learn.common.mq.event.tenant.SyncTenantAllRoleRouterEvent;
import com.wunding.learn.common.mq.event.tenant.TenantDataSourceRemoveEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.pinyin.PinYinUtils;
import com.wunding.learn.common.util.signature.MD5Util;
import com.wunding.learn.tenant.api.dto.PermanentCodeAuthCorpInfoDTO;
import com.wunding.learn.tenant.service.admin.dto.AvailableDTO;
import com.wunding.learn.tenant.service.admin.dto.DonatedCourseDTO;
import com.wunding.learn.tenant.service.admin.dto.NamePath;
import com.wunding.learn.tenant.service.admin.dto.ResponseContentDTO;
import com.wunding.learn.tenant.service.admin.dto.SaveTenantDTO;
import com.wunding.learn.tenant.service.admin.dto.SyncCourseDTO;
import com.wunding.learn.tenant.service.admin.dto.TenantBucketInfo;
import com.wunding.learn.tenant.service.admin.dto.TenantConfigDTO;
import com.wunding.learn.tenant.service.admin.dto.TenantDTO;
import com.wunding.learn.tenant.service.admin.dto.TenantDeploymentDataBaseDTO;
import com.wunding.learn.tenant.service.admin.dto.TenantDeploymentDataBaseDTO.DeploymentModuleDbDTO;
import com.wunding.learn.tenant.service.admin.dto.TenantDeploymentDataBaseDTO.ServiceDbConfig;
import com.wunding.learn.tenant.service.admin.dto.TenantDomainDTO;
import com.wunding.learn.tenant.service.admin.dto.TenantListDTO;
import com.wunding.learn.tenant.service.admin.dto.TenantModuleDTO;
import com.wunding.learn.tenant.service.admin.dto.UploadResultDTO;
import com.wunding.learn.tenant.service.admin.query.CheckRepeatQueryDTO;
import com.wunding.learn.tenant.service.admin.query.DonatedCourseListQueryDTO;
import com.wunding.learn.tenant.service.admin.query.TenantListQueryDTO;
import com.wunding.learn.tenant.service.bean.UploadFileRequest;
import com.wunding.learn.tenant.service.bean.UploadFileResponse;
import com.wunding.learn.tenant.service.config.OperationUrlProperties;
import com.wunding.learn.tenant.service.config.WeComConfig;
import com.wunding.learn.tenant.service.enums.DeploymentStatusEnum;
import com.wunding.learn.tenant.service.enums.FileTypeEnum;
import com.wunding.learn.tenant.service.enums.ImageTypeEnum;
import com.wunding.learn.tenant.service.enums.ModuleEnum;
import com.wunding.learn.tenant.service.enums.Oauth2EntryTypeEnum;
import com.wunding.learn.tenant.service.enums.RedisKeyEnum;
import com.wunding.learn.tenant.service.enums.TenantErrorNoEnum;
import com.wunding.learn.tenant.service.feign.TenantFeignImpl;
import com.wunding.learn.tenant.service.mapper.TenantMapper;
import com.wunding.learn.tenant.service.model.Images;
import com.wunding.learn.tenant.service.model.Tenant;
import com.wunding.learn.tenant.service.model.TenantBucket;
import com.wunding.learn.tenant.service.model.TenantBusinessVersion;
import com.wunding.learn.tenant.service.model.TenantBusinessVersionRecord;
import com.wunding.learn.tenant.service.model.TenantConfig;
import com.wunding.learn.tenant.service.model.TenantDbInstance;
import com.wunding.learn.tenant.service.model.TenantDeploymentStatus;
import com.wunding.learn.tenant.service.model.TenantModule;
import com.wunding.learn.tenant.service.model.TenantThirdApp;
import com.wunding.learn.tenant.service.model.TenantVersion;
import com.wunding.learn.tenant.service.mq.event.DeployLogEvent;
import com.wunding.learn.tenant.service.mq.event.TenantBucketEvent;
import com.wunding.learn.tenant.service.mq.event.TenantDataSourceEvent;
import com.wunding.learn.tenant.service.service.IImagesService;
import com.wunding.learn.tenant.service.service.ITenantBucketService;
import com.wunding.learn.tenant.service.service.ITenantBusinessVersionRecordService;
import com.wunding.learn.tenant.service.service.ITenantBusinessVersionService;
import com.wunding.learn.tenant.service.service.ITenantConfigService;
import com.wunding.learn.tenant.service.service.ITenantDbInstanceService;
import com.wunding.learn.tenant.service.service.ITenantDeploymentStatusService;
import com.wunding.learn.tenant.service.service.ITenantDonateCourseService;
import com.wunding.learn.tenant.service.service.ITenantModuleService;
import com.wunding.learn.tenant.service.service.ITenantService;
import com.wunding.learn.tenant.service.service.ITenantThirdAppService;
import com.wunding.learn.tenant.service.service.ITenantVersionService;
import com.wunding.learn.tenant.service.util.FileUtil;
import com.wunding.learn.tenant.service.util.HttpUtil;
import com.wunding.learn.tenant.service.util.HttpUtil.ResponseDTO;
import com.wunding.learn.tenant.service.util.ImageUtil;
import com.wunding.learn.tenant.service.util.UrlHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p> 系统租户表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-02-07
 */
@Slf4j
@Service("tenantService")
public class TenantServiceImpl extends ServiceImpl<TenantMapper, Tenant> implements ITenantService, InitializingBean {

    @Resource
    private ITenantConfigService tenantConfigService;
    @Resource
    private ITenantModuleService tenantModuleService;
    @Resource
    private ITenantDbInstanceService tenantDbInstanceService;
    @Resource
    private ITenantDeploymentStatusService tenantDeploymentStatusService;
    @Resource
    private IImagesService imagesService;
    @Resource
    private UrlHelper urlHelper;
    @Resource
    private ITenantBucketService tenantBucketService;
    @Resource
    private MqProducer rabbitMqProducer;
    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;
    @Resource(name = "cacheRedisTemplate")
    private RedisTemplate<String, Object> cacheRedisTemplate;
    @Resource
    private ImageUtil imageUtil;
    @Resource
    private ITenantVersionService tenantVersionService;
    @Resource
    private OperationUrlProperties operationUrlProperties;
    @Resource
    private TenantFeignImpl tenantFeign;
    @Resource
    private WeComConfig weComConfig;

    @Resource
    private ITenantService tenantService;
    @Resource
    private ITenantThirdAppService tenantThirdAppService;

    @Resource
    private ITenantBusinessVersionService tenantBusinessVersionService;

    @Resource
    private ITenantBusinessVersionRecordService tenantBusinessVersionRecordService;

    @Resource
    private ITenantDonateCourseService donateCourseService;

    @Value("${tenant.type}")
    private int tenantType;
    @Value("${operation.domain:saas.wdxuexi.com}")
    private String tenantDomain;

    @Value("${operation.defaultDbHost}")
    private String defaultDbHost;

    // object-store constant
    private static final String UPGRADE_DEPLOY_RESPONSE_CONTENT = "upgradeDeploy responseContent:[{}]";
    /**
     * 限制结果数
     */
    public static final String LAST_SQL = "limit 1";

    // datasource constant
    private static final String DB_LOOK_UP_KEY_PREFIX = "DS_";
    private static final String JDBC_URL_FORMAT = "jdbc:mysql://{0}:{1}/{2}?allowMultiQueries=true&serverTimezone=Asia/Shanghai";
    /**
     * redirect uri format
     */
    private static final String REDIRECT_URI_FORMAT =
        "https://{0}/clientcn/login?" + "entrytype={1}" + "&appid={2}" + "&agentid={3}" + "{4}"
            + "&wechattype={5}" + "&code={6}";

    /**
     * redirect uri format
     */
    private static final String THIRD_ACCESS_MODEL_REDIRECT_URI_FORMAT =
        "https://{0}/tenadmin/transit?corpid={1}" + "&entrytype={2}" + "&appid={3}" + "&agentid={4}"
            + "&wechattype={5}" + "&{6}";

    /**
     * 企业微信/微信公众号推送URL规则
     */
    private static final String WECHAT_URL_FORMAT = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={0}&redirect_uri={1}&response_type=code&scope=snsapi_base&agentid={2}";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateTenant(SaveTenantDTO saveTenantDTO) {
        // 不能创建common租户
        checkCommonTenant(saveTenantDTO);
        // Warn 修改这个方法的逻辑记得同步修改方法 saveFromWxCom 的逻辑
        log.info("---saveOrUpdateTenant--传入参数：" + saveTenantDTO.toString());
        String curUserId = UserThreadContext.getUserId();
        Date now = new Date();
        String tenantId = saveTenantDTO.getId();

        boolean isUpdateBusinessVersion = false;

        // 判断是新增还是更新操作
        boolean isUpdate = StringUtils.isNotBlank(tenantId);
        Long oldBusinessVersionId = null;

        Tenant tenant = new Tenant();
        String oldPassword = null;
        if (isUpdate) {
            Tenant oldTenant = baseMapper.selectById(tenantId);
            if (oldTenant == null) {
                throw new BusinessException(TenantErrorNoEnum.TENANT_NOT_EXISTS);
            }
            if (!Objects.equals(oldTenant.getLoginName(), saveTenantDTO.getLoginName())) {
                throw new BusinessException(TenantErrorNoEnum.ACCOUNT_CANNOT_MODIFY);
            }
            oldPassword = oldTenant.getPassword();
            if (!Objects.equals(saveTenantDTO.getBusinessVersionId(), oldTenant.getBusinessVersionId())) {
                isUpdateBusinessVersion = true;
                oldBusinessVersionId = oldTenant.getBusinessVersionId();
            }
        }
        BeanUtils.copyProperties(saveTenantDTO, tenant);
        tenant.setTelephone(StringUtils.trim(saveTenantDTO.getTelephone()));
        tenant.setEmail(StringUtils.trim(saveTenantDTO.getEmail()));
        tenant.setUpdateBy(curUserId);
        // 域名不做更新
        tenant.setCompanyDomainName(null);
        tenant.setUpdateTime(now);
        List<String> list = tenantModuleService.findAllModules().stream().map(TenantModuleDTO::getModuleCode).collect(
            Collectors.toList());
        if (!isUpdate) {
            // 参数校验
            TenantConfigDTO config = checkParameter(saveTenantDTO);
            // 插入租户逻辑
            saveTenant(saveTenantDTO, curUserId, now, tenant, list, config);
            isUpdateBusinessVersion = true;
        } else {
            // 更新租户逻辑
            updateTenant(saveTenantDTO, tenant, oldPassword);
            rabbitMqProducer.sendMsg(
                new LogoJsonFileConfigEvent(saveTenantDTO.getDownloadApp(), saveTenantDTO.getDownloadCourseware(),
                    saveTenantDTO.getSmallAppSignin(),
                    saveTenantDTO.getCustomCode()));
            log.info("saveTenantDTO:{}", saveTenantDTO);
        }

        //生成版本修改记录，生成版本对应的sql
        if (isUpdateBusinessVersion) {
            if (oldBusinessVersionId != null) {
                List<Tenant> tenantList = getByVersionIds(oldBusinessVersionId);
                if (CollectionUtil.isEmpty(tenantList)) {
                    tenantBusinessVersionService.setEdit(oldBusinessVersionId, 1);
                }
            }
            tenantBusinessVersionService.setEdit(tenant.getBusinessVersionId(), 0);
            tenantBusinessVersionService.createBusinessVersionDml(tenant.getBusinessVersionId(), tenant);
        }

        // 课件下载路由权限
        rabbitMqProducer.sendMsg(
            new TenantDownloadCoursewareEvent(saveTenantDTO.getDownloadCourseware(), saveTenantDTO.getCustomCode()));
    }

    private static void checkCommonTenant(SaveTenantDTO saveTenantDTO) {
        if ("common".equals(saveTenantDTO.getCustomCode())) {
            throw new BusinessException(TenantErrorNoEnum.ERR_ADD_COMMON_TENANT);
        }
    }

    @Override
    @SuppressWarnings("java:S4790")
    public Tenant saveFromWxCom(PermanentCodeAuthCorpInfoDTO authCorpInfoDTO, Boolean isAgency) {
        // 使用名称作为租户id
        String customCode = StringUtils.toRootLowerCase(PinYinUtils.getFirstLetter(authCorpInfoDTO.getCorpName()));
        if (StringUtils.isEmpty(customCode)) {
            customCode = DigestUtils.md5Hex(authCorpInfoDTO.getCorpId().getBytes()).substring(8, 24).toLowerCase();
        } else {
            // 防止过短截取8位
            customCode =
                customCode + DigestUtils.md5Hex(authCorpInfoDTO.getCorpId().getBytes()).substring(8, 16).toLowerCase();
            if (customCode.length() > 20) {
                // 截取17防止数据库userName超长
                customCode = customCode.substring(0, 17);
            }
        }
        // 租户是否存在
        TenantThirdApp tenantIdAndAppType = tenantThirdAppService.getByCorpIdAndAppType(authCorpInfoDTO.getCorpId(),
            Oauth2EntryTypeEnum.WECOM.getType());
        // 参照 saveOrUpdateTenant  完成租户基本初始化
        SaveTenantDTO saveTenantDTO = corpInfoTransTenantDTO(authCorpInfoDTO, new SaveTenantDTO());
        saveTenantDTO.setCustomCode(customCode);
        saveTenantDTO.setCompanyDomainName(customCode + "." + tenantDomain.replace("console.", ""));
        LambdaQueryWrapper<Tenant> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(tenantIdAndAppType)) {
            queryWrapper.eq(Tenant::getId, tenantIdAndAppType.getTenantId());
        } else {
            queryWrapper.eq(Tenant::getCustomCode, customCode);
        }
        queryWrapper.eq(Tenant::getIsDel, 0).last(LAST_SQL);
        Tenant dbTenant = baseMapper.selectOne(queryWrapper);
        log.info("---saveFromWxCom--传入参数：dbTenant{}， {}", dbTenant, authCorpInfoDTO.getCorpId());
        if (Objects.nonNull(dbTenant)) {
            if (Objects.equals(dbTenant.getIsAvailable(), AvailableEnum.NOT_AVAILABLE.getValue())) {
                dbTenant.setIsAvailable(AvailableEnum.AVAILABLE.getValue());
                dbTenant.setCustomCode(customCode);
                dbTenant.setCompanyDomainName(saveTenantDTO.getCompanyDomainName());
                tenantBucketService.saveBucketInfo(saveTenantDTO);
                baseMapper.updateById(dbTenant);
            }
            return dbTenant;
        }

        //创建租户
        log.info("---saveOrUpdateTenant--传入参数：{}", saveTenantDTO);
        String curUserId = UserThreadContext.getUserId();
        Date now = new Date();
        Tenant tenant = new Tenant();
        BeanUtils.copyProperties(saveTenantDTO, tenant);
        tenant.setTelephone(StringUtils.trim(saveTenantDTO.getTelephone()));
        tenant.setEmail(StringUtils.trim(saveTenantDTO.getEmail()));
        tenant.setUpdateBy(curUserId);
        tenant.setUpdateTime(now);
        List<String> list = tenantModuleService.findAllModules().stream().map(TenantModuleDTO::getModuleCode).collect(
            Collectors.toList());
        // 参数校验
        TenantConfigDTO config = checkParameter(saveTenantDTO);
        // 插入租户逻辑
        saveTenant(saveTenantDTO, curUserId, now, tenant, list, config, isAgency);
        //自动创建数据库
        tenantService.tenantDetailDateTransform(tenant.getId(), defaultDbHost);
        //生成版本修改记录，生成版本对应的sql
        tenantBusinessVersionService.setEdit(tenant.getBusinessVersionId(), 0);
        tenantBusinessVersionService.createBusinessVersionDml(tenant.getBusinessVersionId(), tenant);
        // 课件下载路由权限
        rabbitMqProducer.sendMsg(
            new TenantDownloadCoursewareEvent(saveTenantDTO.getDownloadCourseware(), saveTenantDTO.getCustomCode()));
        return tenant;
    }


    private SaveTenantDTO corpInfoTransTenantDTO(PermanentCodeAuthCorpInfoDTO codeAuthCorpInfoDTO,
        SaveTenantDTO saveTenantDTO) {
        saveTenantDTO.setId(newId());
        saveTenantDTO.setLoginName("admin");
        saveTenantDTO.setPassword("WunDing1996*");
        saveTenantDTO.setCompanyName(codeAuthCorpInfoDTO.getCorpName());
        //开始租期
        Calendar calendar = Calendar.getInstance();
        saveTenantDTO.setTenantStartTime(calendar.getTime());
        //结束租期
        calendar.add(Calendar.DAY_OF_MONTH, 15);
        saveTenantDTO.setTenantEndTime(calendar.getTime());
        saveTenantDTO.setTelephone("");
        saveTenantDTO.setEmail("");
        saveTenantDTO.setCompanyAddress("");
        saveTenantDTO.setDownloadApp(0);
        saveTenantDTO.setDownloadCourseware(0);
        saveTenantDTO.setSmallAppSignin(0);
        // 旗舰版业务版本
        saveTenantDTO.setBusinessVersionId(1L);
        return saveTenantDTO;
    }

    /**
     * 检查参数
     *
     * @param saveTenantDTO 保存租户数据
     * @return {@link TenantConfigDTO}
     */
    private TenantConfigDTO checkParameter(SaveTenantDTO saveTenantDTO) {
        if (StringUtils.isBlank(saveTenantDTO.getPassword())) {
            throw new BusinessException(TenantErrorNoEnum.PLEASE_INPUT_PASSWORD);
        }
        Integer downloadApp = saveTenantDTO.getDownloadApp();
        if (Objects.isNull(downloadApp)) {
            throw new BusinessException(TenantErrorNoEnum.PLEASE_SET_DOWNLOAD_APP);
        }
        Integer downloadCourseware = saveTenantDTO.getDownloadCourseware();
        if (Objects.isNull(downloadCourseware)) {
            throw new BusinessException(TenantErrorNoEnum.PLEASE_SET_DOWNLOAD_COURSEWARE);
        }
        Integer smallAppSignin = saveTenantDTO.getSmallAppSignin();
        if (Objects.isNull(smallAppSignin)) {
            throw new BusinessException(TenantErrorNoEnum.PLEASE_SET_MINI_PROGRAM_REGISTER);
        }
        Integer hideH5Title = 0;
        if (Objects.isNull(hideH5Title)) {
            throw new BusinessException(TenantErrorNoEnum.PLEASE_SET_SHIELD_H5_HEAD_INFO);
        }
        TenantConfigDTO config = new TenantConfigDTO();
        config.setDownloadApp(downloadApp);
        config.setDownloadCourseware(downloadCourseware);
        config.setSmallAppSignin(smallAppSignin);
        config.setHideH5Title(hideH5Title);
        return config;
    }


    /**
     * 保存租户
     *
     * @param saveTenantDTO 保存租户数据
     * @param curUserId     当前用户id
     * @param now           当前时间
     * @param tenant        租户
     * @param list          模块列表
     * @param config        配置
     * @param isAgency      是否服务商授权
     */
    private void saveTenant(SaveTenantDTO saveTenantDTO, String curUserId, Date now, Tenant tenant, List<String> list,
        TenantConfigDTO config, Boolean isAgency) {
        log.info("save_tenant_params:[{}]", saveTenantDTO);
        String newTenantId = newId();
        String customCode = saveTenantDTO.getCustomCode();
        // 从前端传入，后台校验唯一性
        LambdaQueryWrapper<Tenant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Tenant::getCustomCode);
        queryWrapper.eq(Tenant::getCustomCode, customCode);
        queryWrapper.eq(Tenant::getIsDel, 0).last(LAST_SQL);
        Long existCode = baseMapper.selectCount(queryWrapper);
        if (existCode == 1) {
            // 编号重复直接抛异常
            throw new BusinessException(TenantErrorNoEnum.DUPLICATE_CODE);
        }

        //填充租户实体类
        tenant.setCustomCode(customCode);
        tenant.setId(newTenantId);
        tenant.setCreateBy(curUserId);
        tenant.setCreateTime(now);
        tenant.setIsDel(0);

        //填充租户配置及个性化信息实体类
        TenantConfig tenantConfig = new TenantConfig();
        tenantConfig.setId(newId());
        tenantConfig.setTenantId(newTenantId);
        tenantConfig.setDownloadApp(config.getDownloadApp());
        tenantConfig.setDownloadCourseware(config.getDownloadCourseware());
        tenantConfig.setDescription(config.getDescription());
        tenantConfig.setSmallAppSignin(config.getSmallAppSignin());
        tenantConfig.setHideH5Title(config.getHideH5Title());
        tenantConfig.setCreateBy(curUserId);
        tenantConfig.setCreateTime(now);
        tenantConfig.setIsDel(0);

        //填充租户选择模块实体类
        TenantModule tenantModule = new TenantModule();
        tenantModule.setId(newId());
        tenantModule.setTenantId(newTenantId);
        StringBuilder stringBuilder = new StringBuilder();
        list.forEach(module -> {
            stringBuilder.append(module);
            stringBuilder.append(",");
        });
        if (!stringBuilder.isEmpty()) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        tenantModule.setModules(stringBuilder.toString());
        tenantModule.setCreateBy(curUserId);
        tenantModule.setCreateTime(now);
        tenantModule.setIsDel(0);

        //保存租户信息
        baseMapper.insert(tenant);
        tenantConfigService.save(tenantConfig);
        tenantModuleService.save(tenantModule);
        tenantBucketService.saveBucketInfo(saveTenantDTO);

        // 初始化租户企业微信带参授权链接缓存
        if (StringUtils.isNotBlank(weComConfig.getCorpId()) && isAgency) {
            tenantFeign.getWeComCustomizedAuthUrl(customCode);
        }
    }

    /**
     * 保存租户
     *
     * @param saveTenantDTO 保存租户数据
     * @param curUserId     添加用户id
     * @param now           时间戳
     * @param tenant        租户
     * @param list          模块列表
     * @param config        租户配置
     */
    private void saveTenant(SaveTenantDTO saveTenantDTO, String curUserId, Date now, Tenant tenant, List<String> list,
        TenantConfigDTO config) {
        saveTenant(saveTenantDTO, curUserId, now, tenant, list, config, true);
    }

    /**
     * 更新租户
     *
     * @param saveTenantDTO 保存租户数据
     * @param tenant        房客
     * @param oldPassword   旧密码
     */
    private void updateTenant(SaveTenantDTO saveTenantDTO, Tenant tenant, String oldPassword) {
        log.info("update_tenant_params:[{}]", saveTenantDTO);
        if (StringUtils.isBlank(saveTenantDTO.getPassword())) {
            tenant.setPassword(oldPassword);
        }
        baseMapper.updateById(tenant);

        //更新租户配置及个性化信息
        UpdateWrapper<TenantConfig> tenantConfigWrapper = Wrappers.update();
        tenantConfigWrapper.lambda()
            .set(TenantConfig::getDownloadApp, saveTenantDTO.getDownloadApp())
            .set(TenantConfig::getDownloadCourseware, saveTenantDTO.getDownloadCourseware())
            .set(TenantConfig::getDescription, saveTenantDTO.getDescription())
            .set(TenantConfig::getSmallAppSignin, saveTenantDTO.getSmallAppSignin())
            .set(TenantConfig::getHideH5Title, 0)
            .eq(TenantConfig::getTenantId, saveTenantDTO.getId());
        tenantConfigService.update(null, tenantConfigWrapper);
        //更新选择模块
        tenantBucketService.updateBucketInfo(saveTenantDTO);
    }

    @Override
    public PageInfo<TenantListDTO> findTenantListByPage(TenantListQueryDTO tenantListQuery) {
        log.info("----findTenantListByPage---入参：" + tenantListQuery.toString());
        PageInfo<TenantListDTO> tenantListDTOPageInfo = PageMethod.startPage(tenantListQuery.getPageNo(),
                tenantListQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectListByPage(tenantListQuery));
        List<Long> businessVersionIds = tenantListDTOPageInfo.getList()
            .stream()
            .map(TenantListDTO::getBusinessVersionId)
            .collect(Collectors.toList());
        if (!businessVersionIds.isEmpty()) {
            List<TenantBusinessVersion> tenantBusinessVersions = tenantBusinessVersionService.listByIds(
                businessVersionIds);
            tenantListDTOPageInfo.getList().forEach(tenantListDTO ->
                tenantBusinessVersions.forEach(tenantBusinessVersion -> {
                    if (tenantListDTO.getBusinessVersionId().equals(tenantBusinessVersion.getId())) {
                        tenantListDTO.setBusinessVersionName(tenantBusinessVersion.getName());
                    }
                })
            );
        }
        return tenantListDTOPageInfo;
    }

    @Override
    public Boolean deleteTenants(Collection<String> ids) {
        log.info("----deleteTenants---传入的参数是：" + ids);

        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        // 修改租户状态
        baseMapper.deleteBatchIds(ids);
        return true;
    }

    @Override
    public List<Tenant> getByIds(Collection<String> tenantIds) {
        if (tenantIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Tenant> query = new LambdaQueryWrapper<>();
        query.eq(Tenant::getIsDel, DelEnum.NOT_DELETE.getValue());
        query.in(Tenant::getId, tenantIds);
        return baseMapper.selectList(query);
    }

    @Override
    public String availableTenants(AvailableDTO availableDTO) {
        String userId = UserThreadContext.getUserId();
        log.info("-----availableTenants----参数：" + availableDTO + "  当前用户：" + userId);
        List<String> tenantIdList = availableDTO.getIds();
        Integer isAvailable = availableDTO.getStatus();
        boolean empty = CollectionUtils.isEmpty(tenantIdList);
        if (empty) {
            return "参数不对，请检测传递的参数";
        }

        List<TenantDeploymentStatus> deploymentList = tenantDeploymentStatusService.lambdaQuery()
            .select(TenantDeploymentStatus::getTenantId, TenantDeploymentStatus::getContainerId)
            .in(TenantDeploymentStatus::getTenantId, tenantIdList)
            .list();

        List<String> errorUser = Collections.synchronizedList(new ArrayList<>());

        // 获取数据库信息
        List<TenantDbInstance> dbInstanceList = tenantDbInstanceService.getDbInstanceList(tenantIdList);
        Map<String, String> tenantMap = tenantService.lambdaQuery()
            .select(Tenant::getId, Tenant::getCustomCode)
            .in(Tenant::getId, tenantIdList)
            .list()
            .stream()
            .collect(Collectors.toMap(Tenant::getId, Tenant::getCustomCode, (k1, k2) -> k1));
        Map<String, List<TenantDbInstance>> dbInstanceMap = dbInstanceList.stream().parallel()
            .collect(Collectors.groupingBy(key -> tenantMap.get(key.getTenantId())));

        deploymentList.parallelStream().forEach(item -> {
            // 运维系统的唯一标识
            String containerId = item.getContainerId();
            String url;

            if (isAvailable == 0) {
                url = MessageFormat.format(operationUrlProperties.getDisable(), containerId);
            } else {
                url = MessageFormat.format(operationUrlProperties.getEnable(), containerId);
            }

            ResponseDTO responseDTO = HttpUtil.sendHttpPutReturnCode(url, Collections.emptyMap(), StringUtils.EMPTY);
            log.info("http_put_response_content_json:[{}]", responseDTO);

            if (isAvailable == 1) {
                // 构建消息体
                Map<String, Map<String, DataBaseInfo>> dataSourceMap = dbInstanceList.stream()
                    .collect(
                        Collectors.groupingBy(
                            key -> tenantMap.get(key.getTenantId()),
                            Collectors.toMap(TenantDbInstance::getModuleCode, this::buildDataSource, (k1, k2) -> k1)
                        )
                    );

                tenantService.initTenantDbInstance(dbInstanceMap.get(tenantMap.get(item.getTenantId())),
                    tenantMap.get(item.getTenantId()));
                TenantDataSourceEvent tenantDataSourceEvent = new TenantDataSourceEvent(dataSourceMap);
                rabbitMqProducer.sendMsg(tenantDataSourceEvent);
            } else {
                tenantService.removeTenantDbInstance(tenantMap.get(item.getTenantId()));
                TenantDataSourceRemoveEvent tenantDataSourceRemoveEvent = new TenantDataSourceRemoveEvent(
                    DB_LOOK_UP_KEY_PREFIX + tenantMap.get(item.getTenantId())
                );
                rabbitMqProducer.sendMsg(tenantDataSourceRemoveEvent);
            }

            if (responseDTO.getCode() != HttpStatus.OK.value()) {
                errorUser.add(item.getTenantId());
            }

        });

        tenantIdList.removeAll(errorUser);
        Tenant updateTenant = new Tenant();
        updateTenant.setIsAvailable(isAvailable);
        updateTenant.setUpdateBy(userId);
        updateTenant.setUpdateTime(new Date());

        if (!CollectionUtils.isEmpty(tenantIdList)) {
            LambdaQueryWrapper<Tenant> query = new LambdaQueryWrapper<>();
            query.in(Tenant::getId, tenantIdList);
            baseMapper.update(updateTenant, query);
        }

        if (errorUser.isEmpty()) {
            return StringUtils.EMPTY;
        }

        LambdaQueryWrapper<Tenant> query = new LambdaQueryWrapper<>();
        query.in(Tenant::getId, tenantIdList);
        List<Tenant> tenantList = baseMapper.selectList(query);
        Map<String, String> tenantInfoMap = tenantList.stream()
            .collect(Collectors.toMap(Tenant::getId, Tenant::getCompanyName, (key1, key2) -> key1));
        StringBuilder errMsg = new StringBuilder();
        for (String tenantId : errorUser) {
            errMsg.append(tenantInfoMap.getOrDefault(tenantId, "")).append(",");
        }
        if (isAvailable == 0) {
            errMsg.append("禁用失败");
        } else {
            errMsg.append("启用失败");
        }

        return errMsg.toString();
    }

    private DataBaseInfo buildDataSource(TenantDbInstance prop) {
        DataBaseInfo dataSource = new DataBaseInfo();
        String jdbcUrl = MessageFormat.format(JDBC_URL_FORMAT, prop.getInstanceName(), "3306", prop.getDatabaseName());
        dataSource.setJdbc(jdbcUrl);
        dataSource.setUsername(prop.getUserName());
        dataSource.setPassword(prop.getPassword());
        return dataSource;
    }

    /**
     * 判断是否更新图片
     *
     * @param newNamePath 图片路径
     * @param tenantId    租户Id
     * @param imageType   图像类型
     */
    public void updateImages(NamePath newNamePath, String tenantId, ImageTypeEnum imageType) {
        NamePath namePath = imagesService.getImagePathByTenant(tenantId);
        String oldPath = namePath.getPath();
        String newPath = newNamePath.getPath();
        // 图片地址变化处理
        if (!StringUtils.equals(oldPath, newPath)) {
            // 对象存储文件上传
            UploadFileResponse uploadFileResponse = imageUtil.saveFile(
                FileTypeEnum.IMAGE,
                newPath,
                imageType.name()
            );

            // 添加新的图片
            LambdaUpdateWrapper<Images> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(Images::getCategoryId, tenantId);
            queryWrapper.eq(Images::getIsDel, DelEnum.NOT_DELETE.getValue());
            queryWrapper.eq(Images::getCategoryType, imageType);
            queryWrapper.set(Images::getImageSize, uploadFileResponse.getFileSize())
                .set(Images::getCurrentPath, uploadFileResponse.getFileUrl());
            imagesService.update(queryWrapper);
        }
    }

    @Override
    public TenantDTO findTenantDetailById(String tenantId) {
        Tenant tenant = baseMapper.selectById(tenantId);
        if (Objects.isNull(tenant)) {
            throw new BusinessException(TenantErrorNoEnum.TENANT_NOT_EXISTS);
        }
        TenantDTO tenantDTO = new TenantDTO();
        BeanUtils.copyProperties(tenant, tenantDTO);
//        tenantDTO.setActiveColor(tenant.getBackgroundColor());
        // FIXME 这里应该获取租户模块表设置的数据
        List<TenantModuleDTO> allModules = tenantModuleService.findAllModules();
        StringBuilder moduleName = new StringBuilder();
        List<String> moduleCode = new ArrayList<>();
        allModules.forEach(module -> {
            moduleName.append(module.getModuleName());
            moduleName.append(",");
            moduleCode.add(module.getModuleCode());
        });
        TenantConfigDTO configs = tenantConfigService.findConfigs(tenantId);
        tenantDTO.setDownloadApp(configs.getDownloadApp());
        tenantDTO.setDownloadCourseware(configs.getDownloadCourseware());
        tenantDTO.setDescription(configs.getDescription());
        tenantDTO.setBucketInfo(tenantBucketService.getBucketInfo(tenant.getCustomCode()));
        tenantDTO.setSmallAppSignin(configs.getSmallAppSignin());
        return tenantDTO;
    }

    @Override
    public Boolean checkTenantRepeat(CheckRepeatQueryDTO queryDTO) {
        Integer num = baseMapper.checkTenantRepeat(queryDTO);
        return num != null && num != 0;
    }

    @Override
    public void exportData(TenantListQueryDTO tenantListQuery, HttpServletResponse response,
        HttpServletRequest request) throws Exception {
        PageInfo<TenantListDTO> tenantListByPage = this.findTenantListByPage(tenantListQuery);
        List<TenantListDTO> list = tenantListByPage.getList();
        String[] title = {"公司名称", "账号", "租期", "是否启用"};
        String[][] arr = new String[list.size()][];
        int n = 0;
        for (TenantListDTO vo : list) {
            String[] strArr = new String[title.length];
//            strArr[0] = vo.getCompanyName();
            strArr[1] = vo.getLoginName();
            strArr[2] =
                DateUtil.formatDate(vo.getTenantStartTime()) + "至" + DateUtil.formatDate(vo.getTenantEndTime());
            strArr[3] = vo.getIsAvailable() == 1 ? "是" : "否";
            arr[n] = strArr;
            n++;
        }
        exportExcel1(response, request, arr, "租户管理列表.xls", title);
    }

    @Override
    public UploadResultDTO uploadImg(MultipartFile multipartFile, String realFileName) {
        if (multipartFile.isEmpty()) {
            throw new BusinessException(FileErrorNoEnum.ERR_UPLOAD_FILE_NOT_NULL);
        }

        String originalFilename = multipartFile.getOriginalFilename();

        String fileName = newId() + "." + FilenameUtils.getExtension(originalFilename);

        String currPath = FileUtil.getTenantImagePath(null);

        String allPath = FileUtil.getPhysicalPath(currPath);
        FileUtil.exitDirs(allPath);
        File file = new File(allPath + fileName);
        try {
            multipartFile.transferTo(file);
        } catch (Exception e) {
            log.error("upload_img_error", e);
        }
        UploadResultDTO uploadResultDTO = new UploadResultDTO();
        uploadResultDTO.setDiskFileName(originalFilename);
        uploadResultDTO.setFileName(fileName);
        // 图片临时存在地址
        uploadResultDTO.setPath(currPath + fileName);
        uploadResultDTO.setUrl(urlHelper.getStaticFullUrl(currPath + fileName));
        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(currPath + fileName);
        uploadFileRequest.setAsyncUpload(false);
        uploadFileRequest.setTempFilePath(file.getAbsolutePath());
        uploadFileRequest.setCopyFolderFlag(false);
        imageUtil.uploadFile(uploadFileRequest);
        return uploadResultDTO;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void resetBucket(String tenantId) {
        boolean notEmpty = StringUtils.isNotEmpty(tenantId);
        // 收集redis中已存在的租户,不进行重置
        Set<String> notResetTenant = new HashSet<>();
        if (notEmpty) {
            redisTemplate.opsForHash().delete(TenantRedisKeyConstant.BUCKET_KEY, tenantId);
            // 使用tenantId接收customCode变量,
            tenantId = getById(tenantId).getCustomCode();
        } else {
            Map<String, String> keyMap = redisTemplate.opsForHash().entries(TenantRedisKeyConstant.BUCKET_KEY);
            notResetTenant = keyMap.keySet();
        }

        Set<String> tenantIds = tenantDeploymentStatusService.lambdaQuery()
            .eq(TenantDeploymentStatus::getStatus, 2)
            .select(TenantDeploymentStatus::getTenantId)
            .list()
            .stream()
            .map(TenantDeploymentStatus::getTenantId)
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(tenantIds)) {
            return;
        }

        // 只重置启用的租户
        Set<String> customCodes = lambdaQuery()
            .select(Tenant::getCustomCode)
            .in(Tenant::getId, tenantIds)
            .eq(Tenant::getIsAvailable, 1)
            .list()
            .stream()
            .map(Tenant::getCustomCode)
            .collect(Collectors.toSet());

        // 去除redis中已经存在的租户,已存在的不进行reset
        customCodes.removeAll(notResetTenant);

        if (customCodes.isEmpty()) {
            return;
        }

        List<TenantBucket> tenantBuckets = tenantBucketService.lambdaQuery()
            .eq(notEmpty, TenantBucket::getTenantCustomCode, tenantId)
            .in(TenantBucket::getTenantCustomCode, customCodes)
            .list();
        tenantBuckets.forEach(tenantBucket -> {
            String customCode = tenantBucket.getTenantCustomCode();
            // 修改了租户的方案，这里兼容租户老的消费者,直接将该对象设置到bucketName中
            TenantBucketInfo tenantBucketInfo = new TenantBucketInfo();

            // 这里的常量后续可按需求配置区域和对象存储类型
            tenantBucketInfo.setAccessKey(tenantBucket.getAccessKey())
                .setEndPoint(tenantBucket.getEndPoint())
                .setIntranetEndPoint(tenantBucket.getIntranetEndPoint())
                .setVpcEndPoint(tenantBucket.getVpcEndPoint())
                .setSecretKey(tenantBucket.getSecretKey())
                .setRootPath(customCode)
                .setRegion(tenantBucket.getRegion())
                .setType(tenantBucket.getType());

            // 序列化数据
            String value = JsonUtil.objToJson(tenantBucketInfo);
            // 给redis存入bucket数据
            assert value != null;
            redisTemplate.opsForHash()
                .put(TenantRedisKeyConstant.BUCKET_KEY, customCode, value);
        });
    }

    @Override
    @SuppressWarnings("unchecked")
    public void resetDataSource(String tenantId) {
        boolean notEmpty = StringUtils.isNotEmpty(tenantId);
        // 收集redis中已存在的租户,不进行重置
        Set<String> notResetTenant = new HashSet<>();
        if (notEmpty) {
            redisTemplate.opsForHash().delete(TenantRedisKeyConstant.DB_KEY, tenantId);
        } else {
            Map<String, String> keyMap = redisTemplate.opsForHash().entries(TenantRedisKeyConstant.DB_KEY);
            notResetTenant = keyMap.keySet();
//            redisTemplate.unlink(TenantRedisKeyConstant.DB_KEY);
        }

        Set<String> tenantIds = tenantDeploymentStatusService.lambdaQuery()
            .eq(TenantDeploymentStatus::getStatus, 2)
            .select(TenantDeploymentStatus::getTenantId)
            .list()
            .stream()
            .map(TenantDeploymentStatus::getTenantId)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(tenantIds)) {
            return;
        }

        Map<String, List<TenantDbInstance>> listMap = tenantDbInstanceService.lambdaQuery()
            .eq(StringUtils.isNotEmpty(tenantId), TenantDbInstance::getTenantId, tenantId)
            .in(TenantDbInstance::getTenantId, tenantIds)
            .list()
            .stream()
            .collect(Collectors.groupingBy(TenantDbInstance::getTenantId, Collectors.toList()));

        Map<String, String> tenantMap = lambdaQuery()
            .select(Tenant::getId, Tenant::getCustomCode)
            .in(Tenant::getId, listMap.keySet())
            .eq(Tenant::getIsAvailable, 1)
            .list()
            .stream()
            .collect(Collectors.toMap(Tenant::getId, Tenant::getCustomCode));

        // 不再加载的租户编号的集合
        final Set<String> finalNotResetTenant = notResetTenant.stream()
            .map(item -> item.replace(DB_LOOK_UP_KEY_PREFIX, ""))
            .collect(Collectors.toSet());

        // 遍历
        listMap.forEach((key, value) -> {

            String tenantCustomCode = tenantMap.get(key);
            // 被禁用的租户 或者 redis中已加载过的租户不再加载DS到redis
            if (StringUtils.isEmpty(tenantCustomCode) || finalNotResetTenant.contains(tenantCustomCode)) {
                return;
            }

            Map<String, DataBaseInfo> dataBaseInfoMap = Maps.newHashMapWithExpectedSize(value.size());
            for (TenantDbInstance tenantDbInstance : value) {
                String instanceName = tenantDbInstance.getInstanceName();
                String jdbcUrl = MessageFormat.format(JDBC_URL_FORMAT, instanceName, "3306",
                    tenantDbInstance.getDatabaseName());
                DataBaseInfo dataBaseInfo = new DataBaseInfo();
                dataBaseInfo.setUsername(tenantDbInstance.getUserName())
                    .setPassword(tenantDbInstance.getPassword())
                    .setJdbc(jdbcUrl);
                dataBaseInfoMap.put(tenantDbInstance.getModuleCode(), dataBaseInfo);
            }

            redisTemplate.opsForHash().put(TenantRedisKeyConstant.DB_KEY,
                DB_LOOK_UP_KEY_PREFIX + tenantCustomCode,
                Objects.requireNonNull(JsonUtil.objToJson(dataBaseInfoMap))
            );
        });

        // 遍历 检查已存在的租户是否有添加数据源
        /*listMap.forEach((key, value) -> {

            String tenantCustomCode = tenantMap.get(key);
            // 被禁用的租户 或者 redis中已加载过的租户不再加载DS到redis
            if (finalNotResetTenant.contains(tenantCustomCode)) {

                HashOperations<String,String,String> hashOperations = redisTemplate.opsForHash();

                String redisJson = hashOperations.get(TenantRedisKeyConstant.DB_KEY,
                    DB_LOOK_UP_KEY_PREFIX + tenantCustomCode);
                Map<String, DataBaseInfo> redisDsMap = JsonUtil.parseObject(redisJson, Map.class);

                boolean isUpdate = false;

                Map<String, DataBaseInfo> dataBaseInfoMap = Maps.newHashMapWithExpectedSize(value.size());
                for (TenantDbInstance tenantDbInstance : value) {
                    String instanceName = tenantDbInstance.getInstanceName();
                    String jdbcUrl = MessageFormat.format(JDBC_URL_FORMAT, instanceName, "3306",
                        tenantDbInstance.getDatabaseName());
                    DataBaseInfo dataBaseInfo = new DataBaseInfo();
                    dataBaseInfo.setUsername(tenantDbInstance.getUserName())
                        .setPassword(tenantDbInstance.getPassword())
                        .setJdbc(jdbcUrl);

                    DataBaseInfo old = redisDsMap.get(tenantDbInstance.getModuleCode());
                    if(
                        old==null||
                        !Objects.equals(old.getUsername(),tenantDbInstance.getUserName())||
                        !Objects.equals(old.getPassword(),tenantDbInstance.getPassword())||
                        !Objects.equals(old.getJdbc(),jdbcUrl)
                    ){
                        isUpdate = true;
                    }
                    dataBaseInfoMap.put(tenantDbInstance.getModuleCode(), dataBaseInfo);
                }

                if(isUpdate){
                    log.info("tenantId:{},数据源有变化 {}",tenantCustomCode,JsonUtil.objToJson(dataBaseInfoMap));
                    hashOperations.put(TenantRedisKeyConstant.DB_KEY,
                        DB_LOOK_UP_KEY_PREFIX + tenantCustomCode,
                        Objects.requireNonNull(JsonUtil.objToJson(dataBaseInfoMap))
                    );
                }
            }

        });*/

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void tenantDetailDateTransform(String tenantId, String dbHost) {
        if (!(Objects.equals("mysql", dbHost) || Objects.equals("mysql1", dbHost) || Objects.equals("mysql2",
            dbHost))) {
            throw new BusinessException(TenantErrorNoEnum.FUNCTION_NOT_IMPLEMENTED);
        }
        Tenant tenant = baseMapper.selectById(tenantId);
        if (Objects.isNull(tenant)) {
            throw new BusinessException(TenantErrorNoEnum.TENANT_NOT_EXISTS);
        }

        TenantDeploymentDataBaseDTO tenantDeploymentDataBaseDTO = new TenantDeploymentDataBaseDTO();
        String userId = UserThreadContext.getUserId();
        Date now = new Date();

        // 设置租户数据库信息,并保存住户模块数据库配置到数据库,向redis增量插入数据,如果是已经部署过的,返回之前保存好的容器id
        String containerId = setTenantDbConfigInfo(tenant, tenantDeploymentDataBaseDTO, userId, now, dbHost);

        // 配置其它租户配置信息
        setTenantOtherConfigInfo(tenant, tenantDeploymentDataBaseDTO);

        //暂时填充必要字段
        tenantDeploymentDataBaseDTO.setName("default");
        tenantDeploymentDataBaseDTO.setLogo("https://saasdev.oss.wdxuexi.com/help/file/logoDefaultImage/logoIcon.ico");
        tenantDeploymentDataBaseDTO.setColor("#1E90FFFF");
        tenantDeploymentDataBaseDTO.setActiveColor("#216CB4FF");
        tenantDeploymentDataBaseDTO.setPrivacyPolicy(0);

        //通过http请求发送数据到 运维系统
        String json = JsonUtil.objToJson(tenantDeploymentDataBaseDTO);

        log.info("send_http_json:[{}]", json);

        // 异步处理发布记录
        rabbitMqProducer.sendMsg(
            new DeployLogEvent(tenantDeploymentDataBaseDTO, userId, tenantId, now));

        // 容器id为空,更新租户
        if (StringUtils.isNotEmpty(containerId)) {
            // 调用更新租户的接口
            handlerUpdateOperation(containerId, json, tenantId);
        } else {
            // 调用新建租户接口
            handlerCreateOperation(tenant, now, json);
        }
        saveGetDomainToCache(tenant);
    }

    /**
     * 处理租户创建操作
     *
     * @param tenant 房客
     * @param now    现在
     * @param json   json语言
     */
    private void handlerCreateOperation(Tenant tenant, Date now, String json) {
        String responseContent = HttpUtil.sendHttpPost(operationUrlProperties.getUrl(), Collections.emptyMap(), json);
        log.info("http_post_response_content_json:[{}]", responseContent);

        // 保存部署状态
        saveDeployStatus(tenant.getId(), UserThreadContext.getUserId(), now, responseContent);

        // 为租户创建对象存储桶,保存映射关系到数据库
        createObjectStoreBucket(tenant);
    }

    /**
     * 处理租户更新操作
     *
     * @param containerId 容器id
     * @param json        json
     */
    private void handlerUpdateOperation(String containerId, String json, String tenantId) {
        String url = MessageFormat.format(operationUrlProperties.getUpdate(), containerId);
        String responseContent = HttpUtil.sendHttpPut(url, Collections.emptyMap(), json);
        log.info("http_put_response_content_json:[{}]", responseContent);
        // 刷新部署状态和时间
        tenantDeploymentStatusService.lambdaUpdate()
            .set(TenantDeploymentStatus::getStatus, DeploymentStatusEnum.DEPLOYING.getCode())
            .set(TenantDeploymentStatus::getUpdateTime, new Date())
            .set(TenantDeploymentStatus::getCreateTime, new Date())
            .eq(TenantDeploymentStatus::getTenantId, tenantId)
            .update();
    }

    /**
     * 初始化租户数据库实例
     *
     * @param tenantDbInstanceList 租户数据库实例
     */
    @Override
    @SuppressWarnings("unchecked")
    public void initTenantDbInstance(List<TenantDbInstance> tenantDbInstanceList, String customCode) {
        String lookUpKey = DB_LOOK_UP_KEY_PREFIX + customCode;

        Map<String, DataBaseInfo> value = Maps.newHashMapWithExpectedSize(tenantDbInstanceList.size());
        for (TenantDbInstance tenantDbInstance : tenantDbInstanceList) {
            String instanceName = tenantDbInstance.getInstanceName();
            String jdbcUrl = MessageFormat.format(JDBC_URL_FORMAT, instanceName, "3306",
                tenantDbInstance.getDatabaseName());
            DataBaseInfo dataBaseInfo = new DataBaseInfo();
            dataBaseInfo.setUsername(tenantDbInstance.getUserName())
                .setPassword(tenantDbInstance.getPassword())
                .setJdbc(jdbcUrl);
            value.put(tenantDbInstance.getModuleCode(), dataBaseInfo);
        }

        redisTemplate.opsForHash().put(TenantRedisKeyConstant.DB_KEY, lookUpKey,
            Objects.requireNonNull(JsonUtil.objToJson(value)));
    }

    @Override
    public void removeTenantDbInstance(String customCode) {
        redisTemplate.opsForHash().delete(TenantRedisKeyConstant.DB_KEY, DB_LOOK_UP_KEY_PREFIX + customCode);
    }

    @Override
    public boolean checkFileType(String originalFilename, String[] types) {
        String suffix = FilenameUtils.getExtension(originalFilename);
        if (null == suffix || !ArrayUtils.contains(types, suffix.toLowerCase())) {
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_FORMAT);
        }
        return true;
    }

    @Override
    public void domainFlushTask() {
        List<Tenant> tenants = baseMapper.selectList(new LambdaQueryWrapper<Tenant>()
            .eq(Tenant::getIsDel, 0));
        for (Tenant tenant : tenants) {
            saveGetDomainToCache(tenant);
        }
    }

    /**
     * @param tenant
     */
    @Override
    public TenantDomainDTO saveGetDomainToCache(Tenant tenant) {
        TenantDomainDTO tenantDomainDTO = new TenantDomainDTO();
        // 状态,0：已分配域名  1：域名分配中  2：不存在该租户
        int status = 0;
        String companyDomainName = tenant.getCompanyDomainName();
        if (StringUtils.isEmpty(companyDomainName)) {
            status = 1;
        }
        TenantThirdApp tenantThirdApp = tenantThirdAppService.getTenantIdAndAppType(tenant.getId(),
            Oauth2EntryTypeEnum.WECOM.getType());
        if (tenantThirdApp == null) {
            return tenantDomainDTO;
        }
        tenantDomainDTO.setStatus(status);
        tenantDomainDTO.setDomainUrl(companyDomainName);
        tenantDomainDTO.setAgentId(tenantThirdApp.getAgentId());
        // 缓存租户域名 基于 微信企业id
        saveDomainToCache(tenantThirdApp, tenantDomainDTO);
        return tenantDomainDTO;
    }

    @Override
    public TenantDomainDTO getDomainCacheByCorpId(String corpId, Oauth2EntryTypeEnum oauth2EntryTypeEnum) {
        TenantDomainDTO tenantDomainDTO = new TenantDomainDTO();
        TenantThirdApp tenantThirdApp = tenantThirdAppService.getByCorpIdAndAppType(corpId,
            oauth2EntryTypeEnum.getType());
        if (tenantThirdApp == null) {
            tenantDomainDTO.setStatus(2);
            return tenantDomainDTO;
        }

        Tenant tenant = tenantService.getOne(
            new LambdaQueryWrapper<Tenant>().eq(Tenant::getId, tenantThirdApp.getTenantId()));
        // 状态,0：已分配域名  1：域名分配中  2：不存在该租户
        int status = 0;
        String companyDomainName = tenant.getCompanyDomainName();
        if (StringUtils.isEmpty(companyDomainName)) {
            status = 1;
        }
        tenantDomainDTO.setStatus(status);
        tenantDomainDTO.setDomainUrl(companyDomainName);
        tenantDomainDTO.setAgentId(tenantThirdApp.getAgentId());
        saveDomainToCache(new TenantThirdApp().setCorpId(corpId), tenantDomainDTO);
        // 构建支持企微跳转的域名
        tenantDomainDTO.setDomainUrl(buildCompleteDomain(tenantDomainDTO));
        return tenantDomainDTO;
    }

    public String buildCompleteDomain(TenantDomainDTO domainDTO) {
        String domainUrl = domainDTO.getDomainUrl();
        HttpServletRequest request = Objects.requireNonNull(WebUtil.getRequest());
        String code = request.getParameter("code");
        if (StringUtils.isEmpty(code)) {
            String corpId = request.getParameter("corpid");
            String agentId = domainDTO.getAgentId();
            String serverName = Objects.requireNonNull(WebUtil.getRequest()).getServerName();
            String redirectUri = MessageFormat.format(THIRD_ACCESS_MODEL_REDIRECT_URI_FORMAT,
                serverName,
                corpId,
                0,
                corpId,
                agentId,
                Optional.ofNullable(request.getParameter("wechattype")).orElse("0"),
                Optional.ofNullable(request.getParameter("wdtype")).map(wdtype -> "&wdtype=" + wdtype)
                    .orElse(StringUtils.EMPTY));

            return MessageFormat.format(WECHAT_URL_FORMAT, corpId,
                URLEncoder.encode(redirectUri, StandardCharsets.UTF_8), agentId)
                + "#wechat_redirect";
        } else {
            String appId = request.getParameter("appid");
            String agentId = request.getParameter("agentid");

            return MessageFormat.format(REDIRECT_URI_FORMAT, domainUrl, request.getParameter("entrytype"),
                appId, agentId, Optional.ofNullable(request.getParameter("wdtype")).map(wdtype -> "&wdtype=" + wdtype)
                    .orElse(StringUtils.EMPTY),
                request.getParameter("wechattype"), code);
        }

    }

    private void saveDomainToCache(TenantThirdApp tenantThirdApp, TenantDomainDTO tenantDomainDTO) {
        String tenantDomainCorpIdKey = RedisKeyEnum.getBizFullKey(TenantRedisKeyConstant.TENANT_DOMAIN,
            Oauth2EntryTypeEnum.WECOM.name()) + ":" + tenantThirdApp.getCorpId();
        redisTemplate.opsForValue().set(tenantDomainCorpIdKey, tenantDomainDTO);
    }

    public Map<String, String> diffBusinessVersion(String tenantId) {
        Tenant tenant = baseMapper.selectById(tenantId);
        // 取最后一次部署记录
        TenantBusinessVersionRecord lastDeployRecord = tenantBusinessVersionRecordService.getLastDeployRecord(tenantId);
        String oldDmlFilePath = lastDeployRecord.getDmlFilePath();
        String oldDmlPhysicalPath = FileUtil.getPhysicalPath(oldDmlFilePath);
        String oldContent = "";
        try {
            oldContent = Files.readString(Paths.get(oldDmlPhysicalPath));
        } catch (IOException e) {
            log.error("获取旧的脚本内容出错", e);
        }

        //按当前选择的业务版本创建dml文件
        tenantBusinessVersionService.createBusinessVersionDml(tenant.getBusinessVersionId(), tenant);
        TenantBusinessVersionRecord tenantBusinessVersionRecord = tenantBusinessVersionRecordService.getByTenantId(
            tenantId);
        String newDmlFilePath = tenantBusinessVersionRecord.getDmlFilePath();
        String newDmlPhysicalPath = FileUtil.getPhysicalPath(newDmlFilePath);
        String newContent = "";
        try {
            newContent = Files.readString(Paths.get(newDmlPhysicalPath));
        } catch (IOException e) {
            log.error("获取新的脚本内容出错", e);
        }
        return Map.of("old", oldContent, "new", newContent);
    }

    @Override
    public void upgradeDeploy(String tenantId) {
        Tenant tenant = baseMapper.selectById(tenantId);
        if (Objects.isNull(tenant)) {
            throw new BusinessException(TenantErrorNoEnum.TENANT_NOT_EXISTS);
        }
        TenantDeploymentStatus deploymentStatus = tenantDeploymentStatusService.getDeploymentStatus(tenantId);
        if (deploymentStatus == null) {
            throw new BusinessException(TenantErrorNoEnum.TENANT_NOT_DEPLOY);
        }
        //生成DML文件
        tenantBusinessVersionService.createBusinessVersionDml(tenant.getBusinessVersionId(), tenant);
        TenantBusinessVersionRecord tenantBusinessVersionRecord = tenantBusinessVersionRecordService.getByTenantId(
            tenantId);
        if (tenantBusinessVersionRecord == null) {
            throw new BusinessException(TenantErrorNoEnum.TENANT_NOT_SELECT_BUSINESS_VERSION);
        }

        String url = MessageFormat.format(operationUrlProperties.getUpgrade(), deploymentStatus.getContainerId());
        Map<String, Object> form = new HashMap<>();
        String dmlPhysicalPath = FileUtil.getPhysicalPath(tenantBusinessVersionRecord.getDmlFilePath());
        form.put("sqlfile",
            List.of(
                new File(dmlPhysicalPath)
            )
        );
        log.info("upgradeDeploy form:[{}]", form);
        String responseContent = null;
        try {
            responseContent = HttpUtil.sendHttpPostForm(url, form);
            log.info(UPGRADE_DEPLOY_RESPONSE_CONTENT, responseContent);
        } catch (Exception e) {
            log.error(UPGRADE_DEPLOY_RESPONSE_CONTENT, responseContent, e);
            throw new BusinessException(TenantErrorNoEnum.TENANT_UPGRADE_DEPLOY_FAIL);
        }

        if (responseContent == null || responseContent.isEmpty()) {
            log.error(UPGRADE_DEPLOY_RESPONSE_CONTENT, responseContent);
            throw new BusinessException(TenantErrorNoEnum.TENANT_UPGRADE_DEPLOY_FAIL);
        }

        tenantBusinessVersionRecord.setDeployTime(new Date());
        tenantBusinessVersionRecordService.updateById(tenantBusinessVersionRecord);
    }

    @Override
    public void cleanRedisCache(String tenantId) {
        // 删除该租户下所有的redis缓存
        try {
            Tenant tenant = tenantService.getById(tenantId);
            if (tenant == null) {
                return;
            }
            String pattern = "*:" + tenant.getCustomCode();
            AtomicInteger totalDeleted = new AtomicInteger();

            cacheRedisTemplate.execute((RedisCallback<Object>) connection -> {
                try (Cursor<byte[]> cursor = connection.scan(
                    ScanOptions.scanOptions().match(pattern).count(10000).build())) {
                    List<String> keys = new ArrayList<>();
                    while (cursor.hasNext()) {
                        keys.add(new String(cursor.next()));
                        if (keys.size() >= 100) {
                            cacheRedisTemplate.delete(keys);
                            totalDeleted.addAndGet(keys.size());
                            keys.clear();
                        }
                    }
                    if (!keys.isEmpty()) {
                        cacheRedisTemplate.delete(keys);
                        totalDeleted.addAndGet(keys.size());
                    }
                }
                return null;
            });

            if (totalDeleted.get() > 0) {
                log.info("Successfully deleted all redis keys for tenant: {}, pattern: {}, total deleted: {}",
                    tenantId, pattern, totalDeleted);
            }
        } catch (Exception e) {
            log.error("Failed to delete redis keys for tenant: {}", tenantId, e);
        }
    }

    @Override
    public List<Tenant> getByVersionIds(Long businessVersionId) {
        if (businessVersionId != null) {
            return baseMapper.selectList(
                new LambdaQueryWrapper<Tenant>()
                    .eq(Tenant::getBusinessVersionId, businessVersionId)
            );
        }
        return new ArrayList<>();
    }

    @Override
    public void syncAllTenantAllRoleRouter() {
        rabbitMqProducer.sendMsg(new SyncTenantAllRoleRouterEvent());
    }

    /**
     * 创建对象存储桶,存储租户与bucketName映射关系,发送消息到微服务文件模块
     *
     * @param tenant 租户
     */
    @SuppressWarnings("unchecked")
    private void createObjectStoreBucket(Tenant tenant) {
        String customCode = tenant.getCustomCode();
        // 修改了租户的方案，这里兼容租户老的消费者,直接将该对象设置到bucketName中
        TenantBucket tenantBucket = tenantBucketService.lambdaQuery()
            .eq(TenantBucket::getTenantCustomCode, customCode)
            .one();

        TenantBucketInfo tenantBucketInfo = new TenantBucketInfo();
        // 这里的常量后续可按需求配置区域和对象存储类型
        tenantBucketInfo.setAccessKey(tenantBucket.getAccessKey())
            .setEndPoint(tenantBucket.getEndPoint())
            .setIntranetEndPoint(tenantBucket.getIntranetEndPoint())
            .setVpcEndPoint(tenantBucket.getVpcEndPoint())
            .setSecretKey(tenantBucket.getSecretKey())
            .setRootPath(customCode)
            .setRegion(tenantBucket.getRegion())
            .setType(tenantBucket.getType());

        // 序列化数据
        String value = JsonUtil.objToJson(tenantBucketInfo);

        // 给redis存入bucket数据
        assert value != null;
        redisTemplate.opsForHash()
            .put(TenantRedisKeyConstant.BUCKET_KEY, customCode, value);

        // 发送消息到文件模块,给配置增加桶
        TenantBucketMsg tenantBucketMsg = new TenantBucketMsg()
            .setTenantBucketName(value)
            .setTenantCustomerCode(customCode);
        TenantBucketEvent tenantBucketEvent = new TenantBucketEvent(tenantBucketMsg);
        rabbitMqProducer.sendMsg(tenantBucketEvent);
    }

    /**
     * 设置租户数据库配置信息,保存住户模块数据库配置到数据库,向redis增量插入数据
     *
     * @param tenant                      租户
     * @param tenantDeploymentDataBaseDTO 租户部署数据库dto
     * @param userId                      用户id
     * @param now                         时间戳
     */
    private String setTenantDbConfigInfo(Tenant tenant, TenantDeploymentDataBaseDTO tenantDeploymentDataBaseDTO,
        String userId, Date now, String dbHost) {
        String containerId = StringUtils.EMPTY;
        final String tenantId = tenant.getId();
        final String customCode = tenant.getCustomCode();
        // 正常情况版本只有一条数据
        final String version = tenantVersionService.lambdaQuery()
            .orderByDesc(TenantVersion::getId)
            .last(LAST_SQL)
            .one().getVersion();

        Map<String, ServiceDbConfig> modules = new HashMap<>(ModuleEnum.values().length);
        List<TenantDbInstance> tenantDbInstanceList = new ArrayList<>();
        TenantDeploymentStatus tenantDeploymentStatus = tenantDeploymentStatusService.getDeploymentStatus(tenantId);

        // 如果部署信息为空，则为首次部署，首次部署数据库信息 需重新生成
        boolean condition = Objects.isNull(tenantDeploymentStatus);
        if (condition) {
            // 应该获取租户 tenant_module 的对应枚举
            List<TenantModuleDTO> allModules = tenantModuleService.findUsingModules(tenantId);
            if (CollectionUtils.isEmpty(allModules)) {
                throw new BusinessException(TenantErrorNoEnum.EMPTY_MODULE);
            }
            allModules.forEach(module -> {
                ServiceDbConfig moduleDTO = new ServiceDbConfig();
                String host = dbHost;
                // 数据库名称 = 模块名 + （唯一）组织编码 (数据库账号和数据库名称命名规则相同所以共用一个变量)
                String moduleCode = module.getModuleCode();
                String databaseName = moduleCode + "_" + customCode;
                String password = generateRandomPassword(8);

                moduleDTO.setHost(host)
                    .setUser(databaseName)
                    .setPassword(password)
                    .setName(databaseName)
                    .setPort("3306");

                modules.put(moduleCode, moduleDTO);

                //组装 保存用的 数据库实例信息
                TenantDbInstance dbInstance = new TenantDbInstance();
                dbInstance.setId(newId());
                dbInstance.setTenantId(tenantId);
                dbInstance.setModuleCode(moduleCode);
                dbInstance.setInstanceName(host);
                dbInstance.setUserName(databaseName);
                dbInstance.setDatabaseName(databaseName);
                dbInstance.setPassword(password);
                dbInstance.setVersion(version);
                dbInstance.setCreateBy(userId);
                dbInstance.setCreateTime(now);
                tenantDbInstanceList.add(dbInstance);
            });

            // 批量保存 数据库实例信息
            tenantDbInstanceService.saveBatch(tenantDbInstanceList);
            // 组装请求数据库配置部分
            tenantDeploymentDataBaseDTO.setDb(new DeploymentModuleDbDTO().setVersion(version).setServices(modules));
        } else {
            List<TenantDbInstance> dbInstanceList = tenantDbInstanceService.getDbInstanceList(tenantId);
            Map<String, ServiceDbConfig> services = new HashMap<>(dbInstanceList.size());

            Set<String> moduleCodeSet = dbInstanceList.stream().map(TenantDbInstance::getModuleCode)
                .collect(Collectors.toSet());
            // 数据库中获取配置,重新发起请求
            for (TenantDbInstance dbConfig : dbInstanceList) {
                ServiceDbConfig serviceDbConfig = new ServiceDbConfig()
                    .setPort("3306")
                    .setHost(dbConfig.getInstanceName())
                    .setPassword(dbConfig.getPassword())
                    .setName(dbConfig.getDatabaseName())
                    .setUser(dbConfig.getUserName());
                services.put(dbConfig.getModuleCode(), serviceDbConfig);
            }

            // 应该获取租户 tenant_module 的对应枚举
            List<TenantModuleDTO> allModules = tenantModuleService.findAllModules();
            if (CollectionUtils.isEmpty(allModules)) {
                throw new BusinessException(TenantErrorNoEnum.EMPTY_MODULE);
            }
            // 判断更新的话新增模块
            List<TenantModuleDTO> addModule = allModules.stream()
                .filter(item -> !moduleCodeSet.contains(item.getModuleCode())).collect(Collectors.toList());
            addModule.forEach(module -> {
                ServiceDbConfig moduleDTO = new ServiceDbConfig();
                String host = dbHost;
                // 数据库名称 = 模块名 + （唯一）组织编码 (数据库账号和数据库名称命名规则相同所以共用一个变量)
                String moduleCode = module.getModuleCode();
                String databaseName = moduleCode + "_" + customCode;
                String password = generateRandomPassword(8);

                moduleDTO.setHost(host)
                    .setUser(databaseName)
                    .setPassword(password)
                    .setName(databaseName)
                    .setPort("3306");

                //组装 保存用的 数据库实例信息
                TenantDbInstance dbInstance = new TenantDbInstance();
                dbInstance.setId(newId());
                dbInstance.setTenantId(tenantId);
                dbInstance.setModuleCode(moduleCode);
                dbInstance.setInstanceName(host);
                dbInstance.setUserName(databaseName);
                dbInstance.setDatabaseName(databaseName);
                dbInstance.setPassword(password);
                dbInstance.setVersion(version);
                dbInstance.setCreateBy(userId);
                dbInstance.setCreateTime(now);
                tenantDbInstanceList.add(dbInstance);

                // 组装数据到 json
                ServiceDbConfig serviceDbConfig = new ServiceDbConfig()
                    .setPort("3306")
                    .setHost(dbInstance.getInstanceName())
                    .setPassword(dbInstance.getPassword())
                    .setName(dbInstance.getDatabaseName())
                    .setUser(dbInstance.getUserName());
                services.put(dbInstance.getModuleCode(), serviceDbConfig);
            });

            // 批量保存 数据库实例信息
            tenantDbInstanceService.saveBatch(tenantDbInstanceList);

            // 组装请求数据库配置部分
            tenantDeploymentDataBaseDTO.setDb(new DeploymentModuleDbDTO().setVersion(version).setServices(services));

            // 返回容器id
            containerId = tenantDeploymentStatus.getContainerId();
        }

        return containerId;
    }


    /**
     * 设置租户其它配置信息
     *
     * @param tenant                      租户
     * @param tenantDeploymentDataBaseDTO 租户部署数据库dto
     */
    private void setTenantOtherConfigInfo(Tenant tenant, TenantDeploymentDataBaseDTO tenantDeploymentDataBaseDTO) {
        String tenantId = tenant.getId();
        TenantConfigDTO configs = tenantConfigService.findConfigs(tenantId);
        // 组装租户其他配置信息
        tenantDeploymentDataBaseDTO.setSlug(tenant.getCustomCode())
//            .setName(tenant.getApplicationPlatformName())
            .setDomain(tenant.getCustomCode())
            .setAdmin(tenant.getLoginName())
            // 进行MD5运算,初始化到运维系统
            .setPassword(MD5Util.md5Pwd(tenant.getPassword()))
            .setLogo(imagesService.getImagePathByTenant(tenantId).getUrl())
//            .setColor(tenant.getColor())
//            .setActiveColor(tenant.getBackgroundColor())
//            .setPrivacyPolicy(configs.getPrivacyPolicy())
            .setDownloadApp(configs.getDownloadApp())
            .setSmallAppSignin(configs.getSmallAppSignin())
            .setHideH5Title(configs.getHideH5Title())
        ;
//            .setLanguage(configs.getLanguage());

    }

    /**
     * 保存部署状态
     *
     * @param tenantId        租户id
     * @param userId          用户id
     * @param now             时间戳
     * @param responseContent 响应内容 部署id编号
     */
    private void saveDeployStatus(String tenantId, String userId, Date now, String responseContent) {
        ResponseContentDTO responseContentDTO = Objects.requireNonNull(
            JsonUtil.jsonToObj(responseContent, ResponseContentDTO.class));
        if (Objects.isNull(responseContentDTO.getId())) {
            throw new BusinessException(TenantErrorNoEnum.OPERATION_ERROR);
        }
        TenantDeploymentStatus status = new TenantDeploymentStatus();
        status.setId(newId());
        status.setTenantId(tenantId);
        status.setCreateBy(userId);
        status.setCreateTime(now);
        status.setStatus(DeploymentStatusEnum.DEPLOYING.getCode());
        status.setContainerId(String.valueOf(responseContentDTO.getId()));
        tenantDeploymentStatusService.saveOrUpdate(status,
            Wrappers.lambdaUpdate(TenantDeploymentStatus.class).eq(TenantDeploymentStatus::getTenantId, tenantId));
    }

    /**
     * 随机生成指定长度的 密码
     */
    private String generateRandomPassword(int len) {
        char[] charArray = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890~!@%^&*.?".toCharArray();
        StringBuilder sb = new StringBuilder();
        Random r = ThreadLocalRandom.current();
        for (int x = 0; x < len; ++x) {
            sb.append(charArray[r.nextInt(charArray.length)]);
        }
        return sb.toString();
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        if (tenantType == 1) {
            return;
        }
        resetBucket(null);
        resetDataSource(null);
    }

    @Override
    public PageInfo<DonatedCourseDTO> donatedCourseList(DonatedCourseListQueryDTO donatedCourseListQueryDTO) {
        return donateCourseService.donatedCourseList(donatedCourseListQueryDTO);
    }

    @Override
    public void donateCourse(SyncCourseDTO syncCourseDTO) {
        // 获取当前租户信息
        Tenant tenant = tenantService.getById(syncCourseDTO.getTenantId());
        if (tenant == null) {
            throw new BusinessException(TenantErrorNoEnum.TENANT_NOT_EXISTS);
        }
        donateCourseService.donateCourse(syncCourseDTO);
    }
}
