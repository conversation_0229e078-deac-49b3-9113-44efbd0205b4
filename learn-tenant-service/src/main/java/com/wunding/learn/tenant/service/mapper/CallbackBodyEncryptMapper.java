package com.wunding.learn.tenant.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.tenant.service.model.CallbackBodyEncryptModel;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 企业微信服务商代开发回调密文解密数据表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-03-20
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface CallbackBodyEncryptMapper extends BaseMapper<CallbackBodyEncryptModel> {

}
