<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.tenant.service.mapper.TenantPermissionConfigMapper">

    <!-- 开启二级缓存 -->
    <!--
    <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.tenant.service.mapper.TenantPermissionConfigMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.tenant.service.model.TenantPermissionConfig">
        <!-- @Table sys_tenant_permission_config-->
        <id column="pk_id" jdbcType="BIGINT" property="pkId"/>
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="level_path" jdbcType="VARCHAR" property="levelPath"/>
        <result column="level_path_name" jdbcType="VARCHAR" property="levelPathName"/>
        <result column="is_del" jdbcType="INTEGER" property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="feature_source_id" jdbcType="BIGINT" property="featureSourceId"/>
        <result column="module_id" jdbcType="BIGINT" property="moduleId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pk_id, id, code, parent_id, title, sort_no, level, type, level_path, level_path_name, is_del, create_by, create_time, update_by, update_time, feature_source_id, module_id
    </sql>
</mapper>
