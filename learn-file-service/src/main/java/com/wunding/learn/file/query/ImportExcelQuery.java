package com.wunding.learn.file.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 文件查询对象
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "FilePath", description = "文件路径")
public class ImportExcelQuery {

    @Schema(description = "TESTLIB 试卷库 QUELIB 考试库 EXERCISE 练习库")
    @NotBlank(message = "模板类型不能为空")
    private String code;

    @Schema(description = "文件路径")
    @NotBlank(message = "文件路径不能为空")
    private String excelFile;

}
