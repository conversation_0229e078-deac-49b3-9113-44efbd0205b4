package com.wunding.learn.file.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.file.model.CloudFile;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 云文件 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @since 2024-01-29
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface CloudFileMapper extends BaseMapper<CloudFile> {

}
