package com.wunding.learn.file.util;

import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.string.StringPool;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.CRC32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/11/05
 */
@Slf4j
public class ZipCompressorUtil {

    static final int BUFFER = 8192;

    private final File zipFile;

    public ZipCompressorUtil(String pathName) {
        zipFile = new File(pathName);
    }

    private int suffixCode = 1;

    private List<String> fileNameList = new ArrayList<>();


    public static void unzip(File srcFile, String destDirPath) {
        // 判断源文件是否存在
        if (!srcFile.exists()) {
            log.error(srcFile.getPath() + "所指文件不存在");
            throw new BusinessException(ErrorNoEnum.ERR_FILE_NOT_EXIST);
        }

        try (ZipFile zipFile = new ZipFile(srcFile)) {
            Enumeration<?> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
                // 如果是文件夹，就创建个文件夹
                if (entry.isDirectory()) {
                    String dirPath = destDirPath + StringPool.SLASH + entry.getName();
                    File dir = new File(dirPath);
                    dir.mkdirs();
                } else {
                    // 如果是文件，就先创建一个文件，然后用io流把内容copy过去
                    File targetFile = new File(destDirPath + StringPool.SLASH + entry.getName());
                    //父文件夹
                    File dir = new File(targetFile.getParent());
                    // 保证这个文件的父文件夹必须要存在
                    if (!dir.exists()) {
                        boolean mkdirs = dir.mkdirs();
                        log.info("mkdirs: {}, targetParentFilePath: {}, targetFilePath: {}",mkdirs, targetFile.getParentFile().getAbsolutePath(), targetFile.getAbsolutePath());
                    }
                    if (!targetFile.createNewFile()) {
                        log.error("创建文件失败");
                    }
                    // 将压缩文件内容写入到这个文件中
                    InputStream is = zipFile.getInputStream(entry);
                    try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                        int len;
                        byte[] buf = new byte[BUFFER];
                        while ((len = is.read(buf)) != -1) {
                            fos.write(buf, 0, len);
                        }
                    }
                    is.close();
                }
            }
        } catch (Exception e) {
            log.error("unzip error from ZipUtils", e);
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_UNZIP);
        }
    }

    public void compress(String... pathName) {
        try (FileOutputStream fileOutputStream = new FileOutputStream(zipFile);
            CheckedOutputStream cos = new CheckedOutputStream(fileOutputStream, new CRC32());
            ZipOutputStream out = new ZipOutputStream(cos)) {
            String basedir = "";
            for (String s : pathName) {
                compress(new File(s), out, basedir);
            }
        } catch (Exception e) {
            log.error("compress error from ZipUtils", e);
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_ZIP);
        }
    }

    public void compress(List<String> pathName) {
        compress(pathName.toArray(new String[0]));
    }

    public void compress(String srcPathName) {
        File file = new File(srcPathName);
        if (!file.exists()) {
            log.error(srcPathName + "不存在！");
            throw new BusinessException(ErrorNoEnum.ERR_FILE_NOT_EXIST);
        }

        try (FileOutputStream fileOutputStream = new FileOutputStream(zipFile);
            CheckedOutputStream cos = new CheckedOutputStream(fileOutputStream, new CRC32());
            ZipOutputStream out = new ZipOutputStream(cos)) {
            String basedir = "";
            compress(file, out, basedir);
        } catch (Exception e) {
            log.error("compress error from ZipUtils", e);
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_ZIP);
        }
    }

    private void compress(File file, ZipOutputStream out, String basedir) {
        // 判断是目录还是文件
        if (file.isDirectory()) {
            this.compressDirectory(file, out, basedir);
        } else {
            this.compressFile(file, out, basedir);
        }
    }

    /**
     * 压缩一个目录
     */
    private void compressDirectory(File dir, ZipOutputStream out, String basedir) {
        if (!dir.exists()) {
            return;
        }

        File[] files = dir.listFiles();
        // 递归
        for (File file : files) {
            compress(file, out, basedir + dir.getName() + "/");
        }
    }

    /**
     * 压缩一个文件
     */
    private void compressFile(File file, ZipOutputStream out, String basedir) {
        if (!file.exists()) {
            return;
        }

        try (FileInputStream fileInputStream = new FileInputStream(file);
            BufferedInputStream bis = new BufferedInputStream(fileInputStream)) {
            ZipEntry entry = null;
            String fileName = file.getName();
            // 打包下载时，处理打包项中存在同名的文件情况
            if (fileNameList.contains(fileName)) {
                log.info("this file name {} repeat", fileName);
                suffixCode++;
                entry = new ZipEntry(basedir + fileName + suffixCode);
                fileNameList.add(fileName + suffixCode);
            } else {
                entry = new ZipEntry(basedir + fileName);
                fileNameList.add(fileName);
            }

            out.putNextEntry(entry);
            int count;
            byte[] data = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                out.write(data, 0, count);
            }
        } catch (Exception e) {
            log.error("compressFile error from ZipUtils", e);
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_ZIP);
        }
    }

}


