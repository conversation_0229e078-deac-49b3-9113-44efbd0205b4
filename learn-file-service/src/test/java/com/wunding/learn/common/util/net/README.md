# FTPUtils 测试说明

本目录包含了 `FTPUtils` 类的完整测试套件，包括单元测试和集成测试。

## 测试文件说明

### 1. FTPUtilsSimpleTest.java
- **类型**: 单元测试
- **描述**: 使用 Mockito 模拟 FTPClient，测试 FTPUtils 的所有方法
- **特点**: 
  - 不需要真实的FTP服务器
  - 运行速度快
  - 覆盖所有方法的成功和失败场景
  - 验证方法调用和参数传递

### 2. FTPUtilsIntegrationTest.java
- **类型**: 集成测试
- **描述**: 使用真实的FTP服务器测试完整的工作流程
- **特点**:
  - 默认被 `@Disabled` 注解禁用
  - 需要真实的FTP服务器环境
  - 测试真实的文件上传、下载、删除等操作

## 运行测试

### 运行单元测试

单元测试可以直接运行，不需要任何外部依赖：

```bash
# 运行所有单元测试
mvn test -Dtest=FTPUtilsSimpleTest

# 运行特定的测试方法
mvn test -Dtest=FTPUtilsSimpleTest#testConnect_Success
```

### 运行集成测试

集成测试需要真实的FTP服务器。要运行集成测试：

1. **设置FTP服务器**
   - 安装并启动FTP服务器（如 vsftpd、FileZilla Server等）
   - 创建测试用户账号

2. **配置测试参数**
   
   方法一：修改 `FTPUtilsIntegrationTest.java` 中的常量：
   ```java
   private static final String FTP_SERVER = "your-ftp-server";
   private static final int FTP_PORT = 21;
   private static final String FTP_USER = "your-username";
   private static final String FTP_PASSWORD = "your-password";
   ```

   方法二：使用系统属性：
   ```bash
   mvn test -Dtest=FTPUtilsIntegrationTest -Dftp.test.enabled=true
   ```

3. **移除 @Disabled 注解**
   
   在 `FTPUtilsIntegrationTest.java` 中注释掉或删除：
   ```java
   @Disabled("需要真实的FTP服务器才能运行集成测试")
   ```

4. **运行集成测试**
   ```bash
   mvn test -Dtest=FTPUtilsIntegrationTest
   ```

## 测试覆盖的功能

### 单元测试覆盖
- ✅ 连接FTP服务器（成功/失败）
- ✅ 断开FTP连接
- ✅ 文件上传（成功/失败）
- ✅ 文件下载（成功/失败）
- ✅ 文件删除（成功/失败）
- ✅ 列出文件（有文件/空目录）
- ✅ 切换工作目录（成功/失败）
- ✅ 创建目录（成功/失败）
- ✅ 删除目录（成功/失败）
- ✅ 重命名文件（成功/失败）
- ✅ 检查文件存在性

### 集成测试覆盖
- ✅ 完整的工作流程（连接→上传→下载→删除）
- ✅ 连接失败场景
- ✅ 错误凭据场景
- ✅ 文件不存在场景
- ✅ 目录操作场景
- ✅ 边界条件测试

## 使用Docker快速搭建FTP测试环境

如果需要快速搭建FTP测试环境，可以使用Docker：

```bash
# 启动FTP服务器
docker run -p 20:20 -p 21:21 -p 20000:20000 
# 可选参数
#-v G:\docker\ftp:/home/<USER>
#-v G:\docker\ftp:/var/log/vsftpd 
-e FTP_USER=admin 
-e FTP_PASS=admin 
-e PASV_MIN_PORT=20000 
-e PASV_MAX_PORT=20000 
-e PASV_ADDRESS=宿主机ip 例如:*************
-e LOG_STDOUT=1 
--name ftp fauria/vsftpd

# 停止并删除容器
docker stop ftp && docker rm ftp
```

## 注意事项

1. **文件路径**: 测试中使用的文件路径应该根据实际FTP服务器配置进行调整
2. **权限**: 确保测试用户有足够的权限进行文件和目录操作
3. **防火墙**: 确保FTP端口（通常是21）和被动模式端口范围是开放的
4. **清理**: 集成测试会自动清理创建的测试文件和目录
5. **并发**: 避免同时运行多个集成测试实例，可能会产生冲突

## 测试最佳实践

1. **优先运行单元测试**: 单元测试运行快，应该作为主要的测试手段
2. **集成测试作为补充**: 集成测试用于验证与真实FTP服务器的交互
3. **持续集成**: 在CI/CD流水线中只运行单元测试，集成测试可以在特定环境中手动运行
4. **测试隔离**: 每个测试方法应该是独立的，不依赖其他测试的执行结果
