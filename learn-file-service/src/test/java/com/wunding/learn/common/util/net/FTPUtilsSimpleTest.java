package com.wunding.learn.common.util.net;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * FTPUtils简化单元测试类 专注于测试FTPClient的方法调用和返回值处理逻辑
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class FTPUtilsSimpleTest {

    @Mock
    private FTPClient mockFtpClient;

    private FTPUtils ftpUtils;

    private static final String TEST_SERVER = "test.ftp.server";
    private static final int TEST_PORT = 21;
    private static final String TEST_USER = "testuser";
    private static final String TEST_PASSWORD = "testpass";

    @BeforeEach
    void setUp() {
        ftpUtils = new FTPUtils(mockFtpClient, TEST_SERVER, TEST_PORT, TEST_USER, TEST_PASSWORD);
    }

    @Test
    void testConnect_Success() throws IOException {
        // Given
        when(mockFtpClient.getReplyCode()).thenReturn(220); // 220 is a positive completion code
        when(mockFtpClient.login(TEST_USER, TEST_PASSWORD)).thenReturn(true);

        // When
        ftpUtils.connect();

        // Then
        verify(mockFtpClient).connect(TEST_SERVER, TEST_PORT);
        verify(mockFtpClient).login(TEST_USER, TEST_PASSWORD);
        verify(mockFtpClient).enterLocalPassiveMode();
    }

    @Test
    void testConnect_FailedConnection() throws IOException {
        // Given
        when(mockFtpClient.getReplyCode()).thenReturn(421); // 421 is not a positive completion code

        // When & Then
        IOException exception = assertThrows(IOException.class, () -> ftpUtils.connect());
        assertTrue(exception.getMessage().contains("无法连接到FTP服务器"));

        verify(mockFtpClient).connect(TEST_SERVER, TEST_PORT);
        verify(mockFtpClient, never()).login(anyString(), anyString());
    }

    @Test
    void testConnect_FailedLogin() throws IOException {
        // Given
        when(mockFtpClient.getReplyCode()).thenReturn(220);
        when(mockFtpClient.login(TEST_USER, TEST_PASSWORD)).thenReturn(false);

        // When & Then
        IOException exception = assertThrows(IOException.class, () -> ftpUtils.connect());
        assertTrue(exception.getMessage().contains("FTP登录失败"));

        verify(mockFtpClient).connect(TEST_SERVER, TEST_PORT);
        verify(mockFtpClient).login(TEST_USER, TEST_PASSWORD);
        verify(mockFtpClient, never()).enterLocalPassiveMode();
    }

    @Test
    void testDisconnect_WhenConnected() throws IOException {
        // Given
        when(mockFtpClient.isConnected()).thenReturn(true);

        // When
        ftpUtils.disconnect();

        // Then
        verify(mockFtpClient).logout();
        verify(mockFtpClient).disconnect();
    }

    @Test
    void testDisconnect_WhenNotConnected() throws IOException {
        // Given
        when(mockFtpClient.isConnected()).thenReturn(false);

        // When
        ftpUtils.disconnect();

        // Then
        verify(mockFtpClient, never()).logout();
        verify(mockFtpClient, never()).disconnect();
    }

    @Test
    void testDeleteFile_Success() throws IOException {
        // Given
        String remoteFilePath = "/remote/test-file.txt";
        when(mockFtpClient.deleteFile(remoteFilePath)).thenReturn(true);

        // When
        boolean result = ftpUtils.deleteFile(remoteFilePath);

        // Then
        assertTrue(result);
        verify(mockFtpClient).deleteFile(remoteFilePath);
    }

    @Test
    void testDeleteFile_Failure() throws IOException {
        // Given
        String remoteFilePath = "/remote/test-file.txt";
        when(mockFtpClient.deleteFile(remoteFilePath)).thenReturn(false);

        // When
        boolean result = ftpUtils.deleteFile(remoteFilePath);

        // Then
        assertFalse(result);
        verify(mockFtpClient).deleteFile(remoteFilePath);
    }

    @Test
    void testListFiles_Success() throws IOException {
        // Given
        String remoteDirPath = "/remote/dir";
        FTPFile[] expectedFiles = {new FTPFile(), new FTPFile()};
        when(mockFtpClient.listFiles(remoteDirPath)).thenReturn(expectedFiles);

        // When
        FTPFile[] result = ftpUtils.listFiles(remoteDirPath);

        // Then
        assertArrayEquals(expectedFiles, result);
        verify(mockFtpClient).listFiles(remoteDirPath);
    }

    @Test
    void testListFiles_EmptyDirectory() throws IOException {
        // Given
        String remoteDirPath = "/remote/empty-dir";
        FTPFile[] expectedFiles = {};
        when(mockFtpClient.listFiles(remoteDirPath)).thenReturn(expectedFiles);

        // When
        FTPFile[] result = ftpUtils.listFiles(remoteDirPath);

        // Then
        assertArrayEquals(expectedFiles, result);
        verify(mockFtpClient).listFiles(remoteDirPath);
    }

    @Test
    void testChangeWorkingDirectory_Success() throws IOException {
        // Given
        String remoteDirPath = "/remote/dir";
        when(mockFtpClient.changeWorkingDirectory(remoteDirPath)).thenReturn(true);

        // When
        boolean result = ftpUtils.changeWorkingDirectory(remoteDirPath);

        // Then
        assertTrue(result);
        verify(mockFtpClient).changeWorkingDirectory(remoteDirPath);
    }

    @Test
    void testChangeWorkingDirectory_Failure() throws IOException {
        // Given
        String remoteDirPath = "/remote/nonexistent-dir";
        when(mockFtpClient.changeWorkingDirectory(remoteDirPath)).thenReturn(false);

        // When
        boolean result = ftpUtils.changeWorkingDirectory(remoteDirPath);

        // Then
        assertFalse(result);
        verify(mockFtpClient).changeWorkingDirectory(remoteDirPath);
    }

    @Test
    void testMakeDirectory_Success() throws IOException {
        // Given
        String remoteDirPath = "/remote/new-dir";
        when(mockFtpClient.makeDirectory(remoteDirPath)).thenReturn(true);

        // When
        boolean result = ftpUtils.makeDirectory(remoteDirPath);

        // Then
        assertTrue(result);
        verify(mockFtpClient).makeDirectory(remoteDirPath);
    }

    @Test
    void testMakeDirectory_Failure() throws IOException {
        // Given
        String remoteDirPath = "/remote/existing-dir";
        when(mockFtpClient.makeDirectory(remoteDirPath)).thenReturn(false);

        // When
        boolean result = ftpUtils.makeDirectory(remoteDirPath);

        // Then
        assertFalse(result);
        verify(mockFtpClient).makeDirectory(remoteDirPath);
    }

    @Test
    void testRemoveDirectory_Success() throws IOException {
        // Given
        String remoteDirPath = "/remote/dir-to-remove";
        when(mockFtpClient.removeDirectory(remoteDirPath)).thenReturn(true);

        // When
        boolean result = ftpUtils.removeDirectory(remoteDirPath);

        // Then
        assertTrue(result);
        verify(mockFtpClient).removeDirectory(remoteDirPath);
    }

    @Test
    void testRemoveDirectory_Failure() throws IOException {
        // Given
        String remoteDirPath = "/remote/nonexistent-dir";
        when(mockFtpClient.removeDirectory(remoteDirPath)).thenReturn(false);

        // When
        boolean result = ftpUtils.removeDirectory(remoteDirPath);

        // Then
        assertFalse(result);
        verify(mockFtpClient).removeDirectory(remoteDirPath);
    }

    @Test
    void testRenameFile_Success() throws IOException {
        // Given
        String oldRemotePath = "/remote/old-file.txt";
        String newRemotePath = "/remote/new-file.txt";
        when(mockFtpClient.rename(oldRemotePath, newRemotePath)).thenReturn(true);

        // When
        boolean result = ftpUtils.renameFile(oldRemotePath, newRemotePath);

        // Then
        assertTrue(result);
        verify(mockFtpClient).rename(oldRemotePath, newRemotePath);
    }

    @Test
    void testRenameFile_Failure() throws IOException {
        // Given
        String oldRemotePath = "/remote/nonexistent-file.txt";
        String newRemotePath = "/remote/new-file.txt";
        when(mockFtpClient.rename(oldRemotePath, newRemotePath)).thenReturn(false);

        // When
        boolean result = ftpUtils.renameFile(oldRemotePath, newRemotePath);

        // Then
        assertFalse(result);
        verify(mockFtpClient).rename(oldRemotePath, newRemotePath);
    }

    @Test
    void testExists_FileExists() throws IOException {
        // Given
        String remotePath = "/remote/existing-file.txt";
        FTPFile[] files = {new FTPFile()};
        when(mockFtpClient.listFiles(remotePath)).thenReturn(files);

        // When
        boolean result = ftpUtils.exists(remotePath);

        // Then
        assertTrue(result);
        verify(mockFtpClient).listFiles(remotePath);
    }

    @Test
    void testExists_FileNotExists() throws IOException {
        // Given
        String remotePath = "/remote/nonexistent-file.txt";
        FTPFile[] files = {};
        when(mockFtpClient.listFiles(remotePath)).thenReturn(files);

        // When
        boolean result = ftpUtils.exists(remotePath);

        // Then
        assertFalse(result);
        verify(mockFtpClient).listFiles(remotePath);
    }

    @Test
    void testExists_NullFiles() throws IOException {
        // Given
        String remotePath = "/remote/test-file.txt";
        when(mockFtpClient.listFiles(remotePath)).thenReturn(new FTPFile[]{});

        // When
        boolean result = ftpUtils.exists(remotePath);

        // Then
        assertFalse(result);
        verify(mockFtpClient).listFiles(remotePath);
    }

    @Test
    void testConnect_WithPositiveCompletionReplyCode() throws IOException {
        // Given - 测试不同的正面完成代码
        when(mockFtpClient.getReplyCode()).thenReturn(200); // 另一个正面完成代码
        when(mockFtpClient.login(TEST_USER, TEST_PASSWORD)).thenReturn(true);

        // When
        ftpUtils.connect();

        // Then
        verify(mockFtpClient).connect(TEST_SERVER, TEST_PORT);
        verify(mockFtpClient).login(TEST_USER, TEST_PASSWORD);
        verify(mockFtpClient).enterLocalPassiveMode();
    }

    @Test
    void testListFiles_NullReturn() throws IOException {
        // Given
        String remoteDirPath = "/remote/dir";
        when(mockFtpClient.listFiles(remoteDirPath)).thenReturn(null);

        // When
        FTPFile[] result = ftpUtils.listFiles(remoteDirPath);

        // Then
        assertNull(result);
        verify(mockFtpClient).listFiles(remoteDirPath);
    }
}
