package com.wunding.learn.common.util.net;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

/**
 * FTPUtils集成测试类
 * <p>
 * 注意：这些测试需要真实的FTP服务器，默认被禁用。 要运行这些测试，请： 1. 设置真实的FTP服务器连接信息 2. 移除@Disabled注解 3. 确保FTP服务器可访问
 *
 * <AUTHOR>
 */
@Disabled("需要真实的FTP服务器才能运行集成测试")
class FTPUtilsIntegrationTest {

    // 请根据实际情况修改这些配置
    private static final String FTP_SERVER = "192.168.0.123";
    private static final int FTP_PORT = 21;
    private static final String FTP_USER = "admin";
    private static final String FTP_PASSWORD = "admin";

    private FTPUtils ftpUtils;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // 检查是否有FTP服务器配置
        FTPClient ftpClient = new FTPClient();
        ftpUtils = new FTPUtils(ftpClient, FTP_SERVER, FTP_PORT, FTP_USER, FTP_PASSWORD);
    }

    @AfterEach
    void tearDown() throws IOException {
        if (ftpUtils != null) {
            ftpUtils.disconnect();
        }
    }


    @Test
    void testFullWorkflow() throws IOException {
        // 连接到FTP服务器
        ftpUtils.connect();

        // 创建测试文件
        Path testFile = tempDir.resolve("test-upload.txt");
        try (FileWriter writer = new FileWriter(testFile.toFile())) {
            writer.write("这是一个测试文件内容\nTest file content");
        }

        String remoteFilePath = "/test-upload.txt";
        String remoteDir = "/test-dir";
        String renamedFilePath = "/test-renamed.txt";

        try {
            // 测试上传文件
            boolean uploadResult = ftpUtils.uploadFile(testFile.toString(), remoteFilePath);
            assertTrue(uploadResult, "文件上传应该成功");

            // 测试文件是否存在
            boolean exists = ftpUtils.exists(remoteFilePath);
            assertTrue(exists, "上传的文件应该存在");

            // 测试创建目录
            boolean dirCreated = ftpUtils.makeDirectory(remoteDir);
            assertTrue(dirCreated, "目录创建应该成功");

            // 测试切换工作目录
            boolean dirChanged = ftpUtils.changeWorkingDirectory(remoteDir);
            assertTrue(dirChanged, "切换目录应该成功");

            // 切换回根目录
            ftpUtils.changeWorkingDirectory("/");

            // 测试重命名文件
            boolean renamed = ftpUtils.renameFile(remoteFilePath, renamedFilePath);
            assertTrue(renamed, "文件重命名应该成功");

            // 测试下载文件
            Path downloadFile = tempDir.resolve("test-download.txt");
            boolean downloadResult = ftpUtils.downloadFile(renamedFilePath, downloadFile.toString());
            assertTrue(downloadResult, "文件下载应该成功");

            // 验证下载的文件内容
            String downloadedContent = Files.readString(downloadFile);
            String originalContent = Files.readString(testFile);
            assertEquals(originalContent, downloadedContent, "下载的文件内容应该与原文件相同");

            // 测试列出文件
            FTPFile[] files = ftpUtils.listFiles("/");
            assertNotNull(files, "文件列表不应该为null");

            // 查找我们上传的文件
            boolean foundFile = false;
            for (FTPFile file : files) {
                if (file.getName().equals("test-renamed.txt")) {
                    foundFile = true;
                    break;
                }
            }
            assertTrue(foundFile, "应该能在文件列表中找到重命名后的文件");

        } finally {
            // 清理：删除测试文件和目录
            ftpUtils.deleteFile(renamedFilePath);
            ftpUtils.removeDirectory(remoteDir);
        }
    }

    @Test
    void testConnectionFailure() {
        // 测试连接到不存在的服务器
        FTPClient ftpClient = new FTPClient();
        FTPUtils badFtpUtils = new FTPUtils(ftpClient, "nonexistent.server", 21, "user", "pass");

        assertThrows(IOException.class, badFtpUtils::connect, "连接到不存在的服务器应该抛出异常");
    }

    @Test
    void testInvalidCredentials() throws IOException {
        // 测试错误的登录凭据
        FTPClient ftpClient = new FTPClient();
        FTPUtils badFtpUtils = new FTPUtils(ftpClient, FTP_SERVER, FTP_PORT, "wronguser", "wrongpass");

        assertThrows(IOException.class, badFtpUtils::connect, "错误的登录凭据应该抛出异常");
    }

    @Test
    void testUploadNonExistentFile() throws IOException {
        ftpUtils.connect();

        String nonExistentFile = "/Users/<USER>/data/console11.log";
        String remoteFilePath = "/test-nonexistent.log";

        assertThrows(IOException.class,
            () -> ftpUtils.uploadFile(nonExistentFile, remoteFilePath),
            "上传不存在的文件应该抛出异常");
    }

    @Test
    void testDownloadNonExistentFile() throws IOException {
        ftpUtils.connect();

        String nonExistentRemoteFile = "/path/to/nonexistent/remote-file.txt";
        Path localFile = tempDir.resolve("download-test.txt");

        boolean result = ftpUtils.downloadFile(nonExistentRemoteFile, localFile.toString());
        assertFalse(result, "下载不存在的文件应该返回false");
    }

    @Test
    void testDeleteNonExistentFile() throws IOException {
        ftpUtils.connect();

        String nonExistentFile = "/path/to/nonexistent/file.txt";

        boolean result = ftpUtils.deleteFile(nonExistentFile);
        assertFalse(result, "删除不存在的文件应该返回false");
    }

    @Test
    void testCreateExistingDirectory() throws IOException {
        ftpUtils.connect();

        String testDir = "/test-existing-dir";

        try {
            // 首先创建目录
            boolean created = ftpUtils.makeDirectory(testDir);
            assertTrue(created, "第一次创建目录应该成功");

            // 尝试再次创建相同的目录
            boolean createdAgain = ftpUtils.makeDirectory(testDir);
            assertFalse(createdAgain, "创建已存在的目录应该返回false");

        } finally {
            // 清理
            ftpUtils.removeDirectory(testDir);
        }
    }

    @Test
    void testRemoveNonEmptyDirectory() throws IOException {
        ftpUtils.connect();

        String testDir = "/test-non-empty-dir";
        String testFile = testDir + "/test-file.txt";

        try {
            // 创建目录
            ftpUtils.makeDirectory(testDir);

            // 在目录中创建文件
            Path localFile = tempDir.resolve("temp-file.txt");
            Files.writeString(localFile, "test content");
            ftpUtils.uploadFile(localFile.toString(), testFile);

            // 尝试删除非空目录
            boolean removed = ftpUtils.removeDirectory(testDir);
            assertFalse(removed, "删除非空目录应该返回false");

        } finally {
            // 清理
            ftpUtils.deleteFile(testFile);
            ftpUtils.removeDirectory(testDir);
        }
    }

    @Test
    void testListEmptyDirectory() throws IOException {
        ftpUtils.connect();

        String emptyDir = "/test-empty-dir";

        try {
            // 创建空目录
            ftpUtils.makeDirectory(emptyDir);

            // 列出空目录的内容
            FTPFile[] files = ftpUtils.listFiles(emptyDir);
            assertNotNull(files, "文件列表不应该为null");
            assertEquals(0, files.length, "空目录应该没有文件");

        } finally {
            // 清理
            ftpUtils.removeDirectory(emptyDir);
        }
    }

    @Test
    void testChangeToNonExistentDirectory() throws IOException {
        ftpUtils.connect();

        String nonExistentDir = "/path/to/nonexistent/directory";

        boolean result = ftpUtils.changeWorkingDirectory(nonExistentDir);
        assertFalse(result, "切换到不存在的目录应该返回false");
    }
}
