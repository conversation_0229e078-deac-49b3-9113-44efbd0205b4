<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>learn</artifactId>
        <groupId>com.wunding</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>learn-excitation</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>learn-excitation-api</module>
        <module>learn-excitation-service</module>
    </modules>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>



    <distributionManagement>
        <repository>
            <!--nexus服务器中用户名：在settings.xml中和<server>的id一致-->
            <id>releases</id>
            <!--自定义名称-->
            <name>releases</name>
            <!--仓库地址-->
            <url>http://192.168.0.100:8081/nexus/content/repositories/releases/</url>
        </repository>
        <!--快照版本仓库-->
        <snapshotRepository>
            <!--nexus服务器中用户名：在settings.xml中和<server>的id一致-->
            <id>snapshots</id>
            <!--自定义名称-->
            <name>snapshots</name>
            <!--仓库地址-->
            <url>http://192.168.0.100:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
