<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wunding</groupId>
        <artifactId>learn</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>learn-operation</artifactId>
    <name>learn-operation</name>
    <packaging>pom</packaging>
    <description>learn-operation</description>

    <modules>
        <module>learn-operation-api</module>
        <module>learn-operation-service</module>
    </modules>



    <distributionManagement>
        <repository>
            <!--nexus服务器中用户名：在settings.xml中和<server>的id一致-->
            <id>releases</id>
            <!--自定义名称-->
            <name>releases</name>
            <!--仓库地址-->
            <url>http://*************:8081/nexus/content/repositories/releases/</url>
        </repository>
        <!--快照版本仓库-->
        <snapshotRepository>
            <!--nexus服务器中用户名：在settings.xml中和<server>的id一致-->
            <id>snapshots</id>
            <!--自定义名称-->
            <name>snapshots</name>
            <!--仓库地址-->
            <url>http://*************:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
