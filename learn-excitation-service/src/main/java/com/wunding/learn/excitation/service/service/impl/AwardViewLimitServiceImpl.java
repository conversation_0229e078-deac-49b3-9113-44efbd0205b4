package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.AwardViewLimitMapper;
import com.wunding.learn.excitation.service.model.AwardViewLimit;
import com.wunding.learn.excitation.service.service.IAwardViewLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 奖品访问权限表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
@Slf4j
@Service("awardViewLimitService")
public class AwardViewLimitServiceImpl extends ServiceImpl<AwardViewLimitMapper, AwardViewLimit> implements IAwardViewLimitService {

}
