package com.wunding.learn.excitation.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/11/9 14:38
 */
@Data
@Schema(name = "ExcitationExplainItemDTO", description = "激励明细对象")
public class ExcitationExplainItemDTO implements Serializable {

    private static final long serialVersionUID = 5411000951665265018L;

    @Schema(description = "记录id")
    private String id;

    @Schema(description = "激励类型")
    private String type;

    @Schema(description = "值（激励数量）")
    private String score;

    @Schema(description = "阈值区间")
    private String range;

    @Schema(description = "内容上限")
    private String bound;

    @Schema(description = "每日上限")
    private String dayLimit;
}
