package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.excitation.service.mapper.GameOptionMapper;
import com.wunding.learn.excitation.service.model.GameOption;
import com.wunding.learn.excitation.service.service.IGameOptionService;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>  服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
    * @since 2022-08-08
 */
@Slf4j
@Service("gameOptionService")
public class GameOptionServiceImpl extends ServiceImpl<GameOptionMapper, GameOption> implements IGameOptionService {

    @Override
    public void updateAwardOptions(String optionId) {
        GameOption byId = getById(optionId);
        LambdaUpdateWrapper<GameOption> query=new LambdaUpdateWrapper<>();
        query.set(GameOption::getCount,byId.getCount().subtract(new BigDecimal(1)));
        query.eq(GameOption::getId,optionId);
        update(query);
    }
}
