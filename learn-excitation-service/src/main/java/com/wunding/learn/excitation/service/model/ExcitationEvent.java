package com.wunding.learn.excitation.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 激励事件表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("excitation_event")
@Schema(name = "ExcitationEvent对象", description = "激励事件表")
public class ExcitationEvent implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 事件名字
     */
    @Schema(description = "事件名字")
    @TableField("name")
    private String name;


    /**
     * 事件类型:0-事件;1-计数;2-其他
     */
    @Schema(description = "事件类型:0-事件;1-计数;2-其他")
    @TableField("type")
    private Integer type;


    /**
     * 事件对象类别:比如课程(course)等
     */
    @Schema(description = "事件对象类别:比如课程(course)等")
    @TableField("category")
    private String category;


    /**
     * 事件详细说明
     */
    @Schema(description = "事件详细说明")
    @TableField("intro")
    private String intro;


    /**
     * 是否允许配置 0否 1是
     */
    @Schema(description = "是否允许配置 0否 1是")
    @TableField("is_enable_config")
    private Integer isEnableConfig;


    /**
     * 是否固定每内容限制 0否 1是
     */
    @Schema(description = "是否固定每内容限制 0否 1是")
    @TableField("is_fixed")
    private Integer isFixed;


    /**
     * 每内容固定限制值
     */
    @Schema(description = "每内容固定限制值")
    @TableField("fixed_value")
    private Long fixedValue;


    /**
     * 0-未删除;1-已删除
     */
    @Schema(description = "0-未删除;1-已删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 添加人
     */
    @Schema(description = "添加人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 添加时间
     */
    @Schema(description = "添加时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
}
