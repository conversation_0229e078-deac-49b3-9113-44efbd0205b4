package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.excitation.service.admin.dto.ExcitationConfigUserPageDTO;
import com.wunding.learn.excitation.service.admin.dto.ExcitationConfigUserSaveDTO;
import com.wunding.learn.excitation.service.admin.query.ExcitationConfigUserQuery;
import com.wunding.learn.excitation.service.model.ExcitationConfigUser;
import java.util.List;

/**
 * <p> 用户激励配置表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IExcitationConfigUserService extends IService<ExcitationConfigUser> {

    /**
     * 获取用户自定义激励配置数据列表
     *
     * @param query
     * @return
     */
    List<ExcitationConfigUserPageDTO> getDataList(ExcitationConfigUserQuery query);

    /**
     * 保存配置
     *
     * @param saveDTO
     */
    void saveData(ExcitationConfigUserSaveDTO saveDTO);

    /**
     * 更新配置
     *
     * @param saveDTO 配置信息
     * @param id      配置ID
     */
    void modifyData(String id, ExcitationConfigUserSaveDTO saveDTO);

    /**
     * 删除数据
     *
     * @param ids
     */
    void deleteData(String ids);

    /**
     * 删除数据
     *
     * @param id
     */
    void deleteDataById(String id);

    /**
     * 初始化资源激励配置
     *
     * @param initDTO
     */
    void initResourceConfigUser(ResourceConfigInitDTO initDTO);

    /**
     * 修改激励规则的可兑换属性
     *
     * @param id
     * @param isExchange
     */
    void exchangeData(String id, Integer isExchange);
}
