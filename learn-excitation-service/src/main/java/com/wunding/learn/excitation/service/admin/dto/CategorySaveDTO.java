package com.wunding.learn.excitation.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/10/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "CategorySaveDTO", description = "分类数据保存数据对象")
public class CategorySaveDTO extends CategoryBaseDTO {

    /**
     * 分类名称
     */
    @Schema(description = "分类id,供操作日志获取id使用", hidden = true)
    private String id;

    /**
     * 上级分类
     */
    @Schema(description = "上级分类ID")
    private String parentId;

    @Schema(description = "分类类型(奖品分类:AwardCate)")
    @NotBlank(message = "分类类型不能为空")
    private String categoryType;
}
