package com.wunding.learn.excitation.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/17
 */
@Data
@Schema(name = "ImportIntegralDTO", description = "导入积分数据对象")
public class ImportIntegralDTO {

    @Schema(description = "文件路径")
    @NotBlank(message = "导入文件不能为空")
    private String filePath;
}
