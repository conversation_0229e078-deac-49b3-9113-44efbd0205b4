package com.wunding.learn.common.dto.pay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/21
 */
@Data
@Schema(description = "提交订单数据对象", name = "SubmitOrderDTO")
public class SubmitOrderDTO {

    @Schema(description = "购买商品数量", required = true)
    private Integer goodsNum;

    @Schema(description = "购买商品id goldCoin-金币", required = true)
    private String goodsId;
}
