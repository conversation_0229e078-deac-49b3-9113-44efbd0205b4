package com.wunding.learn.train.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p> 用户操作记录完成查询
 *
 * <AUTHOR> @since
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "LearnMapUserOperationResourcesPageQuery", description = "用户操作记录完成查询")
public class LearnMapUserOperationResourcesPageQuery extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "类型 0- 初训 1-复训")
    private Integer type;

    @Schema(description = "地图id")
    private String mapId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "能力id")
    private String abilityId;
}
