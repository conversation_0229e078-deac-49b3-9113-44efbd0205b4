package com.wunding.learn.train.service.client.dto;

import com.wunding.learn.plan.api.service.dto.PlanInventoryFeignDTO;
import com.wunding.learn.train.service.admin.dto.TemplateColumnDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/10
 */
@Data
@Schema(name="FormDetailDTO",description = "表单详情")
public class FormDetailDTO extends PlanInventoryFeignDTO {

    @Schema(description = "账号")
    private String userNo;

    @Schema(description = "表单配置")
    private List<TemplateColumnDTO> configTemplateColumn;

}
