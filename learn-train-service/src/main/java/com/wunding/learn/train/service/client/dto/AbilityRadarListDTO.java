package com.wunding.learn.train.service.client.dto;

import com.wunding.learn.user.api.dto.AbilityLevelListDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 能力画像能力返回对象DTO
 *
 * <AUTHOR>
 * @date 2024/5/13 下午5:29
 */
@Data
@Schema(name = "AbilityRadarListDTO", description = "能力画像能力返回对象DTO")
public class AbilityRadarListDTO {

    @Schema(description = "能力id")
    private String abilityId;

    @Schema(description = "能力名称")
    private String abilityName;

    @Schema(description = "能力级别列表")
    private List<AbilityLevelListDTO> levelList;

}
