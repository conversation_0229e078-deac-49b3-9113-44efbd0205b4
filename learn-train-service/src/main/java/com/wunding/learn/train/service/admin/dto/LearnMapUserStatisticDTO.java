package com.wunding.learn.train.service.admin.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 地图学习进度学习形式数据对象
 *
 * <AUTHOR> @since
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "LearnMapUserStatisticDTO", description = "地图学习进度学习形式数据对象")
public class LearnMapUserStatisticDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联ID",hidden = true)
    private String askId;

    @Schema(description = "关联用户ID",hidden = true)
    private String userId;

    @Schema(description = "类型 0-初训 1-复训")
    private String type;

    @Schema(description = "学习形式id 1培训 2授课 3监督 4实操")
    private String categoryType;

    @Schema(description = "完成状态 0-未完成 1-已完成")
    private Integer status;

    @Schema(description = "完成时间")
    private Date finishDate;

    @Schema(description = "复训截至时间")
    private Date repeatEndDate;

}
