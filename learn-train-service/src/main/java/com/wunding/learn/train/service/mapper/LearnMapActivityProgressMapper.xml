<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.train.service.mapper.LearnMapActivityProgressMapper">
    <!-- 开启二级缓存 -->
    <!--
    <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.train.service.mapper.LearnMapActivityProgressMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.train.service.model.LearnMapActivityProgress">
        <!--@Table train_learn_map_activity_progress-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="map_id" jdbcType="VARCHAR"
          property="mapId"/>
        <result column="activity_id" jdbcType="VARCHAR"
          property="activityId"/>
        <result column="version_id" jdbcType="VARCHAR"
          property="versionId"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="status" jdbcType="TINYINT"
          property="status"/>
        <result column="start_time" jdbcType="TIMESTAMP"
          property="startTime"/>
        <result column="finish_time" jdbcType="TIMESTAMP"
          property="finishTime"/>
        <result column="course_time" jdbcType="BIGINT"
          property="courseTime"/>
        <result column="test_score" jdbcType="DECIMAL"
          property="testScore"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , map_id
        , activity_id
        , version_id
        , user_id
        , status
        , start_time
        , finish_time
        , course_time
        , test_score
        , is_del
        , create_by
        , create_time
        , update_by
        , update_time
    </sql>

    <select id="selectLearnDetailListByPage"
      parameterType="com.wunding.learn.train.service.admin.query.LearnMapLearnDetailQuery"
      resultType="com.wunding.learn.train.service.admin.dto.LearnMapLearnDetailDTO" useCache="false">
        select a.id,
               c.phase_name,
               b.resource_type,
               b.is_del,
               b.activity_name,
               a.user_id,
               a.status,
               a.course_time,
               a.test_score,
               u.org_id
        from train_learn_map_activity_progress a
            inner join sys_user u on a.user_id = u.id
            inner join train_learn_map_activity b on a.activity_id = b.id and b.version_id = #{params.versionId}
            left join train_learn_map_phase c on b.phase_id = c.id and c.version_id = #{params.versionId}
        <if test="params.execId != null and params.execId != ''">
            inner join w_view_limit_user vu on a.user_id = vu.user_id and vu.view_limit_id = #{params.programmeId}
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            inner join sys_org o on u.org_id = o.id
        </if>
        where a.map_id = #{params.mapId}
        <if test="params.activityName != null and params.activityName != ''">
            and instr(b.activity_name, #{params.activityName}) > 0
        </if>
        <if test="params.phaseId != null and params.phaseId != ''">
            and c.id = #{params.phaseId}
        </if>
        <if test="params.userIds != null and params.userIds.size() > 0">
            <foreach collection="params.userIds" item="item" open="and a.user_id in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            and o.level_path like concat(#{params.levelPath}, '%')
        </if>
        <if test="params.status != null">
            and a.status = #{params.status}
        </if>
        <if test="params.isDel != null">
            and b.is_del = #{params.isDel}
        </if>
        order by c.sort_no, b.sort_no, a.id
    </select>

    <select id="selectLearnUserListByPage"
      parameterType="com.wunding.learn.train.service.admin.query.LearnMapLearnUserQuery"
      resultType="com.wunding.learn.train.service.admin.dto.LearnMapLearnUserDTO" useCache="false">
        select u.id,
               u.full_name,
               u.login_name,
               u.org_id,
               a.start_time,
               a.finish_time
        from train_learn_map_activity_progress a
            inner join sys_user u on a.user_id = u.id
        <if test="params.execId != null and params.execId != ''">
            inner join w_view_limit_user vu on a.user_id = vu.user_id and vu.view_limit_id = #{params.programmeId}
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            inner join sys_org o on u.org_id = o.id
        </if>
        where a.map_id = #{params.mapId}
          and a.version_id = #{params.versionId}
          and a.activity_id = #{params.activityId}
          and a.start_time is not null
        <if test="params.status != null">
            and a.status = #{params.status}
        </if>
        <if test="params.startTime != null">
            and a.start_time >= #{params.startTime}
        </if>
        <if test="params.endTime != null">
            and #{params.endTime} >= a.start_time
        </if>
        <if test="params.userIds != null and params.userIds.size() > 0">
            <foreach collection="params.userIds" item="item" open="and a.user_id in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            and o.level_path like concat(#{params.levelPath}, '%')
        </if>
        order by a.create_time desc
    </select>

    <select id="getPublishActivityAllCompletionCount" resultType="java.lang.Long" useCache="false">
        select count(1)
        from train_learn_map_activity lma
                 left join train_learn_map_activity_progress lmap on lma.id = lmap.activity_id
        where lma.is_del = 0
          and lmap.status = 1
          and lma.map_id = #{mapId}
          and lma.version_id = #{versionId}
          and lmap.user_id = #{userId}
        <if test="isCompulsory != null">
            and lma.is_compulsory = #{isCompulsory}
        </if>
    </select>

    <select id="selectLearnMapActivityProgressStatListByPage"
      parameterType="com.wunding.learn.train.service.admin.query.LearnMapActivityProgressStatQuery"
      resultType="com.wunding.learn.train.service.admin.dto.LearnMapActivityProgressStatDTO" useCache="false">
        select u.id,
               u.full_name,
               u.login_name,
               u.org_id,
               a.activity_id,
               a.status
          from train_learn_map_activity_progress a
         inner join sys_user u on a.user_id = u.id
         inner join sys_org o on u.org_id = o.id
         where a.map_id = #{params.mapId}
        <foreach collection="params.activityIds" item="item" open="and a.activity_id in (" separator="," close=")">
            #{item}
        </foreach>
        <if test="params.startTime != null">
            and a.start_time >= #{params.startTime}
        </if>
        <if test="params.endTime != null">
            and #{params.endTime} >= a.start_time
        </if>
        <if test="params.status != null">
            and a.status = #{params.status}
        </if>
        <if test="params.userIds != null and params.userIds.size() > 0">
            <foreach collection="params.userIds" item="item" open="and a.user_id in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                    o.level_path like concat(#{item}, '%')
            </foreach>
            or (u.org_id = #{params.currentOrgId} and u.create_by = #{params.currentUserId})
                )
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            and o.level_path like concat(#{params.levelPath}, '%')
        </if>
        order by u.id, a.create_time desc
    </select>

    <select id="selectLearnMapActivityProgressListByPage"
      parameterType="com.wunding.learn.train.service.client.query.LearnMapActivityProgressListQuery"
      resultType="com.wunding.learn.train.service.client.dto.LearnMapActivityProgressListDTO" useCache="false">
        select lmap.id,
               lmap.activity_id,
               lma.activity_name,
               lma.is_compulsory,
               lma.resource_id,
               lma.resource_type,
               lma.phase_id,
               lmp.phase_name,
               ifnull(lmap.status, 0) as status
        from train_learn_map_activity_progress lmap
                 inner join train_learn_map_activity lma on lmap.activity_id = lma.id
                 left join train_learn_map_phase lmp on lma.phase_id = lmp.id and lmp.is_del = 0
        where lmap.map_id = #{params.mapId}
          and lmap.user_id = #{params.currentUserId}
          and lma.version_id = #{params.versionId}
          and lma.is_del = 0
          and lma.is_available = 1
        order by lmp.phase_name is null, lmp.sort_no, lma.sort_no, lma.id
    </select>

    <select id="getProgressDetailList"
      resultType="com.wunding.learn.train.service.client.dto.LearnMapActivityProgressDetailDTO" useCache="false">
        select lmap.id,
               lma.id                    activityId,
               lma.activity_name,
               lma.is_compulsory,
               lma.resource_id,
               lma.resource_type,
               lmp.id                    phaseId,
               lmp.phase_name,
               ifnull(lmap.status, 0) as status
        from train_learn_map_activity_progress lmap
                 inner join train_learn_map_activity lma on lmap.activity_id = lma.id
                 left join train_learn_map_phase lmp on lma.phase_id = lmp.id and lmp.is_del = 0
        where lmap.map_id = #{learnMapId}
          and lmap.user_id = #{userId}
          and lma.is_del = 0
          and lma.version_id = #{versionId}
          and lma.is_available = 1
        <if test="activityName != null and activityName != ''">
        and instr(lma.activity_name, #{activityName}) > 0
    </if>
    </select>

    <select id="getProgressByResourceIdAndUserId"
      resultType="com.wunding.learn.train.service.admin.dto.LearnMapVersionActivityProgress" useCache="false">
        select lmap.id,
               lmap.map_id,
               lmap.activity_id,
               lma.version_id,
               lmap.user_id,
               lmap.status,
               lmap.start_time,
               lmap.finish_time,
               lmap.course_time,
               lmap.test_score
          from train_learn_map_activity_progress lmap
         inner join train_learn_map_activity lma on lmap.activity_id = lma.id
        where lma.is_del = 0
          and lma.resource_id = #{resourceId}
          and lmap.user_id = #{userId}
    </select>

    <select id="getActivityLearnFinishCount" resultType="java.lang.Integer" useCache="false">
        select count(1)
          from (select sum(c.`status`)
                  from (select a.id, a.map_id, a.version_id, a.user_id, a.`status`
                          from train_learn_map_activity_progress a
                         inner join train_user_map_record b on a.map_id=b.map_id and a.user_id = b.user_id
                         inner join train_learn_map c on a.version_id = c.current_version_id
                         where a.is_del=0 and c.is_del=0 and c.is_publish=1
                       <if test="userIdList != null and userIdList.size() > 0">
                           and a.user_id in
                           <foreach collection="userIdList" open="(" close=")" item="userId" separator=",">
                               #{userId}
                           </foreach>
                       </if>
                       ) c
                 group by map_id, version_id, user_id
                having count(c.id) - sum(c.`status`) = 0) t
    </select>
</mapper>
