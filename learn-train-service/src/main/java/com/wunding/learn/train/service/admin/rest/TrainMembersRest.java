package com.wunding.learn.train.service.admin.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.train.service.admin.dto.TrainMemberVO;
import com.wunding.learn.train.service.admin.query.TrainMembersQuery;
import com.wunding.learn.train.service.service.ITrainMembersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Arrays;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>  培训项目学员表 前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">cjn</a>
 * @since 2023-03-13
 */
@RestController
@RequestMapping("${module.train.contentPath:/}trainMembers")
@Tag(description = "成员管理-学员", name = "TrainMembersRest")
public class TrainMembersRest {

    @Resource
    private ITrainMembersService trainMembersService;

    /**
     * 导入培训项目学员
     *
     * @param excelPath 导入的文件地址
     * @param trainId   培训项目id
     * @return
     */
    @GetMapping("/import")
    @Operation(operationId = "importTrainMembers", summary = "导入培训项目学员", description = "导入培训项目学员")
    public Result<ImportResultDTO> importTrainMembers(
        @Parameter(description = "导入的文件地址") @RequestParam("excelPath") String excelPath,
        @Parameter(description = "培训项目id") @RequestParam("trainId") String trainId) {
        ImportResultDTO excel = trainMembersService.importTrainMembers(excelPath, trainId);
        return Result.success(excel);
    }

    /**
     * 删除学员
     *
     * @param ids 要删除的id，多个用','分割
     * @return 删除的数量
     */
    @GetMapping("/delete")
    @Operation(operationId = "deleteTrainMembers", summary = "删除学员", description = "删除学员")
    public Result<Integer> deleteTrainMembers(
        @Parameter(description = "要删除的id,多个用','分割") @RequestParam("ids") String ids) {
        return Result.success(trainMembersService.deleteTrainMembersByIds(ids));
    }

    /**
     * 根据条件查询学员列表
     *
     * @param trainMembersQuery 查询条件封装类
     * @return 学员列表
     */
    @GetMapping("/list")
    @Operation(operationId = "queryTrainMembers", summary = "查询学员列表", description = "查询学员列表")
    public Result<PageInfo<TrainMemberVO>> queryTrainMembers(@Valid TrainMembersQuery trainMembersQuery) {
        if (StringUtils.isNotEmpty(trainMembersQuery.getUserIds())) {
            trainMembersQuery.setUserIdList(Arrays.asList(trainMembersQuery.getUserIds().split(",")));
        }
        PageInfo<TrainMemberVO> pageInfo = trainMembersService.queryTrainMemberList(trainMembersQuery);
        return Result.success(pageInfo);
    }

    /**
     * 审核学员通过或者不通过
     *
     * @param ids  要审核的id,多个用','分割
     * @param type 0-审核通过，1-审核不通过
     * @return 审核的数量
     */
    @GetMapping("/review")
    @Operation(operationId = "reviewTrainMembers", summary = "审核学员", description = "审核学员")
    public Result<Integer> reviewTrainMembers(
        @Parameter(description = "要审核的id,多个用','分割") @RequestParam("ids") String ids,
        @Parameter(description = "0-审核通过，1-审核不通过") @RequestParam("type") int type,
        @Parameter(description = "培训项目id") @RequestParam("trainId") String trainId) {
        return Result.success(trainMembersService.changeTrainMembersReviewStatusByIds(ids, type, trainId));
    }

    /**
     * 导出查询出的学员列表
     *
     * @param trainMembersQuery 查询条件封装类
     * @return
     */
    @GetMapping("/export")
    @Operation(operationId = "exportTrainMembers", summary = "导出学员列表", description = "导出学员列表")
    public Result<Void> exportTrainMembers(@Valid TrainMembersQuery trainMembersQuery) {
        if (StringUtils.isNotEmpty(trainMembersQuery.getUserIds())) {
            trainMembersQuery.setUserIdList(Arrays.asList(trainMembersQuery.getUserIds().split(",")));
        }
        trainMembersService.exportTrainMembers(trainMembersQuery);
        return Result.success();
    }

    /**
     * 统计待审核学员数量
     *
     * @param trainId 培训项目id
     * @return 待审核学员数量
     */
    @GetMapping("/count/toBeReviewed")
    @Operation(operationId = "countToBeReviewedTrainMembers", summary = "统计待审核学员数量", description = "统计待审核学员数量")
    public Result<Long> countToBeReviewedTrainMembers(@Parameter(description = "培训项目id") @RequestParam("trainId") String trainId) {
        return Result.success(trainMembersService.countToBeReviewedTrainMembers(trainId));
    }

}
