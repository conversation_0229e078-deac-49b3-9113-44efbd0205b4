package com.wunding.learn.train.service.client.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.train.service.biz.ILearnMapExecBiz;
import com.wunding.learn.train.service.client.dto.UserIdpLearnMapExecDTO;
import com.wunding.learn.train.service.client.query.UserIdpLearnMapExecQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: GYong
 * @Date: 2022/9/29 20:38
 */
@RestController
@RequestMapping("${module.train.contentPath:/}idp")
@Tag(description = "学习地图执行应用-IDP", name = "IDPLearnMapExecRest")
@Validated
public class IDPLearnMapExecRest {

    @Resource
    private ILearnMapExecBiz learnMapExecBiz;

    @GetMapping("/mineLearnMapExec")
    @Operation(operationId = "getUserIdpLearnMapExecByYear", summary = "获取用户年度idp学习地图执行列表", description = "获取用户年度idp学习地图执行列表")
    public Result<UserIdpLearnMapExecDTO> getUserIdpLearnMapExecByYear(
        @Valid @ParameterObject UserIdpLearnMapExecQuery userIdpLearnMapExecQuery) {
        return Result.success(learnMapExecBiz.getUserIdpLearnMapExecByYear(userIdpLearnMapExecQuery));
    }
}
