package com.wunding.learn.train.service.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wunding.learn.common.viewlimit.model.BaseViewLimit;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 培训项目资料下发范围对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">z<PERSON><PERSON><PERSON><PERSON></a>
 * @date 2023/7/14 14:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("train_files_view_limit")
@Schema(name = "TrainFilesViewLimit", description = "培训项目资料下发范围对象")
public class TrainFilesViewLimit extends BaseViewLimit implements Serializable {

    private static final long serialVersionUID = -9051392085891069160L;
}
