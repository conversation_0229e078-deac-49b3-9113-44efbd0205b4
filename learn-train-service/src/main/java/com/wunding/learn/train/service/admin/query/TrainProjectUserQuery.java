package com.wunding.learn.train.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p> 
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2025/2/14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class TrainProjectUserQuery extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = -640443169084601245L;

    @Parameter(description = "项目ID", required = true)
    @NotBlank(message = "项目id不可为空")
    private String projectId;

    @Parameter(description = "用户id集合(以,分割)")
    private String userIds;

    @Parameter(description = "用户id集合", hidden = true)
    private List<String> userIdsVo;

    @Parameter(description = "组织id")
    private String orgId;

    @Parameter(description = "组织全路径", hidden = true)
    private String levelPath;
}
