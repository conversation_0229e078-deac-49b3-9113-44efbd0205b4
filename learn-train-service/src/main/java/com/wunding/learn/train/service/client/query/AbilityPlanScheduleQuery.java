package com.wunding.learn.train.service.client.query;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * <AUTHOR>
 * @date 2024/5/13 下午8:46
 */
@Data
@Schema(name = "LearnMapAbilityPlanClientQuery", description = "培训计划进度计划查询对象")
public class AbilityPlanScheduleQuery {
    @Schema(description = "类型 0-未执行 1-已执行")
    private Integer type;

    @DateTimeFormat(iso = ISO.DATE_TIME)
    @Schema(description = "截至时间")
    private Date endDate;

    @Schema(description = "地图id列表")
    private List<String> mapIdList;
}
