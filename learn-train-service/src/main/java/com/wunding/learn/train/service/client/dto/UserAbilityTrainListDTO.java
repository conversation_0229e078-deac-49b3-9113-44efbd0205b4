package com.wunding.learn.train.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 用户能力培训列表对象DTO
 *
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Schema(name = "UserAbilityTrainListDTO",description = "用户能力培训列表对象")
public class UserAbilityTrainListDTO {
    /**
     * 项目id
     */
    @Schema(description = "项目id")
    private String id;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 学习项目图片
     */
    @Schema(description = "学习项目图片")
    private String imageUrl;

    /**
     * 固定日期的学习项目的开始日期
     */
    @Schema(description = "固定日期的学习项目的开始日期")
    private Date startTime;

    /**
     * 固定日期的学习项目的结束日期
     */
    @Schema(description = "固定日期的学习项目的结束日期")
    private Date endTime;

    /**
     * 周期项目的天数
     */
    @Schema(description = "周期项目的天数")
    private Integer period;

    /**
     * 项目类型 0:固定日期 1:固定周期
     */
    @Schema(description = "项目类型 0:固定日期 1:固定周期")
    private Integer type;

    /**
     * 是否可以直接进入任务: 0不可以, 1可以
     */
    @Schema(description = "是否可以直接进入任务: 0不可以, 1可以")
    private Integer isOperation;

    @Schema(description = "是否增加激励兑换支持")
    private Integer isExchange;
    /**
     * 完成度百分比
     */
    @Schema(description = "完成度百分比")
    private BigDecimal progress;
}
