package com.wunding.learn.push.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>  推送用户
 *
 * <AUTHOR> href="mailto:<EMAIL>">ydq</a>
 * @since 2022-09-05
 */
@Data
@Schema(name = "PushUserManageDTO", description = "推送用户对象")
public class PushUserManageDTO {

    /**
     * 推送用户id
     */
    @Schema(description = "推送用户id")
    private String userId;

    /**
     * 用户ID
     */
    @Schema(description = "用户全名")
    private String userName;

    /**
     * 推送用户邮箱
     */
    @Schema(description = "推送用户邮箱")
    private String userMail;

}
